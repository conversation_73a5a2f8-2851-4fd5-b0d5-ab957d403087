'use client'

import { Suspense } from 'react'
import Link from 'next/link'
import { AuthForm } from '@/components/auth/auth-form'
import { Button } from '@/components/ui/button'
import { ClientThemeToggle } from '@/components/ui/client-theme-toggle'
import { BookOpen } from 'lucide-react'
import { ArrowLeft } from 'lucide-react'

export function SignupPageClient() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="p-4 flex items-center justify-between">
        <Link href="/" className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </Link>
        <ClientThemeToggle />
      </header>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md space-y-8">
          {/* Logo */}
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-primary rounded-lg flex items-center justify-center mb-4">
              <BookOpen className="w-8 h-8 text-primary-foreground" />
            </div>
            <h1 className="text-2xl font-bold">BookScribe AI</h1>
            <p className="text-muted-foreground">Your AI-powered writing companion</p>
          </div>

          {/* Auth Form */}
          <Suspense fallback={<div>Loading...</div>}>
            <AuthForm mode="signup" />
          </Suspense>

          {/* Footer Links */}
          <div className="text-center text-sm text-muted-foreground">
            <Link href="/privacy" className="hover:underline">Privacy Policy</Link>
            {' • '}
            <Link href="/terms" className="hover:underline">Terms of Service</Link>
          </div>
        </div>
      </div>
    </div>
  )
}