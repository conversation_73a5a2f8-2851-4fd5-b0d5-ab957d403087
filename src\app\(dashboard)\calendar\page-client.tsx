'use client'

import { useState } from 'react'
import { WritingCalendar } from '@/components/analytics/components/writing-calendar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Button } from '@/components/ui/button'
import { CalendarIcon } from 'lucide-react'
import { Download } from 'lucide-react'
import { Target } from 'lucide-react'
import { TrendingUp } from 'lucide-react'
import { format } from 'date-fns'

interface CalendarPageClientProps {
  userId: string
  projects: Array<{ id: string; title: string }>
}

export default function CalendarPageClient({ userId, projects }: CalendarPageClientProps) {
  const [selectedProject, setSelectedProject] = useState<string>('all')
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)

  const handleExportCalendar = async () => {
    try {
      const params = new URLSearchParams({
        format: 'ics',
        ...(selectedProject !== 'all' && { projectId: selectedProject })
      })

      const response = await fetch(`/api/analytics/export?${params}`)
      
      if (!response.ok) {
        throw new Error('Export failed')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `writing-calendar-${format(new Date(), 'yyyy-MM')}.ics`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className="container-wide py-6 sm:py-8 lg:py-10">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Writing Calendar</h1>
        <p className="text-muted-foreground">
          Track your daily writing progress and maintain streaks
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              This Month
            </CardTitle>
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Active</div>
            <p className="text-xs text-muted-foreground">
              Writing consistently
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Monthly Goal
            </CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">75%</div>
            <p className="text-xs text-muted-foreground">
              Of 30,000 words
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Best Streak
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">14 days</div>
            <p className="text-xs text-muted-foreground">
              Personal record
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Selected Date
            </CardTitle>
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {selectedDate ? format(selectedDate, 'MMM d') : 'None'}
            </div>
            <p className="text-xs text-muted-foreground">
              {selectedDate ? format(selectedDate, 'yyyy') : 'Click a date'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6 justify-between">
        <Select value={selectedProject} onValueChange={setSelectedProject}>
          <SelectTrigger className="w-[280px]">
            <SelectValue placeholder="All projects" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All projects</SelectItem>
            {projects.map((project) => (
              <SelectItem key={project.id} value={project.id}>
                {project.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button
          variant="outline"
          onClick={handleExportCalendar}
        >
          <Download className="h-4 w-4 mr-2" />
          Export Calendar
        </Button>
      </div>

      {/* Main Calendar */}
      <WritingCalendar 
        userId={userId}
        projectId={selectedProject === 'all' ? undefined : selectedProject}
        onDateSelect={setSelectedDate}
      />

      {/* Writing Habits */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Building Better Writing Habits</CardTitle>
          <CardDescription>
            Tips for maintaining consistency
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="h-2 w-2 rounded-full bg-primary mt-1.5" />
              <div>
                <p className="font-medium">Start small, stay consistent</p>
                <p className="text-sm text-muted-foreground">
                  Even 100 words a day builds momentum. Focus on showing up rather than word count.
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="h-2 w-2 rounded-full bg-primary mt-1.5" />
              <div>
                <p className="font-medium">Track your progress visually</p>
                <p className="text-sm text-muted-foreground">
                  The calendar helps you see patterns and celebrate consistency.
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="h-2 w-2 rounded-full bg-primary mt-1.5" />
              <div>
                <p className="font-medium">Don't break the chain</p>
                <p className="text-sm text-muted-foreground">
                  Streaks are powerful motivators. Keep your writing streak alive!
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="h-2 w-2 rounded-full bg-primary mt-1.5" />
              <div>
                <p className="font-medium">Review and reflect</p>
                <p className="text-sm text-muted-foreground">
                  Look back at your most productive days to identify what worked.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}