#!/usr/bin/env node
import { promises as fs } from 'fs';
import path from 'path';
import { glob } from 'glob';

interface FileRefactoring {
  filePath: string;
  imports: string[];
  supabaseUsages: string[];
  suggestedService: string;
  refactoringPriority: 'high' | 'medium' | 'low';
}

interface ServiceMapping {
  pattern: RegExp;
  service: string;
  importStatement: string;
}

const SERVICE_MAPPINGS: ServiceMapping[] = [
  {
    pattern: /projects\//,
    service: 'ProjectService',
    importStatement: "import { ServiceManager } from '@/lib/services/service-manager'"
  },
  {
    pattern: /chapters\//,
    service: 'ChapterService',
    importStatement: "import { ServiceManager } from '@/lib/services/service-manager'"
  },
  {
    pattern: /characters\//,
    service: 'CharacterService',
    importStatement: "import { ServiceManager } from '@/lib/services/service-manager'"
  },
  {
    pattern: /analytics\//,
    service: 'WritingAnalyticsService',
    importStatement: "import { ServiceManager } from '@/lib/services/service-manager'"
  },
  {
    pattern: /story-bible\//,
    service: 'StoryBibleService',
    importStatement: "import { ServiceManager } from '@/lib/services/service-manager'"
  },
  {
    pattern: /collaboration\//,
    service: 'CollaborationService',
    importStatement: "import { ServiceManager } from '@/lib/services/service-manager'"
  },
  {
    pattern: /series\//,
    service: 'SeriesService',
    importStatement: "import { ServiceManager } from '@/lib/services/service-manager'"
  },
  {
    pattern: /user\//,
    service: 'UserService',
    importStatement: "import { ServiceManager } from '@/lib/services/service-manager'"
  },
  {
    pattern: /billing\//,
    service: 'BillingService',
    importStatement: "import { ServiceManager } from '@/lib/services/service-manager'"
  }
];

async function findDirectSupabaseImports(): Promise<FileRefactoring[]> {
  const apiDir = path.join(process.cwd(), 'src', 'app', 'api');
  const files = await glob('**/*.{ts,tsx}', { cwd: apiDir });
  
  const results: FileRefactoring[] = [];
  
  for (const file of files) {
    const filePath = path.join(apiDir, file);
    const content = await fs.readFile(filePath, 'utf-8');
    
    // Check for direct Supabase imports
    const supabaseImportRegex = /import.*from\s+['"]@\/lib\/supabase[^'"]*['"]/g;
    const imports = content.match(supabaseImportRegex) || [];
    
    if (imports.length > 0) {
      // Find Supabase usage patterns
      const supabaseUsages: string[] = [];
      
      // Common patterns
      const patterns = [
        /supabase\s*\.\s*from\s*\(['"](\w+)['"]\)/g,
        /createTypedServerClient/g,
        /createServerSupabaseClient/g,
        /supabase\s*\.\s*rpc/g,
        /supabase\s*\.\s*storage/g,
      ];
      
      patterns.forEach(pattern => {
        const matches = content.match(pattern);
        if (matches) {
          supabaseUsages.push(...matches);
        }
      });
      
      // Determine service and priority
      const suggestedService = determineService(file, supabaseUsages, content);
      const priority = determinePriority(file, suggestedService);
      
      results.push({
        filePath: file,
        imports,
        supabaseUsages,
        suggestedService,
        refactoringPriority: priority
      });
    }
  }
  
  return results;
}

function determineService(filePath: string, usages: string[], content: string): string {
  // Check file path patterns
  for (const mapping of SERVICE_MAPPINGS) {
    if (mapping.pattern.test(filePath)) {
      return mapping.service;
    }
  }
  
  // Check table usage in content
  const tablePatterns: Record<string, string> = {
    'projects': 'ProjectService',
    'chapters': 'ChapterService',
    'characters': 'CharacterService',
    'writing_sessions': 'WritingAnalyticsService',
    'story_bible': 'StoryBibleService',
    'collaboration_sessions': 'CollaborationService',
    'series': 'SeriesService',
    'profiles': 'UserService',
    'user_subscriptions': 'BillingService'
  };
  
  for (const [table, service] of Object.entries(tablePatterns)) {
    if (content.includes(`from('${table}')`)) {
      return service;
    }
  }
  
  return 'Unknown - needs analysis';
}

function determinePriority(filePath: string, service: string): 'high' | 'medium' | 'low' {
  // High priority: Core functionality
  if (filePath.includes('/projects/') || 
      filePath.includes('/chapters/') || 
      filePath.includes('/characters/') ||
      filePath.includes('/agents/')) {
    return 'high';
  }
  
  // Medium priority: Analytics and features
  if (filePath.includes('/analytics/') || 
      filePath.includes('/collaboration/') ||
      filePath.includes('/story-bible/')) {
    return 'medium';
  }
  
  // Low priority: Admin and settings
  return 'low';
}

async function generateRefactoringPlan(refactorings: FileRefactoring[]): Promise<void> {
  console.log('\n=== Supabase Direct Import Refactoring Plan ===\n');
  console.log(`Total files to refactor: ${refactorings.length}\n`);
  
  // Group by priority
  const byPriority = refactorings.reduce((acc, item) => {
    if (!acc[item.refactoringPriority]) acc[item.refactoringPriority] = [];
    acc[item.refactoringPriority].push(item);
    return acc;
  }, {} as Record<string, FileRefactoring[]>);
  
  // Display by priority
  ['high', 'medium', 'low'].forEach(priority => {
    const files = byPriority[priority] || [];
    if (files.length > 0) {
      console.log(`\n${priority.toUpperCase()} PRIORITY (${files.length} files):`);
      console.log('='.repeat(40));
      
      // Group by service
      const byService = files.reduce((acc, item) => {
        if (!acc[item.suggestedService]) acc[item.suggestedService] = [];
        acc[item.suggestedService].push(item);
        return acc;
      }, {} as Record<string, FileRefactoring[]>);
      
      Object.entries(byService).forEach(([service, serviceFiles]) => {
        console.log(`\n${service} (${serviceFiles.length} files):`);
        serviceFiles.slice(0, 3).forEach(file => {
          console.log(`  - ${file.filePath}`);
          if (file.supabaseUsages.length > 0) {
            console.log(`    Usage: ${file.supabaseUsages[0]}`);
          }
        });
        if (serviceFiles.length > 3) {
          console.log(`  ... and ${serviceFiles.length - 3} more`);
        }
      });
    }
  });
  
  // Save detailed plan
  const planPath = path.join(process.cwd(), 'supabase-refactoring-plan.json');
  await fs.writeFile(planPath, JSON.stringify(refactorings, null, 2));
  console.log(`\n\nDetailed plan saved to: ${planPath}`);
}

async function generateBatchRefactorScript(refactorings: FileRefactoring[]): Promise<void> {
  const scriptLines: string[] = [
    '#!/bin/bash',
    '# Auto-generated script to refactor Supabase imports',
    '# This script provides examples of the refactoring needed',
    '',
    '# High Priority Files'
  ];
  
  const highPriority = refactorings.filter(r => r.refactoringPriority === 'high');
  
  highPriority.slice(0, 5).forEach(file => {
    scriptLines.push(`\n# File: ${file.filePath}`);
    scriptLines.push(`# Service: ${file.suggestedService}`);
    scriptLines.push('# Steps:');
    scriptLines.push('# 1. Remove direct Supabase import');
    scriptLines.push('# 2. Add ServiceManager import');
    scriptLines.push('# 3. Get service instance: const serviceManager = ServiceManager.getInstance();');
    scriptLines.push(`# 4. Get specific service: const service = await serviceManager.get${file.suggestedService}();`);
    scriptLines.push('# 5. Replace Supabase calls with service methods');
  });
  
  const scriptPath = path.join(process.cwd(), 'refactor-supabase-imports.sh');
  await fs.writeFile(scriptPath, scriptLines.join('\n'));
  console.log(`\nRefactoring script template saved to: ${scriptPath}`);
}

// Main execution
async function main() {
  try {
    console.log('Analyzing API routes for direct Supabase imports...');
    const refactorings = await findDirectSupabaseImports();
    
    if (refactorings.length === 0) {
      console.log('No direct Supabase imports found!');
      return;
    }
    
    await generateRefactoringPlan(refactorings);
    await generateBatchRefactorScript(refactorings);
    
    console.log('\n\n=== Next Steps ===');
    console.log('1. Review the refactoring plan');
    console.log('2. Ensure all required service methods exist');
    console.log('3. Start with HIGH priority files');
    console.log('4. Test each refactored route thoroughly');
    console.log('5. Consider using the Task agent for batch refactoring');
    
  } catch (error) {
    console.error('Error analyzing imports:', error);
    process.exit(1);
  }
}

main();