"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { BarChart3 } from 'lucide-react'
import { Target } from 'lucide-react'
import { TrendingUp } from 'lucide-react'
import { Clock } from 'lucide-react'
import { BookOpen } from 'lucide-react'
import { Award } from 'lucide-react'
import { Zap } from 'lucide-react'
import { Users } from 'lucide-react'
import { CheckCircle } from 'lucide-react'
import { Calendar } from 'lucide-react'
import { PieChart } from 'lucide-react'
import { Activity } from 'lucide-react'
import { Brain } from 'lucide-react'
import { Eye } from 'lucide-react'
import { <PERSON>bulb } from 'lucide-react'
import { Star } from 'lucide-react'
import { <PERSON><PERSON> } from 'lucide-react'
import { FileText } from 'lucide-react'
import { Sparkles } from 'lucide-react';

const writingStats = {
  totalWords: 47832,
  todayWords: 1634,
  weeklyGoal: 10000,
  weeklyProgress: 7245,
  averageDaily: 1035,
  writingStreak: 12,
  chaptersCompleted: 12,
  totalChapters: 18,
  estimatedCompletion: "March 15, 2025"
};

const performanceMetrics = [
  { label: "Words per Hour", value: 485, trend: "+12%", icon: Zap, color: "text-yellow-500" },
  { label: "Daily Consistency", value: "94%", trend: "+5%", icon: Target, color: "text-green-500" },
  { label: "Quality Score", value: "8.7/10", trend: "+0.3", icon: Star, color: "text-blue-500" },
  { label: "Focus Time", value: "3.2h", trend: "+15min", icon: Brain, color: "text-purple-500" }
];

const recentSessions = [
  { date: "Today", duration: "2h 15m", words: 1634, quality: 9.1, focus: "High" },
  { date: "Yesterday", duration: "1h 45m", words: 1247, quality: 8.8, focus: "Medium" },
  { date: "2 days ago", duration: "3h 20m", words: 2156, quality: 9.3, focus: "High" },
  { date: "3 days ago", duration: "1h 30m", words: 892, quality: 8.5, focus: "Medium" },
  { date: "4 days ago", duration: "2h 45m", words: 1789, quality: 9.0, focus: "High" }
];

const insights = [
  {
    type: "productivity",
    title: "Peak Writing Hours",
    description: "You write 34% more words between 9-11 AM",
    icon: Clock,
    color: "blue",
    actionable: true,
    suggestion: "Schedule your most challenging scenes during peak hours"
  },
  {
    type: "quality",
    title: "Dialogue Strength",
    description: "Your dialogue scenes score 15% higher than average",
    icon: Users,
    color: "green",
    actionable: false,
    suggestion: "Consider expanding dialogue in action scenes"
  },
  {
    type: "pacing",
    title: "Chapter Length Variance",
    description: "Consider varying chapter lengths for better rhythm",
    icon: BarChart3,
    color: "amber",
    actionable: true,
    suggestion: "Aim for 2000-4000 word range with strategic shorter chapters"
  },
  {
    type: "consistency",
    title: "Character Voice",
    description: "Aria's voice consistency: 94% across all chapters",
    icon: Award,
    color: "purple",
    actionable: false,
    suggestion: "Excellent consistency - maintain current approach"
  }
];

const goals = [
  { name: "Complete Chapter 13", progress: 75, target: "This Week", priority: "High" },
  { name: "Reach 50,000 Words", progress: 96, target: "This Month", priority: "Medium" },
  { name: "Finish First Draft", progress: 67, target: "March 2025", priority: "High" },
  { name: "Character Arc Review", progress: 40, target: "Next Week", priority: "Medium" }
];

const chapterAnalysis = [
  { chapter: "Chapter 8", words: 3245, readability: 8.9, pacing: "Good", tension: "High" },
  { chapter: "Chapter 9", words: 2876, readability: 9.2, pacing: "Excellent", tension: "Medium" },
  { chapter: "Chapter 10", words: 4123, readability: 8.7, pacing: "Good", tension: "High" },
  { chapter: "Chapter 11", words: 3567, readability: 9.0, pacing: "Excellent", tension: "Very High" },
  { chapter: "Chapter 12", words: 3821, readability: 9.1, pacing: "Good", tension: "High" }
];

export function DemoAnalyticsEnhanced() {
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedMetric, setSelectedMetric] = useState<string | null>(null);

  const weeklyProgressPercentage = (writingStats.weeklyProgress / writingStats.weeklyGoal) * 100;
  const bookProgressPercentage = (writingStats.chaptersCompleted / writingStats.totalChapters) * 100;

  return (
    <div className="w-full h-full bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card/50 backdrop-blur-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
            <div className="flex items-center gap-2">
              <BarChart3 className="w-6 h-6 text-primary" />
              <h2 className="text-2xl font-bold">Writing Analytics & Progress</h2>
              <Badge variant="outline" className="border-primary/50 text-primary">
                Demo Data
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Calendar className="w-4 h-4 mr-2" />
              Last 30 Days
            </Button>
            <Button variant="outline" size="sm">
              <FileText className="w-4 h-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-6 grid grid-cols-2 md:grid-cols-5 gap-4 sm:gap-5 lg:gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{writingStats.totalWords.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Total Words</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-500">{writingStats.todayWords}</div>
            <div className="text-sm text-muted-foreground">Today</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-500">{writingStats.averageDaily}</div>
            <div className="text-sm text-muted-foreground">Daily Average</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-500">{writingStats.writingStreak}</div>
            <div className="text-sm text-muted-foreground">Day Streak</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-500">{Math.round(bookProgressPercentage)}%</div>
            <div className="text-sm text-muted-foreground">Book Complete</div>
          </div>
        </div>
      </div>

      <div className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full max-w-2xl lg:max-w-3xl xl:max-w-4xl grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
            <TabsTrigger value="chapters">Chapters</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Progress Cards */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="w-5 h-5" />
                      Weekly Progress
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        {writingStats.weeklyProgress.toLocaleString()} / {writingStats.weeklyGoal.toLocaleString()} words
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {Math.round(weeklyProgressPercentage)}% complete
                      </span>
                    </div>
                    <Progress value={weeklyProgressPercentage} className="h-3" />
                    <p className="text-sm text-muted-foreground">
                      {writingStats.weeklyGoal - writingStats.weeklyProgress} words remaining to reach weekly goal
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="w-5 h-5" />
                      Book Progress
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        Chapter {writingStats.chaptersCompleted} of {writingStats.totalChapters}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {Math.round(bookProgressPercentage)}% complete
                      </span>
                    </div>
                    <Progress value={bookProgressPercentage} className="h-3" />
                    <p className="text-sm text-muted-foreground">
                      Estimated completion: {writingStats.estimatedCompletion}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="w-5 h-5" />
                      Recent Writing Sessions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {recentSessions.map((session, idx) => (
                        <div key={idx} className="flex items-center justify-between p-3 rounded border">
                          <div className="flex items-center gap-3">
                            <div className="text-sm font-medium">{session.date}</div>
                            <Badge variant="outline" className="text-xs">
                              {session.duration}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-4 sm:gap-5 lg:gap-6 text-sm">
                            <div className="text-right">
                              <div className="font-medium">{session.words} words</div>
                              <div className="text-xs text-muted-foreground">Quality: {session.quality}/10</div>
                            </div>
                            <Badge variant={
                              session.focus === 'High' ? 'default' :
                              session.focus === 'Medium' ? 'secondary' : 'outline'
                            }>
                              {session.focus} Focus
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Goals Sidebar */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="w-5 h-5" />
                      Current Goals
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {goals.map((goal, idx) => (
                      <div key={idx} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{goal.name}</span>
                          <Badge variant={goal.priority === 'High' ? 'destructive' : 'secondary'} className="text-xs">
                            {goal.priority}
                          </Badge>
                        </div>
                        <Progress value={goal.progress} className="h-2" />
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>{goal.progress}% complete</span>
                          <span>{goal.target}</span>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Award className="w-5 h-5" />
                      Achievements
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-3 p-2 rounded bg-primary/10">
                      <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
                        <Zap className="w-4 h-4 text-primary" />
                      </div>
                      <div>
                        <div className="text-sm font-medium">Speed Writer</div>
                        <div className="text-xs text-muted-foreground">1000+ words in one session</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-2 rounded bg-green-500/10">
                      <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center">
                        <Target className="w-4 h-4 text-green-500" />
                      </div>
                      <div>
                        <div className="text-sm font-medium">Consistent Writer</div>
                        <div className="text-xs text-muted-foreground">12-day writing streak</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-2 rounded bg-blue-500/10">
                      <div className="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center">
                        <BookOpen className="w-4 h-4 text-blue-500" />
                      </div>
                      <div>
                        <div className="text-sm font-medium">Chapter Master</div>
                        <div className="text-xs text-muted-foreground">Completed 12 chapters</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {performanceMetrics.map((metric, idx) => (
                <Card 
                  key={idx}
                  className={`cursor-pointer transition-all duration-300 hover:scale-105 ${
                    selectedMetric === metric.label ? 'border-primary/50 bg-primary/10' : 'hover:border-primary/30'
                  }`}
                  onClick={() => setSelectedMetric(selectedMetric === metric.label ? null : metric.label)}
                >
                  <CardContent className="p-6 text-center">
                    <metric.icon className={`w-8 h-8 mx-auto mb-3 ${metric.color}`} />
                    <div className="text-2xl font-bold mb-1">{metric.value}</div>
                    <div className="text-sm text-muted-foreground mb-2">{metric.label}</div>
                    <Badge variant="outline" className="text-xs">
                      {metric.trend}
                    </Badge>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Writing Velocity Trend
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center border-2 border-dashed border-border rounded">
                    <div className="text-center text-muted-foreground">
                      <BarChart3 className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>Interactive chart showing writing speed over time</p>
                      <p className="text-sm">Demo visualization</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChart className="w-5 h-5" />
                    Time Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Writing</span>
                      <div className="flex items-center gap-2">
                        <Progress value={65} className="w-20 h-2" />
                        <span className="text-sm text-muted-foreground">65%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Planning</span>
                      <div className="flex items-center gap-2">
                        <Progress value={20} className="w-20 h-2" />
                        <span className="text-sm text-muted-foreground">20%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Editing</span>
                      <div className="flex items-center gap-2">
                        <Progress value={15} className="w-20 h-2" />
                        <span className="text-sm text-muted-foreground">15%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="insights" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {insights.map((insight, idx) => (
                <Card key={idx} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg bg-${insight.color}-500/20`}>
                        <insight.icon className={`w-5 h-5 text-${insight.color}-500`} />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{insight.title}</CardTitle>
                        <Badge variant={insight.actionable ? 'default' : 'secondary'} className="text-xs">
                          {insight.actionable ? 'Actionable' : 'Informational'}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <p className="text-sm text-muted-foreground">{insight.description}</p>
                    <div className="p-3 rounded bg-accent">
                      <div className="flex items-start gap-2">
                        <Lightbulb className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                        <p className="text-sm">{insight.suggestion}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="chapters" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="w-5 h-5" />
                  Chapter Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {chapterAnalysis.map((chapter, idx) => (
                    <div key={idx} className="p-4 rounded border">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium">{chapter.chapter}</h4>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{chapter.words} words</Badge>
                          <Badge variant={
                            chapter.tension === 'Very High' ? 'destructive' :
                            chapter.tension === 'High' ? 'default' :
                            chapter.tension === 'Medium' ? 'secondary' : 'outline'
                          }>
                            {chapter.tension} Tension
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-5 lg:gap-6 text-sm">
                        <div>
                          <span className="font-medium">Readability:</span>
                          <div className="flex items-center gap-2 mt-1">
                            <Progress value={chapter.readability * 10} className="flex-1 h-2" />
                            <span className="text-muted-foreground">{chapter.readability}/10</span>
                          </div>
                        </div>
                        
                        <div>
                          <span className="font-medium">Pacing:</span>
                          <div className="mt-1">
                            <Badge variant={chapter.pacing === 'Excellent' ? 'default' : 'secondary'}>
                              {chapter.pacing}
                            </Badge>
                          </div>
                        </div>
                        
                        <div>
                          <span className="font-medium">Word Count:</span>
                          <div className="mt-1 text-muted-foreground">
                            {chapter.words.toLocaleString()} words
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
