[{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\voice-profiles\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": [], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\version-history\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('chapters')", "supabase\r\n      .from('chapter_versions')", "supabase\r\n      .from('chapters')", "supabase\r\n      .from('chapter_versions')", "supabase\r\n      .from('chapter_versions')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\universes\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('universes')", "supabase\r\n        .from('universes')", "supabase\r\n      .from('universes')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('series')", "supabase\r\n      .from('series')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\sample-projects\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase.from('story_arcs')", "supabase.from('characters')", "supabase.from('chapters')", "supabase.from('story_bible')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\references\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n      .from('reference_materials')", "supabase\r\n      .from('projects')", "supabase\r\n      .from('reference_materials')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\project-collaborators\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n          .from('projects')", "supabase\r\n            .from('project_collaborators')", "supabase\r\n        .from('users')", "supabase\r\n        .from('project_collaborators')", "supabase\r\n        .from('projects')", "supabase\r\n        .from('project_collaborators')", "supabase\r\n        .from('users')", "supabase\r\n        .from('projects')", "supabase\r\n          .from('project_collaborators')", "supabase\r\n      .from('projects')", "supabase\r\n      .from('project_collaborators')", "supabase\r\n          .from('project_collaborators')", "supabase\r\n      .from('users')", "supabase\r\n      .from('project_collaborators')", "supabase\r\n        .from('notifications')", "supabase\r\n        .from('project_collaborators')", "supabase\r\n          .from('project_collaborators')", "supabase\r\n      .from('project_collaborators')", "supabase\r\n        .from('user_presence')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\profiles\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('selection_profiles')", "supabase\r\n      .from('selection_profiles')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\processing-tasks\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('processing_tasks')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\notifications\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('notifications')", "supabase\r\n      .from('notifications')", "supabase\r\n      .from('notifications')", "supabase\r\n        .from('notifications')", "supabase\r\n        .from('notifications')", "supabase\r\n      .from('notifications')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\health\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('profiles')", "supabase\r\n      .from('projects')", "createTypedServerClient()"], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\goals\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('writing_goals')", "supabase\r\n          .from('writing_goal_progress')", "supabase\r\n        .from('writing_goals')", "supabase\r\n        .from('writing_goals')", "supabase\r\n      .from('writing_goals')", "supabase\r\n      .from('writing_goals')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\content-analysis\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n      .from('story_bibles')", "supabase.from('content_analysis_logs')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\achievements\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('user_achievements')", "supabase\r\n      .from('achievements')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\writing\\sessions\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('writing_sessions')", "supabase\r\n      .from('writing_sessions')", "supabase\r\n      .from('projects')", "supabase\r\n      .from('writing_sessions')", "supabase\r\n      .from('projects')", "supabase\r\n      .from('writing_sessions')", "supabase\r\n      .from('writing_sessions')", "supabase\r\n        .from('projects')", "supabase.rpc(", "supabase.rpc(", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "WritingAnalyticsService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\writing\\goals\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('writing_goals')", "supabase\r\n        .from('writing_goal_progress')", "supabase\r\n        .from('writing_goals')", "supabase\r\n      .from('writing_goals')", "supabase\r\n        .from('writing_goal_progress')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\webhooks\\stripe\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n    .from('user_subscriptions')", "supabase\r\n    .from('user_subscriptions')", "supabase\r\n    .from('user_subscriptions')", "supabase\r\n    .from('user_subscriptions')", "supabase\r\n    .from('user_subscriptions')", "supabase\r\n    .from('users')", "supabase\r\n    .from('user_subscriptions')", "supabase\r\n    .from('user_subscriptions')", "supabase\r\n      .from('user_subscriptions')", "supabase\r\n      .from('users')", "supabase\r\n        .from('project_collaborators')", "supabase\r\n    .from('subscription_usage')", "supabase\r\n    .from('subscription_usage')", "createTypedServerClient()"], "suggestedService": "BillingService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\user\\record-consent\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('user_consent_history')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\user\\privacy-settings\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('user_privacy_settings')", "supabase\r\n      .from('user_privacy_settings')", "supabase\r\n      .from('user_preferences')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\user\\export-data\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\n      .from('user_data_requests')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\user\\cookie-consent\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n        .from('user_consent_history')", "supabase\r\n          .from('user_privacy_settings')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\user\\consent-history\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('user_consent_history')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\voice-profiles\\[id]\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('voice_profiles')", "supabase\r\n      .from('voice_profiles')", "supabase\r\n      .from('voice_profiles')", "supabase\r\n      .from('voice_profiles')", "supabase\r\n      .from('voice_profiles')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\version-history\\[id]\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('chapter_versions')", "supabase\r\n      .from('chapter_versions')", "supabase\r\n      .from('chapter_versions')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\universes\\[id]\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\n      .from('universes')", "supabase\n      .from('universes')", "supabase\n      .from('universes')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\universes\\timeline-events\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\n      .from('universes')", "supabase\n      .from('universe_timeline_events')", "supabase\n      .from('universes')", "supabase\n      .from('universe_timeline_events')", "supabase\n      .from('universe_timeline_events')", "supabase\n      .from('universes')", "supabase\n      .from('universe_timeline_events')", "supabase\n      .from('universe_timeline_events')", "supabase\n      .from('universes')", "supabase\n      .from('universe_timeline_events')", "supabase\n    .from('series')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\timeline\\events\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('story_bible')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\[id]\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('series')", "supabase\r\n      .from('series')", "supabase\r\n      .from('series')", "supabase\r\n      .from('series')", "supabase\r\n      .from('series')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\search\\semantic\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n        .from('project_collaborators')", "supabase\r\n      .from('content_index')", "supabase.from('search_analytics')", "supabase\r\n      .from('projects')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\search\\index\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n        .from('project_collaborators')", "supabase\r\n      .from('projects')", "supabase\r\n        .from('project_collaborators')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\search\\content\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n        .from('project_collaborators')", "supabase\r\n        .from('chapters')", "supabase\r\n        .from('characters')", "supabase\r\n        .from('locations')", "supabase\r\n        .from('story_bible')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\references\\[id]\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('reference_materials')", "supabase\r\n      .from('reference_materials')", "supabase\r\n      .from('reference_materials')", "supabase\r\n      .from('reference_materials')", "supabase\r\n      .from('reference_materials')", "supabase.storage\r\n            .", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\references\\upload\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n      .from('reference_materials')", "supabase\r\n          .from('reference_materials')", "supabase.storage\r\n      .", "supabase.storage\r\n      .", "supabase.storage\r\n        .", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\projects\\[id]\\route.ts", "imports": ["import { createServerSupabaseClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\n      .from('projects')", "supabase\n      .from('projects')", "supabase\n      .from('projects')"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\projects\\invite\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\n        .from('users')", "supabase\n        .from('projects')", "supabase\n          .from('project_collaborators')", "supabase\n      .from('projects')", "supabase\n      .from('user_subscriptions')", "supabase\n      .from('project_collaborators')", "supabase\n      .from('users')", "supabase\n        .from('project_collaborators')", "supabase\n      .from('project_invitations')", "supabase\n      .from('project_invitations')", "supabase\n      .from('users')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "BillingService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\projects\\grouped\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\project-collaborators\\[id]\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('project_collaborators')", "supabase\r\n      .from('projects')", "supabase\r\n      .from('project_collaborators')", "supabase\r\n      .from('project_collaborators')", "supabase\r\n        .from('notifications')", "supabase\r\n        .from('notifications')", "supabase\r\n      .from('project_collaborators')", "supabase\r\n      .from('project_collaborators')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\profiles\\public\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('selection_profiles')", "supabase\r\n      .from('selection_profiles')", "createTypedServerClient()"], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\profiles\\user\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('selection_profiles')", "createTypedServerClient()"], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\processing-tasks\\[id]\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('processing_tasks')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\orchestration\\start\\route.ts", "imports": ["import { createServerSupabaseClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n            .from('projects')"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\notifications\\[id]\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('notifications')", "supabase\r\n      .from('notifications')", "supabase\r\n      .from('notifications')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\notifications\\mark-all-read\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('notifications')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\memory\\usage-history\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n      .from('ai_generations')", "supabase\r\n      .from('memory_optimization_logs')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\memory\\stats\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\memory\\settings\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n      .from('memory_settings')", "supabase\r\n      .from('projects')", "supabase\r\n      .from('memory_settings')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\memory\\merge\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\memory\\compress\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\import\\txt\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n          .from('chapters')", "supabase\r\n      .from('projects')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\import\\pdf\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n          .from('chapters')", "supabase\r\n      .from('projects')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\import\\epub\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n            .from('chapters')", "supabase\r\n        .from('projects')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\import\\docx\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n          .from('chapters')", "supabase\r\n      .from('projects')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\goals\\recommendations\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n    .from('writing_sessions')", "supabase\r\n    .from('chapters')", "supabase\r\n      .from('projects')", "supabase\r\n    .from('writing_goals')", "createTypedServerClient()"], "suggestedService": "WritingAnalyticsService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\goals\\progress\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('writing_goals')", "supabase\r\n      .from('writing_goal_progress')", "supabase.rpc(", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\email\\send\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n        .from('profiles')", "supabase\r\n        .from('profiles')", "createTypedServerClient()"], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\email\\preferences\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\consistency\\check\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n      .from('consistency_reports')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\collaboration\\sessions\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('collaboration_sessions')", "supabase\r\n        .from('projects')", "supabase\r\n          .from('project_collaborators')", "supabase\r\n        .from('collaboration_sessions')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\collaboration\\presence\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n        .from('project_collaborators')", "supabase\r\n          .from('projects')", "supabase\r\n        .from('project_collaborators')", "supabase\r\n          .from('projects')", "supabase\r\n          .from('chapters')", "supabase\r\n      .from('user_presence')", "supabase\r\n      .rpc(", "supabase\r\n      .rpc(", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\chapters\\restore-version\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('chapter_versions')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\cron\\process-reference-materials\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('reference_materials')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\cron\\generate-embeddings\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('chapters')", "supabase\r\n          .from('chapters')", "supabase\r\n      .from('story_bible')", "supabase\r\n              .from('story_bible')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\billing\\subscriptions\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('user_subscriptions')", "supabase\r\n      .from('usage_tracking')", "supabase\r\n        .from('profiles')", "supabase\r\n          .from('profiles')", "supabase\r\n        .from('user_subscriptions')", "supabase\r\n        .from('user_subscriptions')", "supabase\r\n        .from('user_subscriptions')", "supabase\r\n        .from('user_subscriptions')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "BillingService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\billing\\history\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('profiles')", "createTypedServerClient()"], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analysis\\voice-consistency\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('voice_consistency_checks')", "supabase\r\n      .from('voice_profiles')", "supabase\r\n        .from('voice_consistency_checks')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analysis\\progress\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('readability_progress')", "supabase\r\n      .from('readability_progress')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analysis\\character-insights\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('characters')", "supabase\r\n      .from('character_insights_cache')", "supabase.from('characters')", "supabase.from('chapters')", "supabase.from('character_insights_cache')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analysis\\character-development-grid\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n      .from('characters')", "supabase\r\n      .from('chapters')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analysis\\character-development\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('characters')", "supabase\r\n      .from('chapters')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analysis\\character-arc\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('characters')", "supabase\r\n      .from('characters')", "supabase\r\n      .from('chapters')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analysis\\character-arc-patterns\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n      .from('characters')", "supabase\r\n      .from('chapters')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analysis\\book-summary\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n      .from('chapters')", "supabase\r\n      .from('story_bible')", "supabase\r\n          .from('book_summaries')", "supabase\r\n          .from('book_summaries')", "supabase\r\n        .from('story_bible')", "supabase\r\n        .from('story_bible')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analysis\\arc-predictions\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n        .from('characters')", "supabase\r\n        .from('chapters')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analysis\\arc-suggestions\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n        .from('characters')", "supabase\r\n        .from('chapters')", "supabase\r\n        .from('projects')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\ai\\typed-stream\\route.ts", "imports": ["import { createServerSupabaseClient } from '@/lib/supabase'"], "supabaseUsage": [], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\ai\\suggestions\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n          .from('projects')", "supabase\r\n      .from('ai_suggestions')", "supabase\r\n          .from('projects')", "supabase\r\n          .from('chapters')", "supabase\r\n      .from('ai_suggestions')", "supabase\r\n        .from('ai_suggestions')", "supabase\r\n      .from('ai_suggestions')", "supabase\r\n        .from('ai_suggestions')", "supabase\r\n      .from('ai_suggestions')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\ai\\story-bible-assistant\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n            .from('characters')", "supabase\r\n            .from('story_bible')", "supabase\r\n            .from('story_bible')", "supabase\r\n            .from('story_bible')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\ai\\stream-content\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\agents\\suggestions\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\agents\\edit\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase.from('editing_sessions')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\agents\\adjust-plan\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase.from('projects')", "supabase.from('chapters')", "supabase.from('chapters')", "supabase.from('characters')", "supabase.from('story_bible')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\agents\\chat\\route.ts", "imports": ["import { createServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\n        .from('projects')", "supabase\n        .from('chapters')", "createServerClient("], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\achievements\\stats\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('achievements')", "supabase\r\n      .from('user_achievements')", "supabase\r\n      .from('user_achievements')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\achievements\\check\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n        .from('achievements')", "supabase\r\n        .from('user_achievements')", "supabase\r\n      .rpc(", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\writing\\goals\\[id]\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('writing_goals')", "supabase\r\n      .from('writing_goal_progress')", "supabase\r\n        .from('writing_goals')", "supabase\r\n          .from('writing_goals')", "supabase\r\n      .from('writing_goals')", "supabase\r\n      .from('writing_goals')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\writing\\goals\\stats\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('writing_sessions')", "supabase\r\n      .from('writing_sessions')", "supabase\r\n      .from('writing_sessions')", "supabase\r\n        .from('writing_sessions')", "supabase\r\n      .from('writing_goals')", "supabase\r\n      .from('writing_goals')", "createTypedServerClient()"], "suggestedService": "WritingAnalyticsService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\writing\\goals\\progress\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('writing_goals')", "supabase\r\n      .from('writing_sessions')", "supabase\r\n        .from('writing_goal_progress')", "createTypedServerClient()"], "suggestedService": "WritingAnalyticsService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\voice-profiles\\[id]\\train-from-content\\route.ts", "imports": ["import { createServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\n      .from('voice_profiles')", "supabase\n          .from('chapters')", "supabase\n          .from('chapters')", "supabase\n          .from('story_bible')", "supabase\n          .from('knowledge_items')", "supabase\n      .from('voice_profiles')", "createServerClient("], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\voice-profiles\\[id]\\train\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('voice_profiles')", "supabase\r\n      .from('voice_profiles')", "createTypedServerClient()"], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\[id]\\word-counts\\route.ts", "imports": ["import { createClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\n      .from('series_books')", "supabase\n      .from('word_count_history')"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\[id]\\universe-rules\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\[id]\\universe\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('book_series')", "supabase\r\n      .from('book_series')", "supabase\r\n          .from('book_series')", "supabase\r\n      .from('book_series')", "supabase\r\n        .from('selection_analytics')", "supabase\r\n      .from('book_series')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\[id]\\continuity-issues\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\[id]\\continuity\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('book_series')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\[id]\\characters\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n        .from('character_shares')", "supabase\r\n        .from('character_shares')", "supabase\r\n      .rpc(", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\[id]\\character-continuity\\route.ts", "imports": ["import { createServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\n      .from('series')", "supabase\n      .from('series_books')", "supabase\n      .from('series')", "supabase\n      .from('series_character_continuity')", "createServerClient(", "createServerClient(", "createServerClient("], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\[id]\\apply-voice-profile\\route.ts", "imports": ["import { createServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\n      .from('series')", "supabase\n      .from('voice_profiles')", "supabase\n          .from('series')", "supabase\n          .from('series_books')", "supabase\n            .from('projects')", "supabase\n          .from('series')", "supabase\n          .from('series_character_continuity')", "supabase\n      .from('series')", "supabase\n      .from('series_books')", "supabase\n      .from('series_character_continuity')", "createServerClient(", "createServerClient("], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\[id]\\books\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('series')", "supabase\r\n      .from('series_books')", "supabase\r\n      .from('series')", "supabase\r\n      .from('projects')", "supabase\r\n      .from('series_books')", "supabase\r\n      .from('series')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\[id]\\analytics\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('series')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\series\\[id]\\character-arcs\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\references\\[id]\\summarize\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('reference_materials')", "supabase\r\n      .from('reference_materials')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\references\\[id]\\extract\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('reference_materials')", "supabase\r\n      .from('reference_materials')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\projects\\[id]\\voice-profile\\route.ts", "imports": ["import { createServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\n      .from('projects')", "supabase\n      .from('projects')", "supabase\n        .from('projects')", "supabase\n        .from('voice_profiles')", "supabase\n        .from('projects')", "supabase\n        .from('projects')", "supabase\n      .from('projects')", "supabase\n      .from('projects')", "createServerClient(", "createServerClient(", "createServerClient("], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\projects\\[id]\\team\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n      .from('users')", "supabase\r\n      .from('project_collaborators')", "supabase\r\n      .from('project_invitations')", "supabase\r\n      .from('project_collaborators')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\projects\\[id]\\plot-threads\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('plot_threads')", "supabase\r\n        .from('characters')", "supabase\r\n        .from('locations')", "supabase\r\n      .from('projects')", "supabase\r\n      .from('plot_threads')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\projects\\[id]\\map-preferences\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n        .from('project_collaborators')", "supabase\r\n      .from('projects')", "supabase\r\n        .from('project_collaborators')", "supabase\r\n      .from('user_map_preferences')", "supabase\r\n      .from('user_map_preferences')", "supabase\r\n      .rpc(", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\projects\\[id]\\locations\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('locations')", "supabase\r\n      .from('projects')", "supabase\r\n      .from('locations')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\projects\\[id]\\location-positions\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('location_positions')", "supabase\r\n      .from('projects')", "supabase\r\n        .from('project_collaborators')", "supabase\r\n        .from('location_positions')", "supabase\r\n      .from('location_positions')", "supabase\r\n      .from('projects')", "supabase\r\n        .from('project_collaborators')", "supabase\r\n      .from('location_positions')", "supabase\r\n      .from('location_positions')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\projects\\[id]\\export\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n        .from('projects')", "supabase\r\n      .from('projects')", "supabase\r\n        .from('chapters')", "supabase\r\n      .from('user_subscriptions')", "supabase.rpc(", "supabase.rpc(", "supabase.rpc(", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "BillingService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\projects\\[id]\\characters\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n      .from('characters')", "supabase\r\n      .from('projects')", "supabase\r\n      .from('characters')", "supabase\r\n      .from('characters')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\profiles\\[id]\\favorite\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('selection_profiles')", "supabase\r\n      .from('users')", "supabase\r\n      .from('users')", "supabase\r\n        .from('selection_analytics')", "supabase\r\n      .from('users')", "supabase\r\n      .from('users')", "supabase\r\n        .from('selection_analytics')", "supabase\r\n      .from('users')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\profiles\\[id]\\clone\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('selection_profiles')", "supabase\r\n      .from('selection_profiles')", "supabase\r\n        .from('selection_analytics')", "supabase.rpc(", "createTypedServerClient()"], "suggestedService": "UserService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\notifications\\[id]\\read\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('notifications')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\memory\\cache\\clear\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('projects')", "supabase\r\n        .from('content_embedding_cache')", "supabase\r\n          .from('content_embedding_cache')", "supabase\r\n        .from('analysis_cache')", "supabase\r\n          .from('analysis_cache')", "supabase.from('system_events')", "supabase\r\n      .from('projects')", "supabase\r\n        .from('content_embedding_cache')", "supabase\r\n        .from('analysis_cache')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\invitations\\[token]\\accept\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n        .from('project_collaborators')", "supabase\r\n      .from('project_invitations')", "supabase\r\n      .from('users')", "supabase\r\n      .from('project_collaborators')", "supabase\r\n          .from('project_collaborators')", "supabase\r\n          .from('project_invitations')", "supabase\r\n      .from('project_invitations')", "supabase\r\n      .from('project_collaborators')", "supabase\r\n        .from('project_invitations')", "supabase\r\n      .from('collaboration_sessions')", "supabase\r\n      .from('project_invitations')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\export\\jobs\\[id]\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('export_jobs')", "supabase\r\n      .from('export_jobs')", "supabase\r\n      .from('export_jobs')", "supabase.storage\r\n            .", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\characters\\[id]\\share\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('series')", "supabase\r\n      .from('characters')", "supabase\r\n      .from('character_shares')", "supabase\r\n      .from('character_shares')", "supabase\r\n      .rpc(", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\billing\\webhooks\\stripe\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase.from('user_subscriptions')", "supabase\r\n            .from('user_subscriptions')", "supabase\r\n          .from('user_subscriptions')", "supabase\r\n            .from('user_subscriptions')", "supabase\r\n              .from('usage_tracking')", "supabase\r\n            .from('user_subscriptions')", "supabase\r\n          .from('payment_intents')", "supabase\r\n          .from('payment_intents')", "createTypedServerClient()"], "suggestedService": "BillingService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\billing\\subscriptions\\portal\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n        .from('user_subscriptions')", "supabase\r\n      .from('profiles')", "supabase\r\n        .from('profiles')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "BillingService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\billing\\subscriptions\\checkout\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n        .from('user_subscriptions')", "supabase\r\n        .from('checkout_sessions')", "supabase\r\n      .from('profiles')", "supabase\r\n          .from('profiles')", "supabase\r\n      .from('checkout_sessions')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "BillingService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\billing\\payments\\create-payment-intent\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n        .from('user_subscriptions')", "supabase\r\n        .from('payment_intents')", "supabase\r\n      .from('profiles')", "supabase\r\n        .from('profiles')", "supabase\r\n      .from('payment_intents')", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "BillingService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\billing\\payments\\charge\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n          .from('user_subscriptions')", "supabase\r\n        .from('payment_attempts')", "supabase\r\n      .from('payment_attempts')", "supabase\r\n      .from('profiles')", "supabase\r\n          .from('profiles')", "supabase\r\n        .from('profiles')", "supabase.from('user_subscriptions')", "supabase\r\n        .from('payment_intents')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "BillingService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\ai\\suggestions\\[id]\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('ai_suggestions')", "supabase\r\n      .from('ai_suggestions')", "supabase\r\n      .from('ai_suggestions')", "supabase\r\n      .from('ai_suggestions')", "supabase\r\n      .from('ai_suggestions')", "createTypedServerClient()", "createTypedServerClient()", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\agents\\suggestions\\feedback\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('ai_suggestion_feedback')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\projects\\[id]\\collaborators\\invite\\route.ts", "imports": ["import { createServerClient } from '@/lib/supabase'", "import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\n      .from('projects')", "supabase\n        .from('project_collaborators')", "supabase\n      .from('project_collaborators')", "supabase\n      .from('user_subscriptions')", "supabase\n        .from('project_collaborators')", "supabase\n      .from('profiles')", "supabase\n      .from('project_collaborators')", "createTypedServerClient()"], "suggestedService": "BillingService"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\export\\jobs\\[id]\\cancel\\route.ts", "imports": ["import { createTypedServerClient } from '@/lib/supabase'"], "supabaseUsage": ["supabase\r\n      .from('export_jobs')", "createTypedServerClient()"], "suggestedService": "Unknown - needs analysis"}]