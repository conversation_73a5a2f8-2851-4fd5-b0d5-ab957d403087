import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { ServiceBase, ServiceResponse } from './base-service';
import { Database } from '@/lib/db/database.types';

type Chapter = Database['public']['Tables']['chapters']['Row'];
type ChapterInsert = Database['public']['Tables']['chapters']['Insert'];
type ChapterUpdate = Database['public']['Tables']['chapters']['Update'];
type ChapterVersion = Database['public']['Tables']['chapter_versions']['Row'];
type ChapterVersionInsert = Database['public']['Tables']['chapter_versions']['Insert'];

export class ChapterService extends ServiceBase {
  constructor() {
    super({
      name: 'chapter-service',
      version: '1.0.0',
      endpoints: ['/api/chapters'],
      dependencies: [],
      healthCheck: '/api/services/chapter/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, { status: 'healthy' });
  }

  /**
   * Get all chapters for a project
   */
  async getProjectChapters(projectId: string): Promise<ServiceResponse<Chapter[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('chapters')
        .select('*')
        .eq('project_id', projectId)
        .order('chapter_number', { ascending: true });
      
      if (error) {
        logger.error('[ChapterService] Error fetching chapters:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Get a single chapter
   */
  async getChapter(chapterId: string): Promise<ServiceResponse<Chapter>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('chapters')
        .select('*')
        .eq('id', chapterId)
        .single();
      
      if (error) {
        logger.error('[ChapterService] Error fetching chapter:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Chapter not found');
      }
      
      return data;
    });
  }

  /**
   * Get a chapter with ownership check
   */
  async getChapterWithOwnership(chapterId: string, userId: string): Promise<ServiceResponse<Chapter & { project: { id: string; title: string; user_id: string } }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('chapters')
        .select(`
          *,
          projects!inner (
            id,
            title,
            user_id
          )
        `)
        .eq('id', chapterId)
        .eq('projects.user_id', userId)
        .single();
      
      if (error) {
        if (error.code === 'PGRST116') {
          throw new Error('Chapter not found or access denied');
        }
        logger.error('[ChapterService] Error fetching chapter with ownership:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Chapter not found');
      }
      
      return data as Chapter & { project: { id: string; title: string; user_id: string } };
    });
  }

  /**
   * Check chapter ownership
   */
  async checkChapterOwnership(chapterId: string, userId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('chapters')
        .select(`
          id,
          projects!inner (
            user_id
          )
        `)
        .eq('id', chapterId)
        .eq('projects.user_id', userId)
        .single();
      
      if (error) {
        if (error.code === 'PGRST116') {
          return false;
        }
        logger.error('[ChapterService] Error checking ownership:', error);
        throw error;
      }
      
      return !!data;
    });
  }

  /**
   * Create a new chapter
   */
  async createChapter(chapterData: ChapterInsert): Promise<ServiceResponse<Chapter>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('chapters')
        .insert({
          ...chapterData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        logger.error('[ChapterService] Error creating chapter:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Failed to create chapter');
      }
      
      return data;
    });
  }

  /**
   * Create multiple chapters
   */
  async createChapters(chaptersData: ChapterInsert[]): Promise<ServiceResponse<Chapter[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const chaptersWithTimestamps = chaptersData.map(chapter => ({
        ...chapter,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));
      
      const { data, error } = await supabase
        .from('chapters')
        .insert(chaptersWithTimestamps)
        .select();
      
      if (error) {
        logger.error('[ChapterService] Error creating chapters:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Update a chapter
   */
  async updateChapter(chapterId: string, updates: ChapterUpdate): Promise<ServiceResponse<Chapter>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Calculate word count if content is provided
      let updateData = { ...updates };
      if (updates.content !== undefined) {
        const wordCount = updates.content.trim().split(/\s+/).filter((word: string) => word.length > 0).length;
        updateData.actual_word_count = wordCount;
      }
      
      const { data, error } = await supabase
        .from('chapters')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', chapterId)
        .select()
        .single();
      
      if (error) {
        logger.error('[ChapterService] Error updating chapter:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Chapter not found');
      }
      
      return data;
    });
  }

  /**
   * Update chapter with ownership check
   */
  async updateChapterWithOwnership(chapterId: string, userId: string, updates: ChapterUpdate): Promise<ServiceResponse<Chapter>> {
    return this.withErrorHandling(async () => {
      // First check ownership
      const ownershipResponse = await this.checkChapterOwnership(chapterId, userId);
      if (!ownershipResponse.success || !ownershipResponse.data) {
        throw new Error('Chapter not found or access denied');
      }
      
      // Then update
      return await this.updateChapter(chapterId, updates);
    });
  }

  /**
   * Delete a chapter
   */
  async deleteChapter(chapterId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from('chapters')
        .delete()
        .eq('id', chapterId);
      
      if (error) {
        logger.error('[ChapterService] Error deleting chapter:', error);
        throw error;
      }
      
      return true;
    });
  }

  /**
   * Delete all chapters for a project
   */
  async deleteProjectChapters(projectId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from('chapters')
        .delete()
        .eq('project_id', projectId);
      
      if (error) {
        logger.error('[ChapterService] Error deleting project chapters:', error);
        throw error;
      }
      
      return true;
    });
  }

  /**
   * Get chapter count for a project
   */
  async getChapterCount(projectId: string): Promise<ServiceResponse<number>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { count, error } = await supabase
        .from('chapters')
        .select('id', { count: 'exact', head: true })
        .eq('project_id', projectId);
      
      if (error) {
        logger.error('[ChapterService] Error counting chapters:', error);
        throw error;
      }
      
      return count || 0;
    });
  }

  /**
   * Get total word count for a project
   */
  async getProjectWordCount(projectId: string): Promise<ServiceResponse<number>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('chapters')
        .select('word_count')
        .eq('project_id', projectId);
      
      if (error) {
        logger.error('[ChapterService] Error calculating word count:', error);
        throw error;
      }
      
      const totalWordCount = (data || []).reduce((sum, chapter) => sum + (chapter.word_count || 0), 0);
      
      return totalWordCount;
    });
  }

  /**
   * Get chapter version history with pagination
   */
  async getChapterVersionHistory(
    chapterId: string, 
    userId: string, 
    options: { limit?: number; offset?: number } = {}
  ): Promise<ServiceResponse<{
    chapter: Chapter & { 
      projects: Array<{ id: string; title: string; user_id: string }> 
    };
    versions: ChapterVersion[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // First verify user owns the chapter through project
      const { data: chapter, error: chapterError } = await supabase
        .from('chapters')
        .select(`
          id,
          title,
          chapter_number,
          content,
          word_count,
          updated_at,
          projects!inner (
            id,
            title,
            user_id
          )
        `)
        .eq('id', chapterId)
        .eq('projects.user_id', userId)
        .single();

      if (chapterError) {
        if (chapterError.code === 'PGRST116') {
          throw new Error('Chapter not found or access denied');
        }
        logger.error('[ChapterService] Error fetching chapter for version history:', chapterError);
        throw chapterError;
      }

      if (!chapter) {
        throw new Error('Chapter not found');
      }

      // Get paginated version history
      const limit = options.limit || 10;
      const offset = options.offset || 0;
      
      let query = supabase
        .from('chapter_versions')
        .select('*', { count: 'exact' })
        .eq('chapter_id', chapterId)
        .order('version_number', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: versions, error: versionsError, count } = await query;

      if (versionsError) {
        logger.error('[ChapterService] Error fetching version history:', versionsError);
        throw versionsError;
      }

      return {
        chapter: chapter as Chapter & { 
          projects: Array<{ id: string; title: string; user_id: string }> 
        },
        versions: versions || [],
        pagination: {
          total: count || 0,
          limit,
          offset,
          hasMore: (count || 0) > offset + limit
        }
      };
    });
  }

  /**
   * Create a new chapter version
   */
  async createChapterVersion(
    chapterId: string, 
    userId: string, 
    versionData: {
      content: string;
      changes_summary?: string;
      created_by?: 'user' | 'ai_writer' | 'ai_editor';
      quality_score?: Record<string, unknown>;
    }
  ): Promise<ServiceResponse<ChapterVersion>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Verify user owns the chapter
      const ownershipResponse = await this.checkChapterOwnership(chapterId, userId);
      if (!ownershipResponse.success || !ownershipResponse.data) {
        throw new Error('Chapter not found or access denied');
      }

      // Get the latest version number
      const { data: latestVersion } = await supabase
        .from('chapter_versions')
        .select('version_number')
        .eq('chapter_id', chapterId)
        .order('version_number', { ascending: false })
        .limit(1)
        .maybeSingle();

      const nextVersionNumber = (latestVersion?.version_number || 0) + 1;

      // Calculate word count
      const wordCount = versionData.content.trim().split(/\s+/).filter(word => word.length > 0).length;

      // Create new version
      const { data: newVersion, error } = await supabase
        .from('chapter_versions')
        .insert({
          chapter_id: chapterId,
          version_number: nextVersionNumber,
          content: versionData.content,
          word_count: wordCount,
          changes_summary: versionData.changes_summary,
          quality_score: versionData.quality_score,
          created_by: versionData.created_by || 'user',
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        logger.error('[ChapterService] Error creating chapter version:', error);
        throw error;
      }

      if (!newVersion) {
        throw new Error('Failed to create chapter version');
      }

      return newVersion;
    });
  }
}