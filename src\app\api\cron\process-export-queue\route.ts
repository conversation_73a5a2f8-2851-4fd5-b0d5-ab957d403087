import { NextRequest, NextResponse } from 'next/server'
import { TaskService } from '@/lib/services/task-service'
import { logger } from '@/lib/services/logger'

export async function GET(request: NextRequest) {
  try {
    // Verify cron secret
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const taskService = new TaskService()
    await taskService.initialize()

    // Process export queue using TaskService
    const processedCountResponse = await taskService.processExportQueue()
    
    if (!processedCountResponse.success) {
      logger.error('Failed to process export queue via TaskService:', processedCountResponse.error)
      return NextResponse.json(
        { error: 'Failed to process export queue' },
        { status: 500 }
      )
    }

    // Clean up old completed/failed tasks (older than 30 days)
    const cleanupResponse = await taskService.cleanupOldTasks(30)
    const cleanedCount = cleanupResponse.success ? cleanupResponse.data : 0

    const processedCount = processedCountResponse.data

    logger.info('Export queue processed via cron', {
      processedTasks: processedCount,
      cleanedupTasks: cleanedCount,
      timestamp: new Date().toISOString()
    })

    return NextResponse.json({ 
      success: true,
      message: 'Export queue processor completed',
      processedTasks: processedCount,
      cleanedupTasks: cleanedCount,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    logger.error('Export queue cron error:', error)
    return NextResponse.json(
      { error: 'Failed to process export queue' },
      { status: 500 }
    )
  }
}

// Also export POST for manual triggering
export async function POST(request: NextRequest) {
  return GET(request)
}