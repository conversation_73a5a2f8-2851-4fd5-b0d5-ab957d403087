'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Zap } from 'lucide-react'
import { Archive } from 'lucide-react'
import { Merge } from 'lucide-react'
import { Trash2 } from 'lucide-react'
import { AlertTriangle } from 'lucide-react'
import { CheckCircle2 } from 'lucide-react'
import { Info } from 'lucide-react'
import { ChevronRight } from 'lucide-react'
import type { MemoryStats } from '@/hooks/use-memory-stats'
import { formatNumber } from '@/lib/utils'

interface OptimizationOption {
  id: string
  name: string
  description: string
  estimatedSavings: number
  risk: 'low' | 'medium' | 'high'
  action: () => Promise<void>
}

interface MemoryOptimizerProps {
  projectId: string
  currentStats: MemoryStats | null
  onOptimizationComplete?: () => void
}

export function MemoryOptimizer({ 
  projectId, 
  currentStats,
  onOptimizationComplete 
}: MemoryOptimizerProps) {
  const [selectedOptions, setSelectedOptions] = useState<Set<string>>(new Set())
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [optimizationResults, setOptimizationResults] = useState<{
    success: boolean
    message: string
    savedTokens?: number
  } | null>(null)

  const optimizationOptions: OptimizationOption[] = [
    {
      id: 'compress-old',
      name: 'Compress Old Chapters',
      description: 'Compress chapters that haven\'t been accessed in 30+ days',
      estimatedSavings: Math.floor((currentStats?.totalTokens || 0) * 0.15),
      risk: 'low',
      action: async () => {
        const response = await fetch('/api/memory/compress', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            projectId,
            options: { targetAge: 30 }
          })
        })
        if (!response.ok) throw new Error('Compression failed')
        return response.json()
      }
    },
    {
      id: 'merge-similar',
      name: 'Merge Similar Contexts',
      description: 'Combine overlapping character and location descriptions',
      estimatedSavings: Math.floor((currentStats?.totalTokens || 0) * 0.10),
      risk: 'medium',
      action: async () => {
        const response = await fetch('/api/memory/merge', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            projectId,
            options: { similarity: 0.8 }
          })
        })
        if (!response.ok) throw new Error('Merge failed')
        return response.json()
      }
    },
    {
      id: 'remove-low-importance',
      name: 'Remove Low-Importance Content',
      description: 'Delete contexts with importance score below 20%',
      estimatedSavings: Math.floor((currentStats?.totalTokens || 0) * 0.08),
      risk: 'high',
      action: async () => {
        const response = await fetch('/api/memory/prune', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            projectId,
            options: { minImportance: 0.2 }
          })
        })
        if (!response.ok) throw new Error('Pruning failed')
        return response.json()
      }
    },
    {
      id: 'archive-completed',
      name: 'Archive Completed Storylines',
      description: 'Move resolved plot threads to cold storage',
      estimatedSavings: Math.floor((currentStats?.totalTokens || 0) * 0.12),
      risk: 'low',
      action: async () => {
        const response = await fetch('/api/memory/archive', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            projectId,
            options: { archiveCompleted: true }
          })
        })
        if (!response.ok) throw new Error('Archiving failed')
        return response.json()
      }
    }
  ]

  const totalEstimatedSavings = Array.from(selectedOptions)
    .reduce((sum, optionId) => {
      const option = optimizationOptions.find(o => o.id === optionId)
      return sum + (option?.estimatedSavings || 0)
    }, 0)

  const handleToggleOption = (optionId: string) => {
    const newSelected = new Set(selectedOptions)
    if (newSelected.has(optionId)) {
      newSelected.delete(optionId)
    } else {
      newSelected.add(optionId)
    }
    setSelectedOptions(newSelected)
  }

  const handleOptimize = async () => {
    if (selectedOptions.size === 0) return

    setIsOptimizing(true)
    setOptimizationResults(null)

    try {
      let totalSaved = 0
      const selectedOptionsList = Array.from(selectedOptions)
        .map(id => optimizationOptions.find(o => o.id === id))
        .filter(Boolean) as OptimizationOption[]

      for (const option of selectedOptionsList) {
        try {
          const result = await option.action()
          totalSaved += result.savedTokens || option.estimatedSavings
        } catch (error) {
          console.error(`Failed to execute ${option.name}:`, error)
        }
      }

      setOptimizationResults({
        success: true,
        message: `Successfully optimized memory`,
        savedTokens: totalSaved
      })

      onOptimizationComplete?.()
    } catch (error) {
      setOptimizationResults({
        success: false,
        message: error instanceof Error ? error.message : 'Optimization failed'
      })
    } finally {
      setIsOptimizing(false)
      setSelectedOptions(new Set())
    }
  }

  const getRiskBadgeVariant = (risk: 'low' | 'medium' | 'high') => {
    switch (risk) {
      case 'low': return 'outline'
      case 'medium': return 'secondary'
      case 'high': return 'destructive'
    }
  }

  const getRiskColor = (risk: 'low' | 'medium' | 'high') => {
    switch (risk) {
      case 'low': return 'text-green-600'
      case 'medium': return 'text-yellow-600'
      case 'high': return 'text-red-600'
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Memory Optimization Options</CardTitle>
          <CardDescription>
            Select optimization strategies to reduce memory usage. Higher risk options may affect AI context quality.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {optimizationOptions.map((option) => (
            <div
              key={option.id}
              className={`p-4 border rounded-lg transition-colors ${
                selectedOptions.has(option.id) ? 'border-primary bg-primary/5' : ''
              }`}
            >
              <div className="flex items-start gap-3">
                <Checkbox
                  id={option.id}
                  checked={selectedOptions.has(option.id)}
                  onCheckedChange={() => handleToggleOption(option.id)}
                  disabled={isOptimizing}
                />
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between">
                    <Label 
                      htmlFor={option.id} 
                      className="text-base font-medium cursor-pointer"
                    >
                      {option.name}
                    </Label>
                    <div className="flex items-center gap-2">
                      <Badge variant={getRiskBadgeVariant(option.risk)}>
                        Risk: {option.risk}
                      </Badge>
                      <span className="text-sm font-medium text-muted-foreground">
                        ~{formatNumber(option.estimatedSavings)} tokens
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {option.description}
                  </p>
                </div>
              </div>
            </div>
          ))}

          {selectedOptions.size > 0 && (
            <div className="mt-6 p-4 bg-primary/5 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Estimated Total Savings</span>
                <span className="text-lg font-bold">
                  {formatNumber(totalEstimatedSavings)} tokens
                </span>
              </div>
              <Progress 
                value={(totalEstimatedSavings / (currentStats?.totalTokens || 1)) * 100} 
                className="h-2"
              />
              <p className="text-sm text-muted-foreground mt-2">
                {((totalEstimatedSavings / (currentStats?.totalTokens || 1)) * 100).toFixed(1)}% 
                reduction in memory usage
              </p>
            </div>
          )}

          <div className="flex justify-end gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setSelectedOptions(new Set())}
              disabled={isOptimizing || selectedOptions.size === 0}
            >
              Clear Selection
            </Button>
            <Button
              onClick={handleOptimize}
              disabled={isOptimizing || selectedOptions.size === 0}
            >
              <Zap className="h-4 w-4 mr-2" />
              {isOptimizing ? 'Optimizing...' : 'Optimize Memory'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {optimizationResults && (
        <Alert className={optimizationResults.success ? 'border-green-200' : ''}>
          {optimizationResults.success ? (
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          ) : (
            <AlertTriangle className="h-4 w-4" />
          )}
          <AlertTitle>
            {optimizationResults.success ? 'Optimization Complete' : 'Optimization Failed'}
          </AlertTitle>
          <AlertDescription>
            {optimizationResults.message}
            {optimizationResults.savedTokens && (
              <span className="block mt-1 font-medium">
                Saved {formatNumber(optimizationResults.savedTokens)} tokens
              </span>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Optimization Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            Optimization Best Practices
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm">
            <li className="flex items-start gap-2">
              <ChevronRight className="h-4 w-4 mt-0.5 flex-shrink-0 text-muted-foreground" />
              <span>
                <strong>Start with low-risk options</strong> - Compression and archiving are safe choices
              </span>
            </li>
            <li className="flex items-start gap-2">
              <ChevronRight className="h-4 w-4 mt-0.5 flex-shrink-0 text-muted-foreground" />
              <span>
                <strong>Review before removing</strong> - High-risk options permanently delete content
              </span>
            </li>
            <li className="flex items-start gap-2">
              <ChevronRight className="h-4 w-4 mt-0.5 flex-shrink-0 text-muted-foreground" />
              <span>
                <strong>Regular maintenance</strong> - Run optimization monthly for best performance
              </span>
            </li>
            <li className="flex items-start gap-2">
              <ChevronRight className="h-4 w-4 mt-0.5 flex-shrink-0 text-muted-foreground" />
              <span>
                <strong>Monitor AI quality</strong> - Too much optimization may affect writing assistance
              </span>
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}