#!/usr/bin/env node
import { readdir, readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface ServiceLayerIssue {
  file: string;
  line: number;
  issue: string;
  pattern: string;
  recommendation: string;
  severity: 'high' | 'medium' | 'low';
}

// Services that should be used instead of direct database access
const AVAILABLE_SERVICES = {
  'projects': 'ProjectService or ContentGenerator',
  'chapters': 'ChapterService or ContentGenerator',
  'characters': 'CharacterService or ContentGenerator',
  'ai': 'AIOrchestrator',
  'analytics': 'AnalyticsEngine',
  'collaboration': 'CollaborationService',
  'search': 'SemanticSearch',
  'email': 'MailerooEmailService',
  'auth': 'UnifiedAuthService',
  'usage': 'UsageTracker',
  'story_bible': 'ContextManager',
  'writing_sessions': 'AnalyticsEngine',
};

async function findAPIRoutes(dir: string): Promise<string[]> {
  const files: string[] = [];
  
  async function walk(currentDir: string) {
    const entries = await readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        if (!['node_modules', '.next', 'dist', '.git'].includes(entry.name)) {
          await walk(fullPath);
        }
      } else if (entry.isFile() && entry.name === 'route.ts' && (fullPath.includes('\\api\\') || fullPath.includes('/api/'))) {
        files.push(fullPath);
      }
    }
  }
  
  await walk(dir);
  return files;
}

function analyzeFile(content: string, filePath: string): ServiceLayerIssue[] {
  const issues: ServiceLayerIssue[] = [];
  const lines = content.split('\n');
  
  // Check if file imports services
  const hasServiceImports = content.includes('ServiceManager') || 
                          content.includes('AIOrchestrator') ||
                          content.includes('ContentGenerator') ||
                          content.includes('AnalyticsEngine') ||
                          content.includes('CollaborationService');
  
  // Check for direct database access patterns
  lines.forEach((line, index) => {
    // Direct Supabase queries in API routes
    if (line.includes('supabase.from(') || line.includes('.from(')) {
      const tableMatch = line.match(/\.from\(['"](\w+)['"]\)/);
      if (tableMatch) {
        const table = tableMatch[1];
        const service = AVAILABLE_SERVICES[table as keyof typeof AVAILABLE_SERVICES];
        
        // Skip if it's just auth check
        if (table === 'profiles' && line.includes('subscription_tier')) {
          return;
        }
        
        issues.push({
          file: filePath,
          line: index + 1,
          issue: `Direct database access to '${table}' table`,
          pattern: line.trim(),
          recommendation: service ? `Use ${service} service instead` : 'Use appropriate service layer',
          severity: hasServiceImports ? 'medium' : 'high'
        });
      }
    }
    
    // Business logic in routes
    if (line.includes('calculate') || line.includes('generate') || line.includes('analyze')) {
      if (!line.includes('await') || !line.includes('Service')) {
        issues.push({
          file: filePath,
          line: index + 1,
          issue: 'Business logic in API route',
          pattern: line.trim(),
          recommendation: 'Move business logic to service layer',
          severity: 'medium'
        });
      }
    }
    
    // Complex data transformations
    if (line.includes('.map(') && lines[index + 1]?.includes('.filter(')) {
      issues.push({
        file: filePath,
        line: index + 1,
        issue: 'Complex data transformation in route',
        pattern: line.trim(),
        recommendation: 'Move data transformation to service layer',
        severity: 'low'
      });
    }
  });
  
  // Check for missing service usage
  if (!hasServiceImports && issues.length > 0) {
    issues.unshift({
      file: filePath,
      line: 1,
      issue: 'No service imports found',
      pattern: 'Missing service imports',
      recommendation: 'Import and use ServiceManager or specific services',
      severity: 'high'
    });
  }
  
  return issues;
}

async function generateServiceLayerGuide(): Promise<void> {
  const guide = `// Service Layer Usage Guide for API Routes
// Follow these patterns for proper service layer usage

import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { ServiceManager } from '@/lib/services/service-manager'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler'
import { z } from 'zod'

// ============================================
// Correct Pattern: Using Service Layer
// ============================================

export async function POST(request: NextRequest) {
  return UnifiedAuthService.withAuth(async (req) => {
    try {
      const user = req.user!
      const body = await req.json()
      
      // Validate input
      const schema = z.object({
        title: z.string().min(1),
        content: z.string(),
      })
      const validated = schema.parse(body)
      
      // Use service layer for business logic
      const serviceManager = ServiceManager.getInstance()
      const contentGenerator = await serviceManager.getContentGenerator()
      
      const result = await contentGenerator.generateContent({
        userId: user.id,
        ...validated
      })
      
      return NextResponse.json({ success: true, data: result })
    } catch (error) {
      return handleAPIError(error as Error)
    }
  })(request)
}

// ============================================
// Service Layer Examples
// ============================================

// 1. Project Management
export async function handleProjectOperation(userId: string, projectId: string) {
  const serviceManager = ServiceManager.getInstance()
  const orchestrator = await serviceManager.getAIOrchestrator()
  
  // Let service handle all business logic
  return orchestrator.orchestrateGeneration({
    projectId,
    userId,
    mode: 'full'
  })
}

// 2. Analytics
export async function getAnalytics(userId: string, dateRange: any) {
  const serviceManager = ServiceManager.getInstance()
  const analytics = await serviceManager.getAnalyticsEngine()
  
  return analytics.generateReport({
    userId,
    dateRange,
    metrics: ['productivity', 'quality', 'progress']
  })
}

// 3. Search Operations
export async function performSearch(query: string, filters: any) {
  const serviceManager = ServiceManager.getInstance()
  const search = await serviceManager.getSemanticSearch()
  
  return search.search({
    query,
    filters,
    limit: 20
  })
}

// ============================================
// What NOT to do in API Routes
// ============================================

// ❌ DON'T: Direct database queries
// const { data } = await supabase.from('projects').select('*')

// ❌ DON'T: Business logic in routes
// const wordCount = content.split(' ').length
// const readingTime = Math.ceil(wordCount / 200)

// ❌ DON'T: Complex data transformations
// const formatted = data.map(...).filter(...).reduce(...)

// ✅ DO: Use services for all of the above
// const result = await service.processData(input)

// ============================================
// Available Services
// ============================================

/*
ServiceManager provides access to:
- getAIOrchestrator(): AI agent coordination
- getContentGenerator(): Content creation
- getContextManager(): Context and memory management
- getAnalyticsEngine(): Analytics and reporting
- getCollaborationHub(): Real-time collaboration
- getSemanticSearch(): Search functionality
- getMailerooEmailService(): Email operations

Each service encapsulates:
- Business logic
- Database operations
- Data transformations
- Error handling
- Caching strategies
*/
`;

  const filePath = join(__dirname, '..', 'src', 'lib', 'api', 'service-layer-guide.ts');
  await writeFile(filePath, guide);
  console.log('Created service layer guide at: src/lib/api/service-layer-guide.ts');
}

async function generateReport(issues: ServiceLayerIssue[]): Promise<void> {
  const reportPath = join(__dirname, '..', 'service-layer-analysis-report.md');
  
  let report = '# Service Layer Usage Analysis\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  report += `Total issues found: ${issues.length}\n\n`;
  
  // Group by severity
  const highSeverity = issues.filter(i => i.severity === 'high');
  const mediumSeverity = issues.filter(i => i.severity === 'medium');
  const lowSeverity = issues.filter(i => i.severity === 'low');
  
  report += '## Summary\n\n';
  report += `- **High Severity**: ${highSeverity.length} issues (no service layer usage)\n`;
  report += `- **Medium Severity**: ${mediumSeverity.length} issues (partial service usage)\n`;
  report += `- **Low Severity**: ${lowSeverity.length} issues (minor improvements)\n\n`;
  
  // Group by file
  const byFile = issues.reduce((acc, issue) => {
    if (!acc[issue.file]) acc[issue.file] = [];
    acc[issue.file].push(issue);
    return acc;
  }, {} as Record<string, ServiceLayerIssue[]>);
  
  // Files with most issues
  const sortedFiles = Object.entries(byFile)
    .sort((a, b) => b[1].length - a[1].length)
    .slice(0, 10);
  
  report += '## Files with Most Issues\n\n';
  sortedFiles.forEach(([file, fileIssues]) => {
    report += `### ${file.replace(/\\/g, '/')}\n`;
    report += `- **Total Issues**: ${fileIssues.length}\n`;
    report += `- **High Severity**: ${fileIssues.filter(i => i.severity === 'high').length}\n`;
    
    // Show first few issues
    fileIssues.slice(0, 3).forEach(issue => {
      report += `  - Line ${issue.line}: ${issue.issue}\n`;
      report += `    - Fix: ${issue.recommendation}\n`;
    });
    
    if (fileIssues.length > 3) {
      report += `  - ... and ${fileIssues.length - 3} more issues\n`;
    }
    report += '\n';
  });
  
  // Common patterns
  const patterns = new Map<string, number>();
  issues.forEach(issue => {
    patterns.set(issue.issue, (patterns.get(issue.issue) || 0) + 1);
  });
  
  report += '## Most Common Issues\n\n';
  Array.from(patterns.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .forEach(([pattern, count]) => {
      report += `- **${pattern}**: ${count} occurrences\n`;
    });
  
  report += '\n## Recommendations\n\n';
  report += '1. **High Priority**: Fix routes with no service layer usage\n';
  report += '2. **Use ServiceManager**: Access all services through the central manager\n';
  report += '3. **Move Business Logic**: Extract complex logic to appropriate services\n';
  report += '4. **Standardize Patterns**: Follow the guide in `service-layer-guide.ts`\n';
  report += '5. **Test After Migration**: Ensure functionality remains intact\n\n';
  
  report += '## Benefits of Service Layer\n\n';
  report += '- **Separation of Concerns**: Routes handle HTTP, services handle logic\n';
  report += '- **Reusability**: Services can be used across multiple routes\n';
  report += '- **Testability**: Easier to unit test business logic\n';
  report += '- **Maintainability**: Changes in one place affect all consumers\n';
  report += '- **Performance**: Services can implement caching and optimization\n';
  
  await writeFile(reportPath, report);
  console.log(`Report saved to: ${reportPath}`);
}

async function main() {
  console.log('🔍 Analyzing API routes for service layer usage...\n');
  
  const srcDir = join(__dirname, '..', 'src');
  const apiRoutes = await findAPIRoutes(srcDir);
  
  console.log(`Found ${apiRoutes.length} API route files\n`);
  
  const allIssues: ServiceLayerIssue[] = [];
  
  // Analyze each route
  for (const route of apiRoutes) {
    const content = await readFile(route, 'utf-8');
    const issues = analyzeFile(content, route);
    if (issues.length > 0) {
      console.log(`❌ ${route.replace(/\\/g, '/')}: ${issues.length} issues`);
      allIssues.push(...issues);
    } else {
      console.log(`✅ ${route.replace(/\\/g, '/')}: Good service layer usage`);
    }
  }
  
  console.log(`\n📊 Total issues found: ${allIssues.length}`);
  
  // Generate guide
  await generateServiceLayerGuide();
  
  // Generate report
  if (allIssues.length > 0) {
    await generateReport(allIssues);
  }
  
  console.log('\n✅ Service layer analysis complete!');
}

// Run the script
main().catch(console.error);