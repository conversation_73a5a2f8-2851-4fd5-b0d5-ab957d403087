# Bundle Optimization Report

Generated: 2025-08-06T01:49:22.512Z

Total issues found: 140

## Summary

- **barrel-import**: 46 issues
- **duplicate-import**: 94 issues

## Barrel Import

### C:/Users/<USER>/BookScribe/src/app/(dashboard)/demo/accessibility/page.tsx:22
- **Issue**: Possible barrel import from lucide-react
- **Fix**: Import specific modules to reduce bundle size

### C:/Users/<USER>/BookScribe/src/app/(dashboard)/projects/[id]/page.tsx:12
- **Issue**: Possible barrel import from lucide-react
- **Fix**: Import specific modules to reduce bundle size

### C:/Users/<USER>/BookScribe/src/app/(marketing)/page.tsx:5
- **Issue**: Possible barrel import from lucide-react
- **Fix**: Import specific modules to reduce bundle size

### C:/Users/<USER>/BookScribe/src/app/(marketing)/privacy/page.tsx:2
- **Issue**: Possible barrel import from lucide-react
- **Fix**: Import specific modules to reduce bundle size

### C:/Users/<USER>/BookScribe/src/app/(marketing)/terms/page.tsx:2
- **Issue**: Possible barrel import from lucide-react
- **Fix**: Import specific modules to reduce bundle size

### C:/Users/<USER>/BookScribe/src/app/api/analytics/productivity/route.ts:5
- **Issue**: Possible barrel import from date-fns
- **Fix**: Import specific modules to reduce bundle size

### C:/Users/<USER>/BookScribe/src/app/tasks/page.tsx:10
- **Issue**: Possible barrel import from lucide-react
- **Fix**: Import specific modules to reduce bundle size

### C:/Users/<USER>/BookScribe/src/app/timeline/page.tsx:10
- **Issue**: Possible barrel import from lucide-react
- **Fix**: Import specific modules to reduce bundle size

### C:/Users/<USER>/BookScribe/src/components/achievements/achievements-page.tsx:9
- **Issue**: Possible barrel import from lucide-react
- **Fix**: Import specific modules to reduce bundle size

### C:/Users/<USER>/BookScribe/src/components/admin/users-management.tsx:12
- **Issue**: Possible barrel import from lucide-react
- **Fix**: Import specific modules to reduce bundle size

... and 36 more

## Duplicate Import

### Multiple files:0
- **Issue**: react imported 93 times
- **Fix**: Consider creating a shared export

### Multiple files:0
- **Issue**: next/navigation imported 30 times
- **Fix**: Consider creating a shared export

### Multiple files:0
- **Issue**: @/components/ui/button imported 267 times
- **Fix**: Consider creating a shared export

### Multiple files:0
- **Issue**: @/components/ui/card imported 145 times
- **Fix**: Consider creating a shared export

### Multiple files:0
- **Issue**: @/components/ui/tabs imported 68 times
- **Fix**: Consider creating a shared export

### Multiple files:0
- **Issue**: @/components/ui/badge imported 182 times
- **Fix**: Consider creating a shared export

### Multiple files:0
- **Issue**: @/hooks/use-toast imported 22 times
- **Fix**: Consider creating a shared export

### Multiple files:0
- **Issue**: @/contexts/auth-context imported 34 times
- **Fix**: Consider creating a shared export

### Multiple files:0
- **Issue**: @/lib/supabase imported 70 times
- **Fix**: Consider creating a shared export

### Multiple files:0
- **Issue**: next/server imported 111 times
- **Fix**: Consider creating a shared export

... and 84 more

## Optimization Tips

1. **Use dynamic imports** for components only needed on specific routes
2. **Implement code splitting** with Next.js dynamic imports
3. **Tree-shake unused exports** with proper webpack configuration
4. **Use production builds** of libraries (react.production.min.js)
5. **Analyze with @next/bundle-analyzer** for detailed insights
