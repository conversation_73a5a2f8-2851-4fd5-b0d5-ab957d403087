import { But<PERSON> } from "@/components/ui/button";
import { User } from "@supabase/supabase-js";
import Link from "next/link";
import { Feather } from 'lucide-react'
import { LogIn } from 'lucide-react'
import { UserPlus } from 'lucide-react';

interface AuthButtonsProps {
  user: User | null;
}

export function AuthButtons({ user }: AuthButtonsProps) {
  if (user) {
    return (
      <Link href="/dashboard">
        <Button variant="literary" className="font-medium px-4 sm:px-6 lg:px-8">
          Dashboard
          <Feather className="w-4 h-4 ml-1" />
        </Button>
      </Link>
    );
  }

  return (
    <div className="flex items-center gap-2 sm:gap-3">
      <Link href="/login">
        <Button 
          variant="ghost" 
          className="text-foreground/80 hover:text-foreground hover:bg-accent font-medium"
        >
          <LogIn className="w-4 h-4 mr-1" />
          Sign In
        </Button>
      </Link>
      <Link href="/signup">
        <Button variant="literary" className="font-medium px-4 sm:px-6 lg:px-8">
          <UserPlus className="w-4 h-4 mr-1" />
          Sign Up
        </Button>
      </Link>
    </div>
  );
}
