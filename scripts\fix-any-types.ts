#!/usr/bin/env node
import { promises as fs } from 'fs';
import path from 'path';
import { glob } from 'glob';

interface AnyTypeLocation {
  file: string;
  line: number;
  column: number;
  context: string;
  suggestedFix?: string;
}

interface FileAnalysis {
  filePath: string;
  anyTypes: AnyTypeLocation[];
  category: string;
}

// Common type replacements based on context
const TYPE_SUGGESTIONS = {
  // Service types
  'characterService:': 'CharacterService',
  'projectService:': 'ProjectService',
  'analyticsService:': 'WritingAnalyticsService',
  'userService:': 'UserService',
  'emailService:': 'MailerooEmailService',
  
  // Data types
  '.map((p: any)': 'Project',
  '.map((c: any)': 'Character',
  '.map((u: any)': 'User',
  'data: any': 'unknown',
  ': any[]': ': unknown[]',
  
  // Zod validation
  'z.any()': 'z.unknown()',
  
  // Generic objects
  'Record<string, any>': 'Record<string, unknown>',
  ': { [key: string]: any }': ': { [key: string]: unknown }',
};

async function findAnyTypes(filePath: string): Promise<AnyTypeLocation[]> {
  const content = await fs.readFile(filePath, 'utf-8');
  const lines = content.split('\n');
  const anyTypes: AnyTypeLocation[] = [];
  
  // Patterns to find 'any' types
  const patterns = [
    /:\s*any\b/g,                    // : any
    /\bany\s*\[\]/g,                 // any[]
    /\<any\>/g,                      // <any>
    /as\s+any\b/g,                   // as any
    /z\.any\(\)/g,                   // z.any()
    /Record<[^,]+,\s*any>/g,         // Record<string, any>
    /\[\s*key:\s*string\s*\]:\s*any/g, // [key: string]: any
    /\((\w+):\s*any[,\)]/g,          // function params
  ];
  
  lines.forEach((line, lineIndex) => {
    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(line)) !== null) {
        const context = line.trim();
        const suggestedFix = getSuggestedFix(context, match[0]);
        
        anyTypes.push({
          file: filePath,
          line: lineIndex + 1,
          column: match.index + 1,
          context,
          suggestedFix
        });
      }
    });
  });
  
  return anyTypes;
}

function getSuggestedFix(context: string, matchedText: string): string | undefined {
  // Check for direct matches
  for (const [pattern, replacement] of Object.entries(TYPE_SUGGESTIONS)) {
    if (context.includes(pattern)) {
      return replacement;
    }
  }
  
  // Context-based suggestions
  if (context.includes('Service') && context.includes('any')) {
    // Extract service name
    const serviceMatch = context.match(/(\w+Service):\s*any/);
    if (serviceMatch) {
      return serviceMatch[1];
    }
  }
  
  // Generic fallback suggestions
  if (matchedText.includes('any[]')) {
    return 'unknown[]';
  }
  
  if (matchedText.includes(': any')) {
    return 'unknown';
  }
  
  if (matchedText.includes('z.any()')) {
    return 'z.unknown()';
  }
  
  return undefined;
}

function categorizeFile(filePath: string): string {
  if (filePath.includes('/api/')) {
    if (filePath.includes('/analytics/')) return 'Analytics Routes';
    if (filePath.includes('/characters/')) return 'Character Routes';
    if (filePath.includes('/projects/')) return 'Project Routes';
    if (filePath.includes('/story-bible/')) return 'Story Bible Routes';
    if (filePath.includes('/collaboration/')) return 'Collaboration Routes';
    if (filePath.includes('/billing/')) return 'Billing Routes';
    return 'Other API Routes';
  }
  
  if (filePath.includes('/services/')) return 'Services';
  if (filePath.includes('/lib/')) return 'Library';
  if (filePath.includes('/components/')) return 'Components';
  if (filePath.includes('/hooks/')) return 'Hooks';
  
  return 'Other';
}

async function analyzeCodebase(): Promise<Map<string, FileAnalysis[]>> {
  const srcDir = path.join(process.cwd(), 'src');
  const files = await glob('**/*.{ts,tsx}', { 
    cwd: srcDir,
    ignore: ['**/*.d.ts', '**/*.test.ts', '**/*.spec.ts']
  });
  
  const results = new Map<string, FileAnalysis[]>();
  
  for (const file of files) {
    const filePath = path.join(srcDir, file);
    const anyTypes = await findAnyTypes(filePath);
    
    if (anyTypes.length > 0) {
      const category = categorizeFile(file);
      const analysis: FileAnalysis = {
        filePath: file,
        anyTypes,
        category
      };
      
      if (!results.has(category)) {
        results.set(category, []);
      }
      results.get(category)!.push(analysis);
    }
  }
  
  return results;
}

async function generateFixScript(analyses: Map<string, FileAnalysis[]>): Promise<void> {
  const fixes: string[] = ['#!/bin/bash', '', '# Auto-generated fix script for any types', ''];
  
  analyses.forEach((files, category) => {
    fixes.push(`# ${category}`);
    fixes.push('#' + '='.repeat(50));
    
    files.forEach(file => {
      fixes.push(`\n# File: ${file.filePath}`);
      file.anyTypes.forEach(anyType => {
        if (anyType.suggestedFix) {
          fixes.push(`# Line ${anyType.line}: ${anyType.context}`);
          fixes.push(`# Suggested: Replace with '${anyType.suggestedFix}'`);
        }
      });
    });
    
    fixes.push('');
  });
  
  await fs.writeFile('fix-any-types.sh', fixes.join('\n'));
}

async function generateReport(analyses: Map<string, FileAnalysis[]>): Promise<void> {
  console.log('\n=== TypeScript "any" Types Analysis ===\n');
  
  let totalAnyTypes = 0;
  let totalFiles = 0;
  
  // Summary by category
  analyses.forEach((files, category) => {
    const categoryTotal = files.reduce((sum, file) => sum + file.anyTypes.length, 0);
    totalAnyTypes += categoryTotal;
    totalFiles += files.length;
    
    console.log(`${category}: ${categoryTotal} occurrences in ${files.length} files`);
  });
  
  console.log(`\nTotal: ${totalAnyTypes} any types in ${totalFiles} files\n`);
  
  // Detailed breakdown
  console.log('=== Priority Files to Fix ===\n');
  
  // Sort categories by priority
  const priorityOrder = [
    'Analytics Routes',
    'Character Routes', 
    'Project Routes',
    'Services',
    'Other API Routes'
  ];
  
  priorityOrder.forEach(category => {
    if (analyses.has(category)) {
      const files = analyses.get(category)!;
      console.log(`\n${category}:`);
      console.log('=' .repeat(category.length));
      
      // Sort files by number of any types
      files.sort((a, b) => b.anyTypes.length - a.anyTypes.length);
      
      files.slice(0, 5).forEach(file => {
        console.log(`\n${file.filePath} (${file.anyTypes.length} occurrences):`);
        file.anyTypes.slice(0, 3).forEach(anyType => {
          console.log(`  Line ${anyType.line}: ${anyType.context}`);
          if (anyType.suggestedFix) {
            console.log(`    → Suggested: ${anyType.suggestedFix}`);
          }
        });
        
        if (file.anyTypes.length > 3) {
          console.log(`  ... and ${file.anyTypes.length - 3} more`);
        }
      });
    }
  });
  
  // Save detailed JSON report
  const detailedReport = {
    summary: {
      totalFiles,
      totalAnyTypes,
      byCategory: Array.from(analyses.entries()).map(([category, files]) => ({
        category,
        files: files.length,
        anyTypes: files.reduce((sum, file) => sum + file.anyTypes.length, 0)
      }))
    },
    files: Array.from(analyses.values()).flat()
  };
  
  await fs.writeFile(
    'any-types-report.json',
    JSON.stringify(detailedReport, null, 2)
  );
  
  console.log('\n\nDetailed report saved to: any-types-report.json');
  console.log('Fix script generated: fix-any-types.sh');
}

// Main execution
async function main() {
  try {
    console.log('Analyzing codebase for "any" types...');
    const analyses = await analyzeCodebase();
    
    await generateReport(analyses);
    await generateFixScript(analyses);
    
    console.log('\n\n=== Next Steps ===');
    console.log('1. Review the any-types-report.json for detailed analysis');
    console.log('2. Start with high-priority files (Analytics and Character routes)');
    console.log('3. Replace "any" with appropriate types:');
    console.log('   - Use "unknown" for truly unknown types');
    console.log('   - Import proper interfaces for known types');
    console.log('   - Use generics where appropriate');
    console.log('4. Run TypeScript compiler to verify fixes');
    
  } catch (error) {
    console.error('Error analyzing any types:', error);
    process.exit(1);
  }
}

main();