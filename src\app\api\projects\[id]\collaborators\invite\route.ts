import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError, AuthorizationError, NotFoundError } from '@/lib/api/error-handler'
import { createTypedServerClient } from '@/lib/supabase'
import { mailerooEmailService, EmailTemplates } from '@/lib/services/maileroo-email-service'
import { logger } from '@/lib/services/logger'
import crypto from 'crypto'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createTypedServerClient()
    const user = await UnifiedAuthService.authenticateUser(request)
    if (!user) {
      return handleAPIError(new AuthenticationError())
    }

    const { email, role = 'viewer' } = await request.json()

    if (!email || !email.includes('@')) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    if (!['editor', 'viewer'].includes(role)) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    const projectId = params.id

    // Check if user owns the project or has manage_team permission
    const { data: project } = await supabase
      .from('projects')
      .select('id, title, user_id')
      .eq('id', projectId)
      .single()

    if (!project) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    const isOwner = project.user_id === user.id

    if (!isOwner) {
      // Check if user has manage_team permission
      const { data: collaborator } = await supabase
        .from('project_collaborators')
        .select('permissions')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single()

      if (!collaborator || !collaborator.permissions?.can_manage_team) {
        return handleAPIError(new AuthorizationError())
      }
    }

    // Check if user is already a collaborator
    const { data: existingCollaborator } = await supabase
      .from('project_collaborators')
      .select('id, status')
      .eq('project_id', projectId)
      .eq('user_id', email) // We'll need to look up by email
      .single()

    if (existingCollaborator) {
      if (existingCollaborator.status === 'active') {
        return handleAPIError(new ValidationError('Invalid request'))
      } else if (existingCollaborator.status === 'pending') {
        return handleAPIError(new ValidationError('Invalid request'))
      }
    }

    // Check collaborator limit
    const { data: userSubscription } = await supabase
      .from('user_subscriptions')
      .select('tier_id')
      .eq('user_id', project.user_id)
      .eq('status', 'active')
      .single()

    let maxCollaborators = 0
    if (userSubscription) {
      switch (userSubscription.tier_id) {
        case 'professional':
          maxCollaborators = 2
          break
        case 'studio':
          maxCollaborators = 5
          break
      }
    }

    if (maxCollaborators > 0) {
      const { count } = await supabase
        .from('project_collaborators')
        .select('*', { count: 'exact', head: true })
        .eq('project_id', projectId)
        .eq('status', 'active')
        .neq('user_id', project.user_id)

      if ((count || 0) >= maxCollaborators) {
        return NextResponse.json({ 
          error: `Collaborator limit reached. Your plan allows ${maxCollaborators} collaborators.` 
        }, { status: 400 })
      }
    } else {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    // Check if invited email has an account
    const { data: invitedUser } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', email)
      .single()

    // Generate invitation token
    const inviteToken = crypto.randomBytes(32).toString('hex')
    const inviteExpiresAt = new Date()
    inviteExpiresAt.setDate(inviteExpiresAt.getDate() + 7) // 7 days expiry

    // Create invitation
    const { data: invitation, error: inviteError } = await supabase
      .from('project_collaborators')
      .insert({
        project_id: projectId,
        user_id: invitedUser?.id || crypto.randomUUID(), // Placeholder ID if user doesn't exist
        invited_by: user.id,
        role,
        status: 'pending',
        invite_token: inviteToken,
        invite_expires_at: inviteExpiresAt.toISOString()
      })
      .select()
      .single()

    if (inviteError) {
      logger.error('Failed to create invitation', inviteError)
      return NextResponse.json({ error: 'Failed to create invitation' }, { status: 500 })
    }

    // Send invitation email
    const inviteUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invite/${inviteToken}`
    
    try {
      await mailerooEmailService.sendEmail(
        email,
        EmailTemplates.COLLABORATION_INVITE,
        {
          projectTitle: project.title,
          role,
          inviteUrl,
          expiresIn: '7 days'
        }
      )
    } catch (emailError) {
      logger.error('Failed to send invitation email', emailError as Error)
      // Don't fail the request if email fails, invitation is still created
    }

    return NextResponse.json({
      success: true,
      invitation: {
        id: invitation.id,
        email,
        role,
        status: 'pending',
        expires_at: inviteExpiresAt.toISOString()
      }
    })

  } catch (error) {
    logger.error('Error in collaborator invite endpoint', error as Error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
