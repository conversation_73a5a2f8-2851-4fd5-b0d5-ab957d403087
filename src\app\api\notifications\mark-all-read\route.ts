import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { notificationService } from '@/lib/services/notification-service'
import { logger } from '@/lib/services/logger'

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const user = request.user!;

    const response = await notificationService.markNotificationsAsRead(user.id, {
      markAll: true
    });

    if (!response.success) {
      logger.error('Error marking all notifications as read:', response.error);
      return NextResponse.json(
        { error: 'Failed to update notifications' },
        { status: 500 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      updated_count: response.data.updated_count
    });
  } catch (error) {
    logger.error('Error in mark-all-read:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
})