# API Route Authentication Migration Guide

## Before (Multiple patterns)

### Pattern 1: Direct UnifiedAuthService
```typescript
export async function POST(request: NextRequest) {
  const user = await UnifiedAuthService.authenticateUser(request)
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }
  // ... route logic
}
```

### Pattern 2: authenticateUser from auth/server (non-existent)
```typescript
import { authenticateUser } from '@/lib/auth/server'

export async function POST(request: NextRequest) {
  const authResult = await authenticateUser()
  if (!authResult.success) {
    return authResult.response!
  }
  // ... route logic
}
```

### Pattern 3: UnifiedAuthService.withAuth
```typescript
export async function POST(request: NextRequest) {
  return UnifiedAuthService.withAuth(async (req) => {
    const user = req.user!
    // ... route logic
  })(request)
}
```

## After (Standardized pattern)

### Simple user authentication
```typescript
import { withUserAuth } from '@/lib/api/with-api-route'

export const POST = withUserAuth(async (request, { body }) => {
  const user = request.user!
  // ... route logic
  return NextResponse.json({ success: true })
})
```

### With rate limiting and validation
```typescript
import { withUserAuth } from '@/lib/api/with-api-route'
import { z } from 'zod'

const requestSchema = z.object({
  projectId: z.string().uuid(),
  content: z.string()
})

export const POST = withUserAuth(async (request, { body }) => {
  const user = request.user!
  const { projectId, content } = body!
  
  // ... route logic
  
  return NextResponse.json({ success: true })
}, {
  validateBody: requestSchema,
  rateLimit: { type: 'ai-generation', cost: 2 }
})
```

### Project-specific authentication
```typescript
import { withProjectAuth } from '@/lib/api/with-api-route'

export const POST = withProjectAuth(async (request, { params, body }) => {
  const user = request.user!
  const projectId = params.id
  
  // User already has access to this project
  // ... route logic
  
  return NextResponse.json({ success: true })
}, 'id') // 'id' is the param name for project ID
```

### Admin-only routes
```typescript
import { withAdminAuth } from '@/lib/api/with-api-route'

export const POST = withAdminAuth(async (request, { body }) => {
  const admin = request.user!
  
  // ... admin logic
  
  return NextResponse.json({ success: true })
})
```

### Public routes (no auth)
```typescript
import { withPublicRoute } from '@/lib/api/with-api-route'

export const GET = withPublicRoute(async (request) => {
  // No authentication required
  return NextResponse.json({ status: 'ok' })
})
```

## Benefits

1. **Consistent Error Handling**: All routes handle errors the same way
2. **Automatic Rate Limiting**: Just specify the type and cost
3. **Built-in Validation**: Zod schemas validate request bodies
4. **Type Safety**: Full TypeScript support with proper types
5. **Less Boilerplate**: No need to repeat auth checks
6. **Centralized Logic**: Easy to update auth logic in one place