import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'

const putSchema = z.object({
  title: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  content: z.string().optional()
})

export const GET = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: materialId } = await params
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Get material and verify user owns the project
    const { data: material, error } = await supabase
      .from('reference_materials')
      .select('*, projects!inner(user_id)')
      .eq('id', materialId)
      .eq('projects.user_id', user.id)
      .single()

    if (error || !material) {
      return UnifiedResponse.error('Reference material not found', 404)
    }

    // Transform response to match frontend interface
    const formattedMaterial = {
      id: material.id,
      projectId: material.project_id,
      type: material.type,
      title: material.title,
      description: material.description,
      fileUrl: material.file_url,
      fileSize: material.file_size,
      mimeType: material.mime_type,
      content: material.content,
      tags: material.tags || [],
      aiSummary: material.ai_summary,
      createdAt: new Date(material.created_at),
      updatedAt: new Date(material.updated_at),
    }

    return UnifiedResponse.success({ material: formattedMaterial })
  } catch (error) {
    logger.error('Reference GET error:', error)
    return UnifiedResponse.error('Failed to retrieve reference material')
  }
})

export const PUT = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: materialId } = await params
    const body = await request.json()
    const validation = putSchema.safeParse(body)
    
    if (!validation.success) {
      return UnifiedResponse.error('Invalid request data', 400, validation.error.errors)
    }

    const { title, description, tags, content } = validation.data
    const user = request.user!
    const supabase = await createTypedServerClient()

    // First verify user owns the project
    const { data: existingMaterial, error: checkError } = await supabase
      .from('reference_materials')
      .select('project_id, projects!inner(user_id)')
      .eq('id', materialId)
      .eq('projects.user_id', user.id)
      .single()

    if (checkError || !existingMaterial) {
      return UnifiedResponse.error('Reference material not found', 404)
    }

    const updates: Record<string, unknown> = {
      updated_at: new Date().toISOString()
    }
    
    if (title !== undefined) updates.title = title
    if (description !== undefined) updates.description = description
    if (tags !== undefined) updates.tags = tags
    if (content !== undefined) updates.content = content

    const { data: material, error } = await supabase
      .from('reference_materials')
      .update(updates)
      .eq('id', materialId)
      .select()
      .single()

    if (error) {
      logger.error('Failed to update reference material:', error)
      return UnifiedResponse.error('Failed to update reference material')
    }

    // Transform response to match frontend interface
    const formattedMaterial = {
      id: material.id,
      projectId: material.project_id,
      type: material.type,
      title: material.title,
      description: material.description,
      fileUrl: material.file_url,
      fileSize: material.file_size,
      mimeType: material.mime_type,
      content: material.content,
      tags: material.tags || [],
      aiSummary: material.ai_summary,
      createdAt: new Date(material.created_at),
      updatedAt: new Date(material.updated_at),
    }

    return UnifiedResponse.success({ material: formattedMaterial })
  } catch (error) {
    logger.error('Reference PUT error:', error)
    return UnifiedResponse.error('Failed to update reference material')
  }
})

export const DELETE = UnifiedAuthService.withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id: materialId } = await params
    const user = request.user!
    const supabase = await createTypedServerClient()

    // First get the material to check if it has a file to delete and verify ownership
    const { data: material, error: fetchError } = await supabase
      .from('reference_materials')
      .select('file_url, projects!inner(user_id)')
      .eq('id', materialId)
      .eq('projects.user_id', user.id)
      .single()

    if (fetchError || !material) {
      return UnifiedResponse.error('Reference material not found', 404)
    }

    // Delete the file from storage if it exists
    if (material.file_url) {
      try {
        // Extract file path from URL
        const url = new URL(material.file_url)
        const filePath = url.pathname.split('/storage/v1/object/public/project-files/')[1]
        
        if (filePath) {
          await supabase.storage
            .from('project-files')
            .remove([filePath])
        }
      } catch (storageError) {
        logger.warn('Failed to delete file from storage:', storageError)
        // Continue with database deletion even if file deletion fails
      }
    }

    // Delete the database record
    const { error: deleteError } = await supabase
      .from('reference_materials')
      .delete()
      .eq('id', materialId)

    if (deleteError) {
      logger.error('Failed to delete reference material:', deleteError)
      return UnifiedResponse.error('Failed to delete reference material')
    }

    return UnifiedResponse.success({ success: true })
  } catch (error) {
    logger.error('Reference DELETE error:', error)
    return UnifiedResponse.error('Failed to delete reference material')
  }
})