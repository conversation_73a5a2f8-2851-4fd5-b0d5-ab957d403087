'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/components/ui/use-toast'
import { FileText } from 'lucide-react'
import { Upload } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { CheckCircle } from 'lucide-react'
import { FileType } from 'lucide-react'
import { Book } from 'lucide-react'
import { File } from 'lucide-react'

interface ImportPanelProps {
  projectId: string
  onImportComplete?: () => void
}

interface ImportResult {
  chaptersImported: number
  totalWordCount: number
  chapters: Array<{
    id: string
    title: string
    chapterNumber: number
    wordCount: number
  }>
}

const supportedFormats = {
  docx: {
    name: 'Word Document',
    icon: FileType,
    accept: '.docx',
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    description: 'Import from Microsoft Word documents',
    endpoint: '/api/import/docx'
  },
  pdf: {
    name: 'PDF',
    icon: File,
    accept: '.pdf',
    mimeType: 'application/pdf',
    description: 'Extract text from PDF files',
    endpoint: '/api/import/pdf'
  },
  epub: {
    name: 'EPUB',
    icon: Book,
    accept: '.epub',
    mimeType: 'application/epub+zip',
    description: 'Import from EPUB e-books',
    endpoint: '/api/import/epub'
  },
  txt: {
    name: 'Plain Text',
    icon: FileText,
    accept: '.txt',
    mimeType: 'text/plain',
    description: 'Import plain text files',
    endpoint: '/api/import/txt'
  }
}

export function ImportPanel({ projectId, onImportComplete }: ImportPanelProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [parseChapters, setParseChapters] = useState(true)
  const [isImporting, setIsImporting] = useState(false)
  const [importProgress, setImportProgress] = useState(0)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const { toast } = useToast()

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      const extension = file.name.split('.').pop()?.toLowerCase()
      if (!extension || !supportedFormats[extension as keyof typeof supportedFormats]) {
        toast({
          title: 'Unsupported file type',
          description: 'Please select a DOCX, PDF, EPUB, or TXT file.',
          variant: 'destructive'
        })
        return
      }
      setSelectedFile(file)
      setImportResult(null)
    }
  }

  const handleImport = async () => {
    if (!selectedFile) return

    setIsImporting(true)
    setImportProgress(0)

    try {
      const extension = selectedFile.name.split('.').pop()?.toLowerCase() as keyof typeof supportedFormats
      const format = supportedFormats[extension]
      
      if (!format) {
        throw new Error('Unsupported file format')
      }

      // For TXT files, we'll handle them differently
      if (extension === 'txt') {
        await handleTextImport()
        return
      }

      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('projectId', projectId)
      formData.append('parseChapters', parseChapters.toString())

      // Simulate progress
      const progressInterval = setInterval(() => {
        setImportProgress(prev => Math.min(prev + 10, 90))
      }, 500)

      const response = await fetch(format.endpoint, {
        method: 'POST',
        body: formData
      })

      clearInterval(progressInterval)

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Import failed')
      }

      const result = await response.json()
      setImportProgress(100)
      setImportResult(result)

      toast({
        title: 'Import successful',
        description: `Imported ${result.chaptersImported} chapters with ${result.totalWordCount.toLocaleString()} words.`
      })

      if (onImportComplete) {
        onImportComplete()
      }
    } catch (error) {
      toast({
        title: 'Import failed',
        description: error instanceof Error ? error.message : 'Please try again later.',
        variant: 'destructive'
      })
    } finally {
      setIsImporting(false)
      setImportProgress(0)
    }
  }

  const handleTextImport = async () => {
    if (!selectedFile) return

    try {
      const text = await selectedFile.text()
      const supabase = (await import('@/lib/supabase')).createClient()
      
      // Parse chapters from text
      const chapters = parseChapters ? parseChaptersFromText(text) : [{
        title: selectedFile.name.replace('.txt', ''),
        content: text,
        wordCount: text.split(/\s+/).filter(word => word.length > 0).length,
        chapterNumber: 1
      }]

      // Create chapters in database
      const createdChapters = []
      for (const chapter of chapters) {
        const { data, error } = await supabase
          .from('chapters')
          .insert({
            project_id: projectId,
            chapter_number: chapter.chapterNumber,
            title: chapter.title,
            content: chapter.content,
            word_count: chapter.wordCount,
            status: 'draft'
          })
          .select()
          .single()

        if (!error && data) {
          createdChapters.push(data)
        }
      }

      const totalWordCount = chapters.reduce((sum, ch) => sum + ch.wordCount, 0)
      
      setImportProgress(100)
      setImportResult({
        chaptersImported: createdChapters.length,
        totalWordCount,
        chapters: createdChapters.map(ch => ({
          id: ch.id,
          title: ch.title,
          chapterNumber: ch.chapter_number,
          wordCount: ch.word_count
        }))
      })

      toast({
        title: 'Import successful',
        description: `Imported ${createdChapters.length} chapters with ${totalWordCount.toLocaleString()} words.`
      })

      if (onImportComplete) {
        onImportComplete()
      }
    } catch (error) {
      toast({
        title: 'Import failed',
        description: error instanceof Error ? error.message : 'Please try again later.',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Import Document</CardTitle>
          <CardDescription>
            Import chapters from Word documents, PDFs, EPUBs, or text files
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* File format info */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {Object.entries(supportedFormats).map(([key, format]) => {
              const Icon = format.icon
              return (
                <div 
                  key={key}
                  className="flex flex-col items-center p-3 border rounded-lg text-center"
                >
                  <Icon className="h-8 w-8 mb-2 text-muted-foreground" />
                  <p className="text-sm font-medium">{format.name}</p>
                  <p className="text-xs text-muted-foreground">{format.accept}</p>
                </div>
              )
            })}
          </div>

          {/* File input */}
          <div className="space-y-2">
            <Label htmlFor="file-upload">Select file to import</Label>
            <Input
              id="file-upload"
              type="file"
              accept=".docx,.pdf,.epub,.txt"
              onChange={handleFileSelect}
              disabled={isImporting}
            />
          </div>

          {/* Chapter parsing option */}
          <div className="flex items-center space-x-2">
            <Switch
              id="parse-chapters"
              checked={parseChapters}
              onCheckedChange={setParseChapters}
              disabled={isImporting}
            />
            <Label htmlFor="parse-chapters">
              Automatically detect and split chapters
            </Label>
          </div>

          {/* Selected file info */}
          {selectedFile && (
            <Alert>
              <FileText className="h-4 w-4" />
              <AlertDescription>
                <strong>Selected:</strong> {selectedFile.name} 
                ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
              </AlertDescription>
            </Alert>
          )}

          {/* Import progress */}
          {isImporting && (
            <div className="space-y-2">
              <Progress value={importProgress} />
              <p className="text-sm text-muted-foreground text-center">
                Importing... {importProgress}%
              </p>
            </div>
          )}

          {/* Import results */}
          {importResult && (
            <Alert className="border-success">
              <CheckCircle className="h-4 w-4 text-success" />
              <AlertDescription>
                <div className="space-y-2">
                  <p>
                    <strong>Import complete!</strong>
                  </p>
                  <ul className="text-sm space-y-1">
                    <li>Chapters imported: {importResult.chaptersImported}</li>
                    <li>Total words: {importResult.totalWordCount.toLocaleString()}</li>
                  </ul>
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm font-medium">
                      View imported chapters
                    </summary>
                    <ul className="mt-2 space-y-1 text-sm">
                      {importResult.chapters.map(ch => (
                        <li key={ch.id}>
                          {ch.chapterNumber}. {ch.title} ({ch.wordCount.toLocaleString()} words)
                        </li>
                      ))}
                    </ul>
                  </details>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Import button */}
          <Button 
            onClick={handleImport} 
            disabled={!selectedFile || isImporting}
            className="w-full"
          >
            <Upload className="h-4 w-4 mr-2" />
            {isImporting ? 'Importing...' : 'Import Document'}
          </Button>

          {/* Help text */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Tips:</strong>
              <ul className="text-sm mt-1 space-y-1">
                <li>• For best results, use clear chapter headings like "Chapter 1" or "CHAPTER ONE"</li>
                <li>• PDF text extraction may vary based on the PDF structure</li>
                <li>• Large files may take longer to process</li>
              </ul>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  )
}

function parseChaptersFromText(text: string): Array<{
  title: string
  content: string
  wordCount: number
  chapterNumber: number
}> {
  const chapters: Array<{
    title: string
    content: string
    wordCount: number
    chapterNumber: number
  }> = []
  
  // Common chapter patterns
  const chapterPatterns = [
    /^Chapter\s+(\d+)(?:\s*[-:]\s*(.*))?$/im,
    /^CHAPTER\s+(\d+)(?:\s*[-:]\s*(.*))?$/im,
    /^Chapter\s+([A-Z]+)(?:\s*[-:]\s*(.*))?$/im,
    /^(\d+)\.?\s+(.*)$/m,
    /^Part\s+(\d+)(?:\s*[-:]\s*(.*))?$/im
  ]

  // Split text by chapter markers
  let currentChapter: typeof chapters[0] | null = null
  const lines = text.split('\n')
  let chapterNumber = 0

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]?.trim() || ''
    let isChapterStart = false
    let title = ''

    // Check if line matches any chapter pattern
    for (const pattern of chapterPatterns) {
      const match = line.match(pattern)
      if (match) {
        isChapterStart = true
        chapterNumber++
        title = match[2] || `Chapter ${chapterNumber}`
        break
      }
    }

    // Also check for lines that are all caps and short (likely chapter titles)
    if (!isChapterStart && line.length > 0 && line.length < 50 && line === line.toUpperCase()) {
      isChapterStart = true
      chapterNumber++
      title = line
    }

    if (isChapterStart) {
      // Save previous chapter if exists
      if (currentChapter && currentChapter.content.trim()) {
        chapters.push(currentChapter)
      }

      // Start new chapter
      currentChapter = {
        title: title.trim(),
        content: '',
        wordCount: 0,
        chapterNumber
      }
    } else if (currentChapter) {
      // Add line to current chapter
      currentChapter.content += line + '\n'
    } else if (!currentChapter && line.trim()) {
      // No chapter marker found yet, create a default first chapter
      chapterNumber = 1
      currentChapter = {
        title: 'Chapter 1',
        content: line + '\n',
        wordCount: 0,
        chapterNumber
      }
    }
  }

  // Add the last chapter
  if (currentChapter && currentChapter.content.trim()) {
    chapters.push(currentChapter)
  }

  // Calculate word counts
  for (const chapter of chapters) {
    chapter.content = chapter.content.trim()
    chapter.wordCount = chapter.content.split(/\s+/).filter(word => word.length > 0).length
  }

  // If no chapters found, return the entire text as one chapter
  if (chapters.length === 0) {
    chapters.push({
      title: 'Imported Content',
      content: text,
      wordCount: text.split(/\s+/).filter(word => word.length > 0).length,
      chapterNumber: 1
    })
  }

  return chapters
}