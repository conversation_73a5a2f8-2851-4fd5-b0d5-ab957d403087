"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Scroll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Globe } from 'lucide-react'
import { Network } from 'lucide-react'
import { BookOpen } from 'lucide-react'
import { Users } from 'lucide-react'
import { MapPin } from 'lucide-react'
import { Clock } from 'lucide-react'
import { Zap } from 'lucide-react'
import { Crown } from 'lucide-react'
import { Sword } from 'lucide-react'
import { Shield } from 'lucide-react'
import { Heart } from 'lucide-react'
import { Star } from 'lucide-react'
import { <PERSON><PERSON><PERSON> } from 'lucide-react'
import { Mountain } from 'lucide-react'
import { <PERSON> } from 'lucide-react'
import { Home } from 'lucide-react'
import { Sparkles } from 'lucide-react'
import { Eye } from 'lucide-react'
import { Plus } from 'lucide-react'
import { Edit } from 'lucide-react'
import { Link } from 'lucide-react'
import { Share } from 'lucide-react'
import { Search } from 'lucide-react'
import { Filter } from 'lucide-react'
import { Layers } from 'lucide-react'
import { GitBranch } from 'lucide-react'
import { Database } from 'lucide-react'
import { Settings } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { CheckCircle } from 'lucide-react';

const universes = [
  {
    id: 1,
    name: "Aethermoor Saga",
    status: "Active",
    books: 3,
    characters: 47,
    locations: 23,
    plotThreads: 12,
    lastUpdated: "2 hours ago",
    description: "Epic fantasy realm of crystal magic and ancient mysteries",
    genre: "High Fantasy",
    tone: "Epic, Mystical",
    themes: ["Power & Responsibility", "Ancient Mysteries", "Friendship"],
    consistency: 94
  },
  {
    id: 2,
    name: "Neon Shadows",
    status: "Planning",
    books: 1,
    characters: 12,
    locations: 8,
    plotThreads: 5,
    lastUpdated: "1 week ago",
    description: "Cyberpunk thriller in a dystopian future city",
    genre: "Cyberpunk",
    tone: "Dark, Gritty",
    themes: ["Technology vs Humanity", "Corporate Control", "Identity"],
    consistency: 87
  },
  {
    id: 3,
    name: "Starbound Chronicles",
    status: "Draft",
    books: 2,
    characters: 31,
    locations: 15,
    plotThreads: 8,
    lastUpdated: "3 days ago",
    description: "Space opera spanning multiple star systems",
    genre: "Space Opera",
    tone: "Adventurous, Hopeful",
    themes: ["Exploration", "Unity in Diversity", "Legacy"],
    consistency: 91
  }
];

const crossReferences = [
  {
    type: "Character Appearance",
    from: "Aethermoor Saga - Book 1",
    to: "Aethermoor Saga - Book 3",
    element: "Marcus Stormwind",
    consistency: "Verified",
    notes: "Age progression and character development tracked"
  },
  {
    type: "Location Reference",
    from: "Aethermoor Saga - Book 2",
    to: "Aethermoor Saga - Book 1",
    element: "Crystal Chamber",
    consistency: "Minor Issue",
    notes: "Chamber size description varies slightly"
  },
  {
    type: "Magic System",
    from: "Aethermoor Saga - Book 1",
    to: "Aethermoor Saga - Book 2",
    element: "Crystal Resonance Rules",
    consistency: "Verified",
    notes: "Rules consistently applied across books"
  },
  {
    type: "Timeline Event",
    from: "Aethermoor Saga - Book 2",
    to: "Aethermoor Saga - Book 3",
    element: "The Great Convergence",
    consistency: "Verified",
    notes: "Timeline and consequences properly tracked"
  }
];

const sharedElements = [
  {
    name: "Crystal Magic System",
    type: "Magic System",
    usedIn: ["Book 1", "Book 2", "Book 3"],
    lastModified: "1 day ago",
    consistency: 96,
    description: "Core magic system based on crystal resonance and emotional connection"
  },
  {
    name: "Aethermoor Realm",
    type: "World Setting",
    usedIn: ["Book 1", "Book 2", "Book 3"],
    lastModified: "3 days ago",
    consistency: 94,
    description: "Primary world setting with established geography and cultures"
  },
  {
    name: "Ancient Language",
    type: "Language System",
    usedIn: ["Book 1", "Book 3"],
    lastModified: "1 week ago",
    consistency: 89,
    description: "Mystical language used in spells and ancient texts"
  },
  {
    name: "Political Structure",
    type: "Governance",
    usedIn: ["Book 2", "Book 3"],
    lastModified: "2 days ago",
    consistency: 92,
    description: "Kingdom hierarchies and inter-realm relationships"
  }
];

const timelineEvents = [
  { year: "Age of Creation", event: "Formation of Aethermoor", books: ["Book 1"], importance: "Critical" },
  { year: "First Era", event: "Rise of Crystal Mages", books: ["Book 1", "Book 2"], importance: "High" },
  { year: "Second Era", event: "The Great War", books: ["Book 2"], importance: "High" },
  { year: "Third Era", event: "Shadow King's Emergence", books: ["Book 1", "Book 3"], importance: "Critical" },
  { year: "Current Era", event: "Aria's Journey Begins", books: ["Book 1", "Book 2", "Book 3"], importance: "Critical" }
];

export function DemoUniverseEnhanced() {
  const [selectedUniverse, setSelectedUniverse] = useState<number | null>(1);
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedElement, setSelectedElement] = useState<string | null>(null);

  const currentUniverse = universes.find(u => u.id === selectedUniverse);

  return (
    <div className="w-full h-full bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card/50 backdrop-blur-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
            <div className="flex items-center gap-2">
              <Globe className="w-6 h-6 text-primary" />
              <h2 className="text-2xl font-bold">Universe Management System</h2>
              <Badge variant="outline" className="border-primary/50 text-primary">
                Multi-Series
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Search className="w-4 h-4 mr-2" />
              Search
            </Button>
            <Button variant="outline" size="sm">
              <Link className="w-4 h-4 mr-2" />
              Cross-Reference
            </Button>
            <Button variant="outline" size="sm">
              <Plus className="w-4 h-4 mr-2" />
              New Universe
            </Button>
          </div>
        </div>

        <p className="text-muted-foreground mt-2 max-w-3xl">
          Manage complex story universes across multiple books and series. Track consistency, 
          cross-references, and shared elements to maintain perfect continuity.
        </p>
      </div>

      <div className="flex h-[calc(100vh-200px)]">
        {/* Universe Sidebar */}
        <div className="w-80 border-r border-border bg-card/30">
          <div className="p-4">
            <h3 className="font-semibold mb-4 flex items-center gap-2">
              <Database className="w-4 h-4" />
              Your Universes ({universes.length})
            </h3>
            
            <div className="space-y-3">
              {universes.map((universe) => (
                <Card 
                  key={universe.id}
                  className={`cursor-pointer transition-all duration-300 ${
                    selectedUniverse === universe.id 
                      ? 'border-primary/50 bg-primary/10 shadow-lg' 
                      : 'hover:border-primary/30'
                  }`}
                  onClick={() => setSelectedUniverse(universe.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{universe.name}</h4>
                      <Badge variant={
                        universe.status === 'Active' ? 'default' :
                        universe.status === 'Planning' ? 'secondary' : 'outline'
                      }>
                        {universe.status}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-3">{universe.description}</p>
                    
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="flex items-center gap-1">
                        <BookOpen className="w-3 h-3" />
                        <span>{universe.books} books</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="w-3 h-3" />
                        <span>{universe.characters} chars</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        <span>{universe.locations} places</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <GitBranch className="w-3 h-3" />
                        <span>{universe.plotThreads} threads</span>
                      </div>
                    </div>
                    
                    <div className="mt-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs font-medium">Consistency</span>
                        <span className="text-xs text-muted-foreground">{universe.consistency}%</span>
                      </div>
                      <Progress value={universe.consistency} className="h-1" />
                    </div>
                    
                    <div className="text-xs text-muted-foreground mt-2">
                      Updated {universe.lastUpdated}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {currentUniverse ? (
            <>
              {/* Universe Header */}
              <div className="border-b border-border p-6 bg-card/20">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold">{currentUniverse.name}</h2>
                    <p className="text-muted-foreground">{currentUniverse.description}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Edit className="w-4 h-4 mr-2" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      <Share className="w-4 h-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
                
                <div className="mt-4 flex flex-wrap gap-2">
                  <Badge variant="outline">{currentUniverse.genre}</Badge>
                  <Badge variant="outline">{currentUniverse.tone}</Badge>
                  {currentUniverse.themes.map((theme, idx) => (
                    <Badge key={idx} variant="secondary" className="text-xs">
                      {theme}
                    </Badge>
                  ))}
                </div>
              </div>

              <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
                <div className="border-b border-border px-4 sm:px-6 lg:px-8">
                  <TabsList className="grid w-full max-w-2xl lg:max-w-3xl xl:max-w-4xl grid-cols-5">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="elements">Elements</TabsTrigger>
                    <TabsTrigger value="timeline">Timeline</TabsTrigger>
                    <TabsTrigger value="references">References</TabsTrigger>
                    <TabsTrigger value="consistency">Consistency</TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="overview" className="flex-1 p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="lg:col-span-2 space-y-6">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <BookOpen className="w-5 h-5" />
                            Books in Universe
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {["The Crystal Awakening", "Shadows of the Past", "The Final Convergence"].map((book, idx) => (
                              <div key={idx} className="flex items-center justify-between p-3 rounded border">
                                <div>
                                  <h4 className="font-medium">Book {idx + 1}: {book}</h4>
                                  <p className="text-sm text-muted-foreground">
                                    {idx === 0 ? "Complete" : idx === 1 ? "In Progress" : "Planned"}
                                  </p>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge variant={idx === 0 ? 'default' : idx === 1 ? 'secondary' : 'outline'}>
                                    {idx === 0 ? "Published" : idx === 1 ? "Draft" : "Outline"}
                                  </Badge>
                                  <Button variant="ghost" size="sm">
                                    <Eye className="w-4 h-4" />
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Network className="w-5 h-5" />
                            Universe Statistics
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-5 lg:gap-6">
                            <div className="text-center">
                              <div className="text-2xl font-bold text-primary">{currentUniverse.characters}</div>
                              <div className="text-sm text-muted-foreground">Characters</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-green-500">{currentUniverse.locations}</div>
                              <div className="text-sm text-muted-foreground">Locations</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-blue-500">{currentUniverse.plotThreads}</div>
                              <div className="text-sm text-muted-foreground">Plot Threads</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-purple-500">{currentUniverse.consistency}%</div>
                              <div className="text-sm text-muted-foreground">Consistency</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    <div className="space-y-6">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Clock className="w-5 h-5" />
                            Recent Activity
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="flex items-start gap-3">
                            <div className="w-2 h-2 rounded-full bg-primary mt-2"></div>
                            <div>
                              <p className="text-sm">Updated Crystal Magic System rules</p>
                              <p className="text-xs text-muted-foreground">2 hours ago</p>
                            </div>
                          </div>
                          <div className="flex items-start gap-3">
                            <div className="w-2 h-2 rounded-full bg-green-500 mt-2"></div>
                            <div>
                              <p className="text-sm">Added new character: Zara Nightblade</p>
                              <p className="text-xs text-muted-foreground">1 day ago</p>
                            </div>
                          </div>
                          <div className="flex items-start gap-3">
                            <div className="w-2 h-2 rounded-full bg-blue-500 mt-2"></div>
                            <div>
                              <p className="text-sm">Cross-referenced timeline events</p>
                              <p className="text-xs text-muted-foreground">3 days ago</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Star className="w-5 h-5" />
                            Quick Actions
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <Button variant="outline" size="sm" className="w-full justify-start">
                            <Plus className="w-4 h-4 mr-2" />
                            Add Character
                          </Button>
                          <Button variant="outline" size="sm" className="w-full justify-start">
                            <MapPin className="w-4 h-4 mr-2" />
                            Add Location
                          </Button>
                          <Button variant="outline" size="sm" className="w-full justify-start">
                            <GitBranch className="w-4 h-4 mr-2" />
                            Add Plot Thread
                          </Button>
                          <Button variant="outline" size="sm" className="w-full justify-start">
                            <Link className="w-4 h-4 mr-2" />
                            Cross-Reference
                          </Button>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="elements" className="flex-1 p-6">
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold">Shared Universe Elements</h3>
                      <Button variant="outline" size="sm">
                        <Plus className="w-4 h-4 mr-2" />
                        Add Element
                      </Button>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {sharedElements.map((element, idx) => (
                        <Card 
                          key={idx}
                          className={`cursor-pointer transition-all duration-300 hover:scale-105 ${
                            selectedElement === element.name ? 'border-primary/50 bg-primary/10' : 'hover:border-primary/30'
                          }`}
                          onClick={() => setSelectedElement(selectedElement === element.name ? null : element.name)}
                        >
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <div className="p-2 rounded-lg bg-primary/20">
                                  {element.type === 'Magic System' && <Sparkles className="w-4 h-4 text-primary" />}
                                  {element.type === 'World Setting' && <Globe className="w-4 h-4 text-green-500" />}
                                  {element.type === 'Language System' && <BookOpen className="w-4 h-4 text-blue-500" />}
                                  {element.type === 'Governance' && <Crown className="w-4 h-4 text-purple-500" />}
                                </div>
                                <div>
                                  <CardTitle className="text-base">{element.name}</CardTitle>
                                  <p className="text-sm text-muted-foreground">{element.type}</p>
                                </div>
                              </div>
                            </div>
                          </CardHeader>

                          <CardContent className="space-y-3">
                            <p className="text-sm text-muted-foreground">{element.description}</p>
                            
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-xs font-medium">Consistency</span>
                                <span className="text-xs text-muted-foreground">{element.consistency}%</span>
                              </div>
                              <Progress value={element.consistency} className="h-1" />
                            </div>

                            <div className="flex flex-wrap gap-1">
                              {element.usedIn.map((book, bookIdx) => (
                                <Badge key={bookIdx} variant="outline" className="text-xs">
                                  {book}
                                </Badge>
                              ))}
                            </div>

                            <div className="text-xs text-muted-foreground">
                              Last modified: {element.lastModified}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="timeline" className="flex-1 p-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Clock className="w-5 h-5" />
                        Universe Timeline
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div className="relative">
                          <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-border"></div>
                          
                          {timelineEvents.map((event, idx) => (
                            <div key={idx} className="relative flex items-start gap-4 sm:gap-5 lg:gap-6">
                              <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center ${
                                event.importance === 'Critical' ? 'bg-red-500 border-red-500' :
                                event.importance === 'High' ? 'bg-primary border-primary' :
                                'bg-background border-border'
                              }`}>
                                <Clock className={`w-4 h-4 ${
                                  event.importance === 'Critical' ? 'text-white' :
                                  event.importance === 'High' ? 'text-white' :
                                  'text-muted-foreground'
                                }`} />
                              </div>
                              <div className="flex-1 pb-6">
                                <div className="flex items-center justify-between mb-2">
                                  <h4 className="font-medium">{event.event}</h4>
                                  <Badge variant={
                                    event.importance === 'Critical' ? 'destructive' :
                                    event.importance === 'High' ? 'default' : 'secondary'
                                  }>
                                    {event.importance}
                                  </Badge>
                                </div>
                                <p className="text-sm text-muted-foreground mb-2">{event.year}</p>
                                <div className="flex flex-wrap gap-1">
                                  {event.books.map((book, bookIdx) => (
                                    <Badge key={bookIdx} variant="outline" className="text-xs">
                                      {book}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="references" className="flex-1 p-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Link className="w-5 h-5" />
                        Cross-References & Consistency Checks
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {crossReferences.map((ref, idx) => (
                          <div key={idx} className="p-4 rounded border">
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-2">
                                <Badge variant="outline">{ref.type}</Badge>
                                <span className="font-medium">{ref.element}</span>
                              </div>
                              <Badge variant={
                                ref.consistency === 'Verified' ? 'default' :
                                ref.consistency === 'Minor Issue' ? 'secondary' : 'destructive'
                              }>
                                {ref.consistency}
                              </Badge>
                            </div>
                            
                            <div className="text-sm text-muted-foreground mb-2">
                              <span className="font-medium">From:</span> {ref.from} → <span className="font-medium">To:</span> {ref.to}
                            </div>
                            
                            <p className="text-sm">{ref.notes}</p>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="consistency" className="flex-1 p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Shield className="w-5 h-5" />
                          Consistency Score
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="text-center">
                          <div className="text-4xl font-bold text-primary mb-2">{currentUniverse.consistency}%</div>
                          <p className="text-muted-foreground">Overall Universe Consistency</p>
                        </div>
                        
                        <div className="space-y-3">
                          <div>
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm">Character Consistency</span>
                              <span className="text-sm text-muted-foreground">96%</span>
                            </div>
                            <Progress value={96} className="h-2" />
                          </div>
                          
                          <div>
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm">World Building</span>
                              <span className="text-sm text-muted-foreground">94%</span>
                            </div>
                            <Progress value={94} className="h-2" />
                          </div>
                          
                          <div>
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm">Timeline Accuracy</span>
                              <span className="text-sm text-muted-foreground">92%</span>
                            </div>
                            <Progress value={92} className="h-2" />
                          </div>
                          
                          <div>
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm">Magic System</span>
                              <span className="text-sm text-muted-foreground">98%</span>
                            </div>
                            <Progress value={98} className="h-2" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Settings className="w-5 h-5" />
                          Consistency Issues
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="p-3 rounded border border-yellow-500/50 bg-yellow-500/10">
                            <div className="flex items-center gap-2 mb-1">
                              <AlertCircle className="w-4 h-4 text-yellow-500" />
                              <span className="text-sm font-medium">Minor Issue</span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Crystal Chamber size description varies between Book 1 and Book 2
                            </p>
                          </div>
                          
                          <div className="p-3 rounded border border-green-500/50 bg-green-500/10">
                            <div className="flex items-center gap-2 mb-1">
                              <CheckCircle className="w-4 h-4 text-green-500" />
                              <span className="text-sm font-medium">Resolved</span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Character age progression verified across all books
                            </p>
                          </div>
                          
                          <div className="p-3 rounded border border-green-500/50 bg-green-500/10">
                            <div className="flex items-center gap-2 mb-1">
                              <CheckCircle className="w-4 h-4 text-green-500" />
                              <span className="text-sm font-medium">Verified</span>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Magic system rules consistently applied
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-muted-foreground">
                <Globe className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg">Select a universe to manage</p>
                <p className="text-sm">Choose from your existing universes or create a new one</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
