#!/usr/bin/env node
import { readdir, readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface FileChange {
  file: string;
  changes: number;
  details: string[];
}

async function findAPIRoutes(dir: string): Promise<string[]> {
  const files: string[] = [];
  
  async function walk(currentDir: string) {
    const entries = await readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        if (!['node_modules', '.next', 'dist', '.git'].includes(entry.name)) {
          await walk(fullPath);
        }
      } else if (entry.isFile() && entry.name === 'route.ts') {
        files.push(fullPath);
      }
    }
  }
  
  await walk(dir);
  return files;
}

async function fixFile(filePath: string): Promise<FileChange> {
  let content = await readFile(filePath, 'utf-8');
  const originalContent = content;
  const details: string[] = [];
  
  // Fix 1: Add missing imports
  const hasUnifiedAuthImport = content.includes("import { UnifiedAuthService");
  const hasAuthErrorImport = content.includes("AuthenticationError");
  const needsAuth = content.includes("authenticateUser") || content.includes("supabase.auth.getUser()");
  
  if (needsAuth && !hasUnifiedAuthImport) {
    // Find the last import statement
    const importMatch = content.match(/import[^;]+from\s+['"][^'"]+['"]\s*\n/g);
    if (importMatch) {
      const lastImport = importMatch[importMatch.length - 1];
      const lastImportIndex = content.lastIndexOf(lastImport);
      const insertIndex = lastImportIndex + lastImport.length;
      
      content = content.slice(0, insertIndex) + 
        "import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'\n" +
        content.slice(insertIndex);
      details.push("Added UnifiedAuthService import");
    }
  }
  
  // Fix 2: Add AuthenticationError to existing error-handler import
  if (needsAuth && !hasAuthErrorImport && content.includes('@/lib/api/error-handler')) {
    content = content.replace(
      /import\s*{\s*([^}]+)\s*}\s*from\s*['"]@\/lib\/api\/error-handler['"]/,
      (match, imports) => {
        const importList = imports.split(',').map((i: string) => i.trim());
        if (!importList.includes('AuthenticationError')) {
          importList.push('AuthenticationError');
        }
        return `import { ${importList.join(', ')} } from '@/lib/api/error-handler'`;
      }
    );
    details.push("Added AuthenticationError to error-handler import");
  }
  
  // Fix 3: Replace supabase.auth.getUser() pattern
  const supabaseAuthPattern = /const\s*{\s*data:\s*{\s*user\s*}(?:,\s*error:\s*\w+)?\s*}\s*=\s*await\s+supabase\.auth\.getUser\(\)/g;
  if (supabaseAuthPattern.test(content)) {
    content = content.replace(
      supabaseAuthPattern,
      `const user = await UnifiedAuthService.authenticateUser(request);\n    if (!user) {\n      return handleAPIError(new AuthenticationError());\n    }`
    );
    details.push("Replaced supabase.auth.getUser() with UnifiedAuthService");
    
    // Remove the old error check if it exists
    content = content.replace(
      /\s*if\s*\(\s*(?:authError|\w+Error)\s*\|\|\s*!user\s*\)\s*{\s*return\s+(?:NextResponse\.json|handleAPIError)[^}]+}\s*/g,
      ''
    );
  }
  
  // Fix 4: Replace undefined authenticateUser() calls
  const undefinedAuthPattern = /const\s+authResult\s*=\s*await\s+authenticateUser\(\)/g;
  if (undefinedAuthPattern.test(content)) {
    content = content.replace(
      undefinedAuthPattern,
      'const user = await UnifiedAuthService.authenticateUser(request)'
    );
    
    // Also fix the error handling
    content = content.replace(
      /if\s*\(\s*!authResult\.success\s*\)\s*{\s*return\s+authResult\.response!?;\s*}/g,
      'if (!user) {\n      return handleAPIError(new AuthenticationError());\n    }'
    );
    
    // Fix references to authResult.user
    content = content.replace(/authResult\.user\??\./g, 'user.');
    content = content.replace(/authResult\.user/g, 'user');
    
    details.push("Fixed undefined authenticateUser() pattern");
  }
  
  // Fix 5: Add missing error handling after UnifiedAuthService.authenticateUser
  const authCallPattern = /const\s+user\s*=\s*await\s+UnifiedAuthService\.authenticateUser\(request\)\s*\n(?!\s*if\s*\(!user\))/g;
  if (authCallPattern.test(content)) {
    content = content.replace(
      authCallPattern,
      `const user = await UnifiedAuthService.authenticateUser(request);\n    if (!user) {\n      return handleAPIError(new AuthenticationError());\n    }\n`
    );
    details.push("Added missing error handling after authentication");
  }
  
  // Fix 6: Remove duplicate auth checks
  content = content.replace(
    /const\s+user\s*=\s*await\s+UnifiedAuthService\.authenticateUser\(request\);\s*if\s*\(!user\)\s*{\s*return\s+handleAPIError\(new\s+AuthenticationError\(\)\);\s*}\s*if\s*\(!user\)\s*{\s*return\s+handleAPIError\(new\s+AuthenticationError\(\)\);\s*}/g,
    `const user = await UnifiedAuthService.authenticateUser(request);\n    if (!user) {\n      return handleAPIError(new AuthenticationError());\n    }`
  );
  
  const changes = content !== originalContent ? 1 : 0;
  
  if (changes > 0) {
    await writeFile(filePath, content);
  }
  
  return {
    file: filePath,
    changes,
    details
  };
}

async function main() {
  console.log('🔧 Auto-fixing API authentication patterns...\n');
  
  const apiDir = join(__dirname, '..', 'src', 'app', 'api');
  const routes = await findAPIRoutes(apiDir);
  
  console.log(`Found ${routes.length} API route files\n`);
  
  const results: FileChange[] = [];
  let totalFixed = 0;
  
  for (const route of routes) {
    const result = await fixFile(route);
    results.push(result);
    
    if (result.changes > 0) {
      console.log(`✅ Fixed: ${route.replace(/\\/g, '/')}`);
      result.details.forEach(detail => console.log(`   - ${detail}`));
      totalFixed++;
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`- Total files processed: ${routes.length}`);
  console.log(`- Files fixed: ${totalFixed}`);
  console.log(`- Files already correct: ${routes.length - totalFixed}`);
  
  // Generate detailed report
  const reportPath = join(__dirname, '..', 'api-auth-fixes-applied.md');
  let report = '# API Authentication Fixes Applied\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  report += `Total files fixed: ${totalFixed}\n\n`;
  
  const fixedFiles = results.filter(r => r.changes > 0);
  if (fixedFiles.length > 0) {
    report += '## Files Fixed\n\n';
    for (const file of fixedFiles) {
      report += `### ${file.file.replace(/\\/g, '/')}\n\n`;
      file.details.forEach(detail => {
        report += `- ${detail}\n`;
      });
      report += '\n';
    }
  }
  
  await writeFile(reportPath, report);
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
}

// Run the script
main().catch(console.error);