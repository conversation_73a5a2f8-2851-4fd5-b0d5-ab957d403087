// Lazy route configuration for Next.js pages
// This ensures each route bundle only loads when accessed

// Analytics pages - Heavy with charts
export const analyticsRoutes = {
  '/analytics': () => import('@/app/(dashboard)/analytics/page'),
  '/analytics/productivity': () => import('@/app/(dashboard)/analytics/productivity/page'),
  '/analytics/quality': () => import('@/app/(dashboard)/analytics/quality/page'),
}

// Editor pages - Heavy with Monaco
export const editorRoutes = {
  '/editor': () => import('@/app/(dashboard)/editor/page'),
  '/projects/[id]/editor': () => import('@/app/(dashboard)/projects/[id]/editor/page'),
}

// Export pages - Heavy with PDF generation
export const exportRoutes = {
  '/export': () => import('@/app/(dashboard)/export/page'),
  '/projects/[id]/export': () => import('@/app/(dashboard)/projects/[id]/export/page'),
}

// Admin pages - Only for admins
export const adminRoutes = {
  '/admin': () => import('@/app/(dashboard)/admin/page'),
  '/admin/users': () => import('@/app/(dashboard)/admin/users/page'),
  '/admin/analytics': () => import('@/app/(dashboard)/admin/analytics/page'),
}
