import { NextResponse } from 'next/server'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { logger } from '@/lib/services/logger'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { ServiceManager } from '@/lib/services/service-manager'
import { z } from 'zod'
import { baseSchemas } from '@/lib/validation/common-schemas'

// Schema for tracking selection analytics events
const trackEventSchema = z.object({
  projectId: baseSchemas.uuid.optional(),
  selectionProfileId: baseSchemas.uuid.optional(),
  eventType: z.enum([
    'profile_used',
    'project_created',
    'project_completed',
    'project_abandoned',
    'selection_modified',
    'writing_started',
    'chapter_completed',
    'export_generated'
  ]),
  selectionData: z.record(z.unknown()).optional(),
  outcomeData: z.record(z.unknown()).optional()
});

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: trackEventSchema,
    rateLimitKey: 'analytics-events',
    rateLimitCost: 1,
    maxRequestSize: 10240, // 10KB
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      // Check if user can write analytics (basic auth check)
      if (!user.email_verified) {
        return { valid: false, error: 'Email verification required' };
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { projectId, selectionProfileId, eventType, selectionData, outcomeData } = context.body;

  try {
    const serviceManager = ServiceManager.getInstance();
    const analyticsService = await serviceManager.getWritingAnalyticsService();
    
    if (!analyticsService) {
      logger.error('WritingAnalyticsService not available');
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 503 }
      );
    }

    // Track the event
    const response = await analyticsService.trackSelectionEvent(user.id, {
      projectId,
      selectionProfileId,
      eventType,
      selectionData,
      outcomeData
    });

    if (!response.success) {
      logger.error('Failed to track selection event:', response.error, {
        userId: user.id,
        eventType,
        clientIP: context.clientIP
      });
      return NextResponse.json(
        { error: response.error || 'Failed to track event' },
        { status: 400 }
      );
    }

    logger.info('Selection event tracked', {
      userId: user.id,
      eventType,
      projectId,
      selectionProfileId,
      clientIP: context.clientIP
    });

    return NextResponse.json({ 
      success: true,
      analyticsEntry: response.data
    });
    
  } catch (error) {
    logger.error('Selection analytics API error:', error, {
      userId: user.id,
      clientIP: context.clientIP
    });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
})

// Schema for fetching selection analytics
const getAnalyticsSchema = z.object({
  userId: baseSchemas.uuid.optional(),
  projectId: baseSchemas.uuid.optional(),
  eventType: z.enum([
    'profile_used',
    'project_created',
    'project_completed',
    'project_abandoned',
    'selection_modified',
    'writing_started',
    'chapter_completed',
    'export_generated'
  ]).optional(),
  profileId: baseSchemas.uuid.optional(),
  days: z.coerce.number().min(1).max(365).default(30)
});

export const GET = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const { searchParams } = new URL(request.url);
  const queryParams = {
    userId: searchParams.get('userId'),
    projectId: searchParams.get('projectId'),
    eventType: searchParams.get('eventType'),
    profileId: searchParams.get('profileId'),
    days: searchParams.get('days') || '30'
  };

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    querySchema: getAnalyticsSchema,
    rateLimitKey: 'analytics-read',
    rateLimitCost: 2,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      // Only allow users to view their own analytics unless they're admin
      if (queryParams.userId && queryParams.userId !== user.id) {
        const serviceManager = ServiceManager.getInstance();
        const userService = await serviceManager.getUserService();
        
        if (!userService) {
          return { valid: false, error: 'Service temporarily unavailable' };
        }
        
        const isAdminResponse = await userService.isAdmin(user.id);
        if (!isAdminResponse.success || !isAdminResponse.data) {
          return { valid: false, error: 'Access denied' };
        }
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { userId, projectId, eventType, profileId, days } = context.query;

  // If no userId specified, default to current user
  const targetUserId = userId || user.id;

  try {
    const serviceManager = ServiceManager.getInstance();
    const analyticsService = await serviceManager.getWritingAnalyticsService();
    
    if (!analyticsService) {
      logger.error('WritingAnalyticsService not available');
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 503 }
      );
    }

    // Get selection analytics
    const response = await analyticsService.getSelectionAnalytics(targetUserId, {
      projectId,
      eventType,
      profileId,
      days
    });

    if (!response.success) {
      logger.error('Failed to fetch selection analytics:', response.error, {
        userId: user.id,
        targetUserId,
        clientIP: context.clientIP
      });
      return NextResponse.json(
        { error: response.error || 'Failed to fetch analytics' },
        { status: 500 }
      );
    }

    logger.info('Selection analytics fetched', {
      userId: user.id,
      targetUserId,
      analyticsCount: response.data?.length || 0,
      filters: { projectId, eventType, profileId, days },
      clientIP: context.clientIP
    });

    return NextResponse.json({ analytics: response.data || [] });
    
  } catch (error) {
    logger.error('Selection analytics API error:', error, {
      userId: user.id,
      clientIP: context.clientIP
    });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
})