import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { ServiceBase, ServiceResponse } from './base-service';
import { Database } from '@/lib/db/database.types';

type Project = Database['public']['Tables']['projects']['Row'];
type ProjectInsert = Database['public']['Tables']['projects']['Insert'];
type ProjectUpdate = Database['public']['Tables']['projects']['Update'];

export class ProjectService extends ServiceBase {
  constructor() {
    super({
      name: 'project-service',
      version: '1.0.0',
      endpoints: ['/api/projects'],
      dependencies: [],
      healthCheck: '/api/services/project/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, { status: 'healthy' });
  }

  /**
   * Get a single project by ID
   */
  async getProject(projectId: string, userId?: string): Promise<ServiceResponse<Project>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      let query = supabase
        .from('projects')
        .select(`
          id, title, description, status, 
          primary_genre, secondary_genre, sub_genres,
          pov_type, narrative_voice, tense,
          world_type, time_period, magic_tech_level,
          violence_level, romance_level, humor_level,
          tone_options, pacing, dialogue_density,
          descriptive_density, narrative_distance,
          chapter_structure, scene_density,
          conflict_types, endings, writing_style,
          themes, protagonist_types, antagonist_types,
          target_word_count, target_chapters,
          current_word_count, total_word_count,
          target_audience, content_rating,
          project_settings, created_at, updated_at,
          user_id
        `)
        .eq('id', projectId);
      
      // Add user filter if provided
      if (userId) {
        query = query.eq('user_id', userId);
      }
      
      const { data, error } = await query.single();
      
      if (error) {
        logger.error('[ProjectService] Error fetching project:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Project not found');
      }
      
      return data;
    });
  }

  /**
   * Get project by ID (without ownership check)
   */
  async getProjectById(projectId: string): Promise<ServiceResponse<Project>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single();
      
      if (error) {
        if (error.code === 'PGRST116') {
          throw new Error('Project not found');
        }
        logger.error('[ProjectService] Error fetching project by ID:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Project not found');
      }
      
      return data;
    });
  }

  /**
   * Get all projects for a user
   */
  async getUserProjects(
    userId: string, 
    options?: {
      limit?: number;
      offset?: number;
      status?: string;
      orderBy?: string;
    }
  ): Promise<ServiceResponse<Project[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      let query = supabase
        .from('projects')
        .select('id, title, status, primary_genre, updated_at, total_word_count, target_word_count')
        .eq('user_id', userId);
      
      // Apply filters
      if (options?.status) {
        query = query.eq('status', options.status);
      }
      
      // Apply ordering
      const orderBy = options?.orderBy || 'updated_at';
      query = query.order(orderBy, { ascending: false });
      
      // Apply pagination
      if (options?.limit) {
        query = query.limit(options.limit);
      }
      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 20) - 1);
      }
      
      const { data, error } = await query;
      
      if (error) {
        logger.error('[ProjectService] Error fetching user projects:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Create a new project
   */
  async createProject(userId: string, projectData: Partial<ProjectInsert>): Promise<ServiceResponse<Project>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('projects')
        .insert({
          ...projectData,
          user_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        logger.error('[ProjectService] Error creating project:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Failed to create project');
      }
      
      return data;
    });
  }

  /**
   * Update a project
   */
  async updateProject(
    projectId: string, 
    userId: string, 
    updates: Partial<ProjectUpdate>
  ): Promise<ServiceResponse<Project>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('projects')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId)
        .eq('user_id', userId)
        .select()
        .single();
      
      if (error) {
        logger.error('[ProjectService] Error updating project:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Project not found or unauthorized');
      }
      
      return data;
    });
  }

  /**
   * Delete a project
   */
  async deleteProject(projectId: string, userId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId)
        .eq('user_id', userId);
      
      if (error) {
        logger.error('[ProjectService] Error deleting project:', error);
        throw error;
      }
      
      return true;
    });
  }

  /**
   * Get project count for a user
   */
  async getProjectCount(userId: string): Promise<ServiceResponse<number>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { count, error } = await supabase
        .from('projects')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId);
      
      if (error) {
        logger.error('[ProjectService] Error counting projects:', error);
        throw error;
      }
      
      return count || 0;
    });
  }

  /**
   * Check if user has access to a project
   */
  async checkProjectAccess(projectId: string, userId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Check direct ownership
      const { data: project } = await supabase
        .from('projects')
        .select('id')
        .eq('id', projectId)
        .eq('user_id', userId)
        .single();
      
      if (project) {
        return true;
      }
      
      // Check collaboration access
      const { data: collaborator } = await supabase
        .from('project_collaborators')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', userId)
        .single();
      
      return !!collaborator;
    });
  }

  /**
   * Get project with related data
   */
  async getProjectWithRelations(
    projectId: string, 
    userId: string,
    relations: Array<'chapters' | 'characters' | 'story_bible'>
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      let selectQuery = '*';
      
      if (relations.includes('chapters')) {
        selectQuery += ', chapters(id, title, chapter_number, word_count, status)';
      }
      
      if (relations.includes('characters')) {
        selectQuery += ', characters(id, name, role, avatar_url)';
      }
      
      if (relations.includes('story_bible')) {
        selectQuery += ', story_bible(id, category, key, value)';
      }
      
      const { data, error } = await supabase
        .from('projects')
        .select(selectQuery)
        .eq('id', projectId)
        .eq('user_id', userId)
        .single();
      
      if (error) {
        logger.error('[ProjectService] Error fetching project with relations:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Project not found');
      }
      
      return data;
    });
  }

  /**
   * Get reference materials for a project
   */
  async getReferenceMaterials(
    projectId: string,
    userId: string,
    filters?: { type?: string; tags?: string[] }
  ): Promise<ServiceResponse<any[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // First verify project access
      const accessResponse = await this.checkProjectAccess(projectId, userId);
      if (!accessResponse.success || !accessResponse.data) {
        throw new Error('Project not found or access denied');
      }

      let query = supabase
        .from('reference_materials')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (filters?.type && filters.type !== 'all') {
        query = query.eq('type', filters.type);
      }

      if (filters?.tags) {
        query = query.contains('tags', filters.tags);
      }

      const { data, error } = await query;

      if (error) {
        logger.error('[ProjectService] Error fetching reference materials:', error);
        throw error;
      }

      // Transform data to match frontend interface
      const formattedMaterials = data?.map(material => ({
        id: material.id,
        projectId: material.project_id,
        type: material.type,
        title: material.title,
        description: material.description,
        fileUrl: material.file_url,
        fileSize: material.file_size,
        mimeType: material.mime_type,
        content: material.content,
        tags: material.tags || [],
        aiSummary: material.ai_summary,
        createdAt: new Date(material.created_at),
        updatedAt: new Date(material.updated_at),
      })) || [];

      return formattedMaterials;
    });
  }

  /**
   * Create a new reference material
   */
  async createReferenceMaterial(
    projectId: string,
    userId: string,
    materialData: {
      type: 'document' | 'image' | 'url' | 'note' | 'research';
      title: string;
      description?: string;
      content?: string;
      tags?: string[];
    }
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // First verify project access
      const accessResponse = await this.checkProjectAccess(projectId, userId);
      if (!accessResponse.success || !accessResponse.data) {
        throw new Error('Project not found or access denied');
      }

      const insertData = {
        project_id: projectId,
        user_id: userId,
        type: materialData.type,
        title: materialData.title,
        description: materialData.description || null,
        content: materialData.content || null,
        tags: materialData.tags || [],
      };

      const { data, error } = await supabase
        .from('reference_materials')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        logger.error('[ProjectService] Error creating reference material:', error);
        throw error;
      }

      if (!data) {
        throw new Error('Failed to create reference material');
      }

      // Transform response to match frontend interface
      const formattedMaterial = {
        id: data.id,
        projectId: data.project_id,
        type: data.type,
        title: data.title,
        description: data.description,
        fileUrl: data.file_url,
        fileSize: data.file_size,
        mimeType: data.mime_type,
        content: data.content,
        tags: data.tags || [],
        aiSummary: data.ai_summary,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };

      return formattedMaterial;
    });
  }

  /**
   * Get project team members (owner + collaborators + pending invitations)
   */
  async getProjectTeam(projectId: string, userId: string): Promise<ServiceResponse<{
    members: any[];
    pendingInvites: string[];
  }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // First verify project access
      const accessResponse = await this.checkProjectAccess(projectId, userId);
      if (!accessResponse.success || !accessResponse.data) {
        throw new Error('Project not found or access denied');
      }

      // Get project details
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('user_id, title')
        .eq('id', projectId)
        .single();
      
      if (projectError) {
        logger.error('[ProjectService] Error fetching project for team:', projectError);
        throw projectError;
      }

      const members = [];
      
      // Add project owner
      const { data: owner } = await supabase
        .from('users')
        .select('id, email, profiles(display_name, avatar_url)')
        .eq('id', project.user_id)
        .single();
      
      if (owner) {
        members.push({
          id: owner.id,
          email: owner.email,
          name: owner.profiles?.display_name || owner.email,
          avatar_url: owner.profiles?.avatar_url,
          role: 'owner',
          status: 'active',
          joined_at: new Date().toISOString(),
          permissions: {
            can_write: true,
            can_manage_team: true,
            can_export: true,
            can_delete: true
          }
        });
      }
      
      // Get collaborators
      const { data: collaborators } = await supabase
        .from('project_collaborators')
        .select(`
          *,
          user:users!user_id(
            id,
            email,
            profiles(display_name, avatar_url)
          )
        `)
        .eq('project_id', projectId)
        .eq('status', 'active');
      
      if (collaborators) {
        collaborators.forEach((collab: any) => {
          members.push({
            id: collab.user.id,
            email: collab.user.email,
            name: collab.user.profiles?.display_name || collab.user.email,
            avatar_url: collab.user.profiles?.avatar_url,
            role: collab.role,
            status: 'active',
            joined_at: collab.created_at,
            permissions: {
              can_write: collab.role === 'editor',
              can_manage_team: false,
              can_export: true,
              can_delete: false
            }
          });
        });
      }
      
      // Get pending invitations
      const { data: invitations } = await supabase
        .from('project_invitations')
        .select('email, role, created_at')
        .eq('project_id', projectId)
        .eq('status', 'pending')
        .gte('expires_at', new Date().toISOString());
      
      const pendingInvites = invitations?.map((inv: any) => inv.email) || [];
      
      return {
        members,
        pendingInvites
      };
    });
  }

  /**
   * Remove a collaborator from project team
   */
  async removeTeamMember(
    projectId: string,
    userId: string,
    memberId: string
  ): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // First verify project ownership (only owner can remove team members)
      const { data: project } = await supabase
        .from('projects')
        .select('user_id')
        .eq('id', projectId)
        .eq('user_id', userId)
        .single();
        
      if (!project) {
        throw new Error('Project not found or insufficient permissions');
      }

      // Remove collaborator
      const { error } = await supabase
        .from('project_collaborators')
        .delete()
        .eq('project_id', projectId)
        .eq('user_id', memberId);

      if (error) {
        logger.error('[ProjectService] Error removing team member:', error);
        throw error;
      }

      logger.info('[ProjectService] Team member removed', {
        projectId,
        memberId
      });

      return true;
    });
  }
}