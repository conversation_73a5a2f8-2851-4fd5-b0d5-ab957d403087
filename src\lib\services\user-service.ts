import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { ServiceBase, ServiceResponse } from './base-service';
import { Database } from '@/lib/db/database.types';

type Profile = Database['public']['Tables']['profiles']['Row'];
type UserSubscription = Database['public']['Tables']['user_subscriptions']['Row'];

export class UserService extends ServiceBase {
  constructor() {
    super({
      name: 'user-service',
      version: '1.0.0',
      endpoints: ['/api/user'],
      dependencies: [],
      healthCheck: '/api/services/user/health'
    });
  }

  async initialize(): Promise<void> {
    this.initialize= true;
    this.setStatus('active');
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, { status: 'healthy' });
  }

  /**
   * Get user profile
   */
  async getUserProfile(userId: string): Promise<ServiceResponse<Profile>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
      
      if (error) {
        logger.error('[UserService] Error fetching profile:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Profile not found');
      }
      
      return data;
    });
  }

  /**
   * Get user subscription
   */
  async getUserSubscription(userId: string): Promise<ServiceResponse<UserSubscription | null>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', userId)
        .single();
      
      if (error) {
        // It's okay if no subscription exists
        if (error.code === 'PGRST116') {
          return null;
        }
        logger.error('[UserService] Error fetching subscription:', error);
        throw error;
      }
      
      return data;
    });
  }

  /**
   * Get user subscription tier
   */
  async getUserSubscriptionTier(userId: string): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      const subscriptionResponse = await this.getUserSubscription(userId);
      
      if (!subscriptionResponse.success || !subscriptionResponse.data) {
        return 'free'; // Default tier
      }
      
      return subscriptionResponse.data.tier || 'free';
    });
  }

  /**
   * Update user profile
   */
  async updateUserProfile(userId: string, updates: Partial<Profile>): Promise<ServiceResponse<Profile>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();
      
      if (error) {
        logger.error('[UserService] Error updating profile:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Profile not found');
      }
      
      return data;
    });
  }

  /**
   * Check if user has subscription
   */
  async hasActiveSubscription(userId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const subscriptionResponse = await this.getUserSubscription(userId);
      
      if (!subscriptionResponse.success || !subscriptionResponse.data) {
        return false;
      }
      
      const subscription = subscriptionResponse.data;
      
      // Check if subscription is active and not expired
      return subscription.status === 'active' && 
             (!subscription.end_date || new Date(subscription.end_date) > new Date());
    });
  }

  /**
   * Get email preferences for a user
   */
  async getEmailPreferences(userId: string): Promise<ServiceResponse<Record<string, boolean>>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('profiles')
        .select('email_preferences')
        .eq('id', userId)
        .single();
      
      if (error) {
        logger.error('[UserService] Error fetching email preferences:', error);
        throw error;
      }
      
      // Return default preferences if none set
      return data?.email_preferences || {
        marketing: true,
        progress: true,
        achievements: true,
        collaboration: true,
        newsletter: false
      };
    });
  }

  /**
   * Update email preferences for a user
   */
  async updateEmailPreferences(
    userId: string, 
    preferences: Record<string, boolean>
  ): Promise<ServiceResponse<Record<string, boolean>>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('profiles')
        .update({
          email_preferences: preferences,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select('email_preferences')
        .single();
      
      if (error) {
        logger.error('[UserService] Error updating email preferences:', error);
        throw error;
      }
      
      return data?.email_preferences || preferences;
    });
  }

  /**
   * Get user privacy settings
   */
  async getPrivacySettings(userId: string): Promise<ServiceResponse<Record<string, unknown>>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('profiles')
        .select('privacy_settings')
        .eq('id', userId)
        .single();
      
      if (error) {
        logger.error('[UserService] Error fetching privacy settings:', error);
        throw error;
      }
      
      return data?.privacy_settings || {};
    });
  }

  /**
   * Update user privacy settings
   */
  async updatePrivacySettings(
    userId: string, 
    settings: Record<string, unknown>
  ): Promise<ServiceResponse<Record<string, unknown>>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('profiles')
        .update({
          privacy_settings: settings,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select('privacy_settings')
        .single();
      
      if (error) {
        logger.error('[UserService] Error updating privacy settings:', error);
        throw error;
      }
      
      return data?.privacy_settings || settings;
    });
  }

  /**
   * Export all user data (GDPR compliance)
   */
  async exportUserData(userId: string): Promise<ServiceResponse<Record<string, unknown>>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const userData: Record<string, unknown> = {};

      // User profile
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
      userData.profile = profile;

      // Projects
      const { data: projects } = await supabase
        .from('projects')
        .select('*')
        .eq('user_id', userId);
      userData.projects = projects;

      // Chapters
      const { data: chapters } = await supabase
        .from('chapters')
        .select('*')
        .in('project_id', projects?.map(p => p.id) || []);
      userData.chapters = chapters;

      // Characters
      const { data: characters } = await supabase
        .from('characters')
        .select('*')
        .in('project_id', projects?.map(p => p.id) || []);
      userData.characters = characters;

      // Writing sessions
      const { data: sessions } = await supabase
        .from('writing_sessions')
        .select('*')
        .eq('user_id', userId);
      userData.writingSessions = sessions;

      // Goals
      const { data: goals } = await supabase
        .from('writing_goals')
        .select('*')
        .eq('user_id', userId);
      userData.writingGoals = goals;

      // Analytics
      const { data: analytics } = await supabase
        .from('user_analytics')
        .select('*')
        .eq('user_id', userId);
      userData.analytics = analytics;

      // AI usage logs
      const { data: aiLogs } = await supabase
        .from('ai_usage_logs')
        .select('*')
        .eq('user_id', userId);
      userData.aiUsageLogs = aiLogs;

      // Subscriptions
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', userId);
      userData.subscription = subscription;

      return userData;
    });
  }

  /**
   * Delete all user data (GDPR compliance)
   */
  async deleteUserData(userId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      // Delete in order to respect foreign key constraints
      
      // First get all project IDs
      const { data: projects } = await supabase
        .from('projects')
        .select('id')
        .eq('user_id', userId);
      
      const projectIds = projects?.map(p => p.id) || [];

      // Delete chapter versions
      if (projectIds.length > 0) {
        await supabase
          .from('chapter_versions')
          .delete()
          .in('project_id', projectIds);
      }

      // Delete chapters
      if (projectIds.length > 0) {
        await supabase
          .from('chapters')
          .delete()
          .in('project_id', projectIds);
      }

      // Delete characters
      if (projectIds.length > 0) {
        await supabase
          .from('characters')
          .delete()
          .in('project_id', projectIds);
      }

      // Delete story bibles
      if (projectIds.length > 0) {
        await supabase
          .from('story_bible')
          .delete()
          .in('project_id', projectIds);
      }

      // Delete project collaborators
      if (projectIds.length > 0) {
        await supabase
          .from('project_collaborators')
          .delete()
          .in('project_id', projectIds);
      }

      // Delete projects
      await supabase
        .from('projects')
        .delete()
        .eq('user_id', userId);

      // Delete writing sessions
      await supabase
        .from('writing_sessions')
        .delete()
        .eq('user_id', userId);

      // Delete writing goals and progress
      const { data: goalIds } = await supabase
        .from('writing_goals')
        .select('id')
        .eq('user_id', userId);

      if (goalIds && goalIds.length > 0) {
        await supabase
          .from('writing_goal_progress')
          .delete()
          .in('goal_id', goalIds.map(g => g.id));
      }

      await supabase
        .from('writing_goals')
        .delete()
        .eq('user_id', userId);

      // Delete analytics
      await supabase
        .from('user_analytics')
        .delete()
        .eq('user_id', userId);

      await supabase
        .from('selection_analytics')
        .delete()
        .eq('user_id', userId);

      await supabase
        .from('behavioral_analytics')
        .delete()
        .eq('user_id', userId);

      // Delete AI usage logs
      await supabase
        .from('ai_usage_logs')
        .delete()
        .eq('user_id', userId);

      // Delete notifications
      await supabase
        .from('notifications')
        .delete()
        .eq('user_id', userId);

      // Delete processing tasks
      await supabase
        .from('processing_tasks')
        .delete()
        .eq('user_id', userId);

      // Delete subscriptions
      await supabase
        .from('user_subscriptions')
        .delete()
        .eq('user_id', userId);

      // Delete usage tracking
      await supabase
        .from('usage_tracking')
        .delete()
        .eq('user_id', userId);

      await supabase
        .from('usage_events')
        .delete()
        .eq('user_id', userId);

      // Delete profile last (has foreign key references)
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (error) {
        logger.error('[UserService] Error deleting user profile:', error);
        throw error;
      }

      logger.info(`[UserService] Successfully deleted all data for user ${userId}`);
      return true;
    });
  }

  /**
   * Get consent history for a user
   */
  async getConsentHistory(userId: string): Promise<ServiceResponse<ConsentHistory[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('consent_history')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('[UserService] Error fetching consent history:', error);
        throw error;
      }

      return data || [];
    });
  }

  /**
   * Record user consent
   */
  async recordConsent(
    userId: string,
    consentType: string,
    granted: boolean,
    details?: Record<string, unknown>
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('consent_history')
        .insert({
          user_id: userId,
          consent_type: consentType,
          granted,
          details,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        logger.error('[UserService] Error recording consent:', error);
        throw error;
      }

      return data;
    });
  }
}