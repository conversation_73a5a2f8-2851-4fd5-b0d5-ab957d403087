'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { GoalCard } from './goal-card'
import { CreateGoalDialog } from './create-goal-dialog'
import { GoalProgressChart } from './goal-progress-chart'
import { Plus } from 'lucide-react'
import { Target } from 'lucide-react'
import { TrendingUp } from 'lucide-react'
import { Calendar } from 'lucide-react'
import { Award } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

interface WritingGoal {
  id: string
  goal_type: 'daily' | 'weekly' | 'monthly' | 'project'
  target_words: number
  start_date: string
  end_date?: string
  is_active: boolean
  current_progress: number
  percentage_complete: number
  days_active: number
  streak_count?: number
  project_id?: string
  project_name?: string
}

interface GoalStats {
  total_words_today: number
  total_words_week: number
  total_words_month: number
  current_streak: number
  longest_streak: number
  goals_completed: number
  goals_active: number
}

export function WritingGoalsDashboard() {
  const [goals, setGoals] = useState<WritingGoal[]>([])
  const [stats, setStats] = useState<GoalStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingGoal, setEditingGoal] = useState<WritingGoal | null>(null)
  const [activeTab, setActiveTab] = useState('active')
  const { toast } = useToast()

  useEffect(() => {
    fetchGoals()
    fetchStats()
  }, [])

  const fetchGoals = async () => {
    try {
      const response = await fetch('/api/writing/goals')
      if (response.ok) {
        const data = await response.json()
        setGoals(data.goals || [])
      }
    } catch (error) {
      console.error('Error fetching goals:', error)
      toast({
        title: 'Error',
        description: 'Failed to load writing goals',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/writing/goals/stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleCreateGoal = async (goalData: any) => {
    try {
      const response = await fetch('/api/writing/goals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(goalData)
      })

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Writing goal created successfully'
        })
        fetchGoals()
        fetchStats()
        setShowCreateDialog(false)
      }
    } catch (error) {
      console.error('Error creating goal:', error)
      toast({
        title: 'Error',
        description: 'Failed to create writing goal',
        variant: 'destructive'
      })
    }
  }

  const handleUpdateGoal = async (goalId: string, updates: any) => {
    try {
      const response = await fetch(`/api/writing/goals/${goalId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      })

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Goal updated successfully'
        })
        fetchGoals()
        setEditingGoal(null)
      }
    } catch (error) {
      console.error('Error updating goal:', error)
      toast({
        title: 'Error',
        description: 'Failed to update goal',
        variant: 'destructive'
      })
    }
  }

  const handleDeleteGoal = async (goalId: string) => {
    if (!confirm('Are you sure you want to delete this goal?')) return

    try {
      const response = await fetch(`/api/writing/goals/${goalId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Goal deleted successfully'
        })
        fetchGoals()
        fetchStats()
      }
    } catch (error) {
      console.error('Error deleting goal:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete goal',
        variant: 'destructive'
      })
    }
  }

  const handleToggleGoal = async (goalId: string, active: boolean) => {
    await handleUpdateGoal(goalId, { is_active: active })
  }

  const activeGoals = goals.filter(g => g.is_active)
  const inactiveGoals = goals.filter(g => !g.is_active)

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Today's Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.total_words_today.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">words written</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Weekly Total</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.total_words_week.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">words this week</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Current Streak</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold flex items-center">
                {stats.current_streak}
                {stats.current_streak > 0 && <TrendingUp className="h-4 w-4 ml-1 text-green-500" />}
              </div>
              <p className="text-xs text-muted-foreground">consecutive days</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Goals Completed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold flex items-center">
                {stats.goals_completed}
                <Award className="h-4 w-4 ml-1 text-yellow-500" />
              </div>
              <p className="text-xs text-muted-foreground">total completed</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Progress Chart */}
      <GoalProgressChart />

      {/* Goals List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Writing Goals</CardTitle>
              <CardDescription>
                Set and track your writing targets
              </CardDescription>
            </div>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Goal
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="active">
                Active ({activeGoals.length})
              </TabsTrigger>
              <TabsTrigger value="inactive">
                Inactive ({inactiveGoals.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="active" className="mt-6">
              {activeGoals.length === 0 ? (
                <div className="text-center py-8">
                  <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground mb-4">
                    No active writing goals
                  </p>
                  <Button onClick={() => setShowCreateDialog(true)}>
                    Create Your First Goal
                  </Button>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {activeGoals.map(goal => (
                    <GoalCard
                      key={goal.id}
                      goal={goal}
                      onEdit={setEditingGoal}
                      onDelete={handleDeleteGoal}
                      onToggle={handleToggleGoal}
                    />
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="inactive" className="mt-6">
              {inactiveGoals.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No inactive goals
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {inactiveGoals.map(goal => (
                    <GoalCard
                      key={goal.id}
                      goal={goal}
                      onEdit={setEditingGoal}
                      onDelete={handleDeleteGoal}
                      onToggle={handleToggleGoal}
                    />
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Create/Edit Goal Dialog */}
      <CreateGoalDialog
        open={showCreateDialog || !!editingGoal}
        onClose={() => {
          setShowCreateDialog(false)
          setEditingGoal(null)
        }}
        onSubmit={editingGoal 
          ? (data) => handleUpdateGoal(editingGoal.id, data)
          : handleCreateGoal
        }
        initialData={editingGoal}
      />
    </div>
  )
}