'use client'

import { useState, useEffect, use<PERSON><PERSON>back, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Search } from 'lucide-react'
import { X } from 'lucide-react'
import { Filter } from 'lucide-react'
import { FileText } from 'lucide-react'
import { Users } from 'lucide-react'
import { MapPin } from 'lucide-react'
import { Calendar } from 'lucide-react'
import { Book<PERSON><PERSON> } from 'lucide-react'
import { Hash } from 'lucide-react'
import { Clock } from 'lucide-react'
import { TrendingUp } from 'lucide-react'
import { Settings2 } from 'lucide-react'
import { ChevronDown } from 'lucide-react'
import { ChevronRight } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useDebounce } from '@/hooks/use-debounce'
import { ComponentErrorBoundary } from '@/components/error/unified-error-boundary'
import { cn } from '@/lib/utils'

interface ContentSearchInterfaceProps {
  projectId: string
  onResultSelect?: (result: SearchResult) => void
  className?: string
}

export interface SearchResult {
  id: string
  type: 'chapter' | 'character' | 'location' | 'story_bible' | 'note'
  title: string
  content: string
  excerpt: string
  relevanceScore: number
  highlights: string[]
  metadata: {
    chapterNumber?: number
    characterRole?: string
    locationType?: string
    entryType?: string
    wordCount?: number
    lastModified: string
    createdBy?: string
  }
}

interface SearchFilters {
  contentTypes: string[]
  dateRange: 'all' | 'today' | 'week' | 'month' | 'custom'
  customDateRange?: { from: Date; to: Date }
  sortBy: 'relevance' | 'date' | 'title' | 'type'
  includeArchived: boolean
}

const CONTENT_TYPE_OPTIONS = [
  { value: 'chapter', label: 'Chapters', icon: FileText },
  { value: 'character', label: 'Characters', icon: Users },
  { value: 'location', label: 'Locations', icon: MapPin },
  { value: 'story_bible', label: 'Story Bible', icon: BookOpen },
  { value: 'note', label: 'Notes', icon: Hash }
]

const SORT_OPTIONS = [
  { value: 'relevance', label: 'Most Relevant' },
  { value: 'date', label: 'Most Recent' },
  { value: 'title', label: 'Alphabetical' },
  { value: 'type', label: 'By Type' }
]

export function ContentSearchInterface({
  projectId,
  onResultSelect,
  className
}: ContentSearchInterfaceProps) {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [totalResults, setTotalResults] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [showFilters, setShowFilters] = useState(false)
  const [selectedTab, setSelectedTab] = useState('all')
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [filters, setFilters] = useState<SearchFilters>({
    contentTypes: [],
    dateRange: 'all',
    sortBy: 'relevance',
    includeArchived: false
  })
  
  const searchInputRef = useRef<HTMLInputElement>(null)
  const debouncedQuery = useDebounce(searchQuery, 300)

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem(`bookscribe-recent-searches-${projectId}`)
    if (saved) {
      setRecentSearches(JSON.parse(saved).slice(0, 5))
    }
  }, [projectId])

  // Perform search
  const performSearch = useCallback(async (query: string, page = 1) => {
    if (!query.trim()) {
      setResults([])
      setTotalResults(0)
      return
    }

    setIsSearching(true)
    try {
      const params = new URLSearchParams({
        q: query,
        projectId,
        page: page.toString(),
        limit: '20',
        ...Object.fromEntries(
          Object.entries(filters).map(([key, value]) => [
            key,
            Array.isArray(value) ? value.join(',') : String(value)
          ])
        )
      })

      const response = await fetch(`/api/search/content?${params}`)
      if (!response.ok) throw new Error('Search failed')

      const data = await response.json()
      setResults(data.data.results || [])
      setTotalResults(data.data.total || 0)
      setCurrentPage(page)

      // Save to recent searches
      if (page === 1 && query.trim()) {
        const updated = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5)
        setRecentSearches(updated)
        localStorage.setItem(
          `bookscribe-recent-searches-${projectId}`,
          JSON.stringify(updated)
        )
      }
    } catch (error) {
      toast({
        title: 'Search Error',
        description: 'Failed to search content. Please try again.',
        variant: 'destructive'
      })
      console.error('Search error:', error)
    } finally {
      setIsSearching(false)
    }
  }, [projectId, filters, recentSearches, toast])

  // Debounced search effect
  useEffect(() => {
    if (debouncedQuery) {
      performSearch(debouncedQuery)
    }
  }, [debouncedQuery, performSearch])

  // Filter change effect
  useEffect(() => {
    if (searchQuery) {
      performSearch(searchQuery)
    }
  }, [filters, searchQuery, performSearch])

  const handleResultClick = (result: SearchResult) => {
    if (onResultSelect) {
      onResultSelect(result)
    }
  }

  const clearSearch = () => {
    setSearchQuery('')
    setResults([])
    setTotalResults(0)
    searchInputRef.current?.focus()
  }

  const getResultIcon = (type: SearchResult['type']) => {
    switch (type) {
      case 'chapter': return FileText
      case 'character': return Users
      case 'location': return MapPin
      case 'story_bible': return BookOpen
      case 'note': return Hash
      default: return FileText
    }
  }

  const renderSearchResult = (result: SearchResult) => {
    const Icon = getResultIcon(result.type)
    
    return (
      <button
        key={result.id}
        className="w-full text-left p-4 hover:bg-accent rounded-lg transition-colors group"
        onClick={() => handleResultClick(result)}
      >
        <div className="flex gap-3">
          <div className="mt-1">
            <Icon className="w-5 h-5 text-muted-foreground" />
          </div>
          <div className="flex-1 space-y-1">
            <div className="flex items-start justify-between gap-2">
              <h4 className="font-medium group-hover:text-primary transition-colors">
                {result.title}
              </h4>
              <Badge variant="outline" className="capitalize">
                {result.type.replace('_', ' ')}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground line-clamp-2">
              {result.excerpt}
            </p>
            {result.highlights.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {result.highlights.slice(0, 3).map((highlight, idx) => (
                  <span
                    key={idx}
                    className="text-xs bg-yellow-100 dark:bg-yellow-900/30 px-1 rounded"
                    dangerouslySetInnerHTML={{ __html: highlight }}
                  />
                ))}
              </div>
            )}
            <div className="flex items-center gap-4 text-xs text-muted-foreground mt-2">
              {result.metadata.chapterNumber && (
                <span>Chapter {result.metadata.chapterNumber}</span>
              )}
              {result.metadata.wordCount && (
                <span>{result.metadata.wordCount.toLocaleString()} words</span>
              )}
              <span>
                <Clock className="w-3 h-3 inline mr-1" />
                {new Date(result.metadata.lastModified).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      </button>
    )
  }

  const renderFilterSection = () => (
    <div className="space-y-4 p-4 border rounded-lg">
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Filters</h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
        >
          {showFilters ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
        </Button>
      </div>
      
      {showFilters && (
        <div className="space-y-4">
          {/* Content Types */}
          <div className="space-y-2">
            <Label>Content Types</Label>
            <div className="space-y-2">
              {CONTENT_TYPE_OPTIONS.map(option => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={option.value}
                    checked={filters.contentTypes.includes(option.value)}
                    onCheckedChange={(checked) => {
                      setFilters(prev => ({
                        ...prev,
                        contentTypes: checked
                          ? [...prev.contentTypes, option.value]
                          : prev.contentTypes.filter(t => t !== option.value)
                      }))
                    }}
                  />
                  <Label
                    htmlFor={option.value}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Date Range */}
          <div className="space-y-2">
            <Label>Date Range</Label>
            <Select
              value={filters.dateRange}
              onValueChange={(value: any) => setFilters(prev => ({ ...prev, dateRange: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">Past Week</SelectItem>
                <SelectItem value="month">Past Month</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Sort By */}
          <div className="space-y-2">
            <Label>Sort By</Label>
            <Select
              value={filters.sortBy}
              onValueChange={(value: any) => setFilters(prev => ({ ...prev, sortBy: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {SORT_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Include Archived */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="archived"
              checked={filters.includeArchived}
              onCheckedChange={(checked) => {
                setFilters(prev => ({ ...prev, includeArchived: checked as boolean }))
              }}
            />
            <Label htmlFor="archived" className="text-sm font-normal cursor-pointer">
              Include archived content
            </Label>
          </div>
        </div>
      )}
    </div>
  )

  return (
    <ComponentErrorBoundary componentName="ContentSearchInterface">
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            Content Search
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              ref={searchInputRef}
              type="text"
              placeholder="Search chapters, characters, locations, and more..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-10"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-0"
                onClick={clearSearch}
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>

          {/* Recent Searches */}
          {!searchQuery && recentSearches.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Recent searches:</p>
              <div className="flex flex-wrap gap-2">
                {recentSearches.map((search, idx) => (
                  <Button
                    key={idx}
                    variant="outline"
                    size="sm"
                    onClick={() => setSearchQuery(search)}
                  >
                    <Clock className="w-3 h-3 mr-1" />
                    {search}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Search Results */}
          {(searchQuery || results.length > 0) && (
            <div className="space-y-4">
              {/* Results Header */}
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  {isSearching ? (
                    'Searching...'
                  ) : totalResults > 0 ? (
                    `Found ${totalResults} result${totalResults === 1 ? '' : 's'}`
                  ) : searchQuery ? (
                    'No results found'
                  ) : null}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <Filter className="w-4 h-4 mr-2" />
                  Filters
                </Button>
              </div>

              {/* Filters */}
              {showFilters && renderFilterSection()}

              {/* Results List */}
              <ScrollArea className="h-[400px]">
                <div className="space-y-2">
                  {isSearching ? (
                    Array.from({ length: 3 }).map((_, idx) => (
                      <div key={idx} className="p-4 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-full" />
                        <Skeleton className="h-3 w-2/3" />
                      </div>
                    ))
                  ) : (
                    results.map(renderSearchResult)
                  )}
                </div>

                {/* Load More */}
                {totalResults > results.length && !isSearching && (
                  <div className="mt-4 text-center">
                    <Button
                      variant="outline"
                      onClick={() => performSearch(searchQuery, currentPage + 1)}
                    >
                      Load More Results
                    </Button>
                  </div>
                )}
              </ScrollArea>
            </div>
          )}
        </CardContent>
      </Card>
    </ComponentErrorBoundary>
  )
}