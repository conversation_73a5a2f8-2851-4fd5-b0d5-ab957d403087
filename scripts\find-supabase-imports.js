const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get all files with Supabase imports
const output = execSync(`grep -r "from '@/lib/supabase" src/app/api --include="*.ts" --include="*.tsx" -l`, { encoding: 'utf-8' });
const files = output.trim().split('\n').filter(f => f);

console.log(`Found ${files.length} files with direct Supabase imports:\n`);

// Group by directory
const byDir = {};
files.forEach(file => {
  const parts = file.split('/');
  const dir = parts.slice(0, 4).join('/'); // src/app/api/xxx
  if (!byDir[dir]) byDir[dir] = [];
  byDir[dir].push(file);
});

// Display grouped results
Object.entries(byDir).forEach(([dir, dirFiles]) => {
  console.log(`\n${dir} (${dirFiles.length} files):`);
  dirFiles.slice(0, 3).forEach(f => console.log(`  - ${f}`));
  if (dirFiles.length > 3) {
    console.log(`  ... and ${dirFiles.length - 3} more`);
  }
});

// Save to file
fs.writeFileSync('supabase-imports-list.txt', files.join('\n'));
console.log(`\nFull list saved to: supabase-imports-list.txt`);