"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  BarChart, Bar, LineChart, Line, RadarChart, Radar, PolarGrid, PolarAngleAxis, PolarRadiusAxis,
  XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend, Cell
} from "recharts"
import { TrendingUp } from 'lucide-react'
import { TrendingDown } from 'lucide-react'
import { Target } from 'lucide-react'
import { Clock } from 'lucide-react'
import { Calendar } from 'lucide-react'
import { Activity } from 'lucide-react'
import { Zap } from 'lucide-react'
import { Award } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { ChevronRight } from 'lucide-react'
import { <PERSON> } from 'lucide-react'
import { Lightbulb } from 'lucide-react'
import { logger } from '@/lib/services/logger'

interface ProductivityMetricsProps {
  userId: string
  projectId?: string
  timeframe?: 'week' | 'month' | 'quarter' | 'year'
}

interface ProductivityData {
  overview: {
    totalWords: number
    totalTime: number
    totalSessions: number
    activeDays: number
    totalDays: number
    productivityScore: number
  }
  averages: {
    wordsPerDay: number
    timePerDay: number
    sessionsPerDay: number
    wordsPerSession: number
    sessionDuration: number
  }
  patterns: {
    hourly: Array<{ hour: number; words: number; sessions: number }>
    weekday: Array<{ day: string; words: number; sessions: number }>
  }
  trends?: {
    wordsTrend: number
    daysTrend: number
  }
  insights?: Array<{
    type: string
    title: string
    message: string
    priority: 'high' | 'medium' | 'low'
  }>
}

const CHART_COLORS = {
  primary: "hsl(var(--primary))",
  secondary: "hsl(var(--secondary))",
  accent: "hsl(var(--accent))",
  success: "hsl(142, 76%, 36%)",
  warning: "hsl(38, 92%, 50%)",
  danger: "hsl(0, 84%, 60%)"
}

export function ProductivityMetrics({ userId, projectId, timeframe = 'week' }: ProductivityMetricsProps) {
  const [data, setData] = useState<ProductivityData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTab, setSelectedTab] = useState("overview")

  useEffect(() => {
    fetchProductivityData()
  }, [userId, projectId, timeframe])

  const fetchProductivityData = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        timeframe,
        insights: 'true',
        ...(projectId && { projectId })
      })

      const response = await fetch(`/api/analytics/productivity?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch productivity data')
      }

      const productivityData = await response.json()
      setData(productivityData)
    } catch (err) {
      logger.error('Error fetching productivity data:', err)
      setError('Failed to load productivity metrics')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="h-4 bg-muted animate-pulse rounded" />
            <div className="h-32 bg-muted animate-pulse rounded" />
            <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !data) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            {error || 'No productivity data available'}
          </div>
        </CardContent>
      </Card>
    )
  }

  const productivityScoreColor = 
    data.overview.productivityScore >= 80 ? CHART_COLORS.success :
    data.overview.productivityScore >= 60 ? CHART_COLORS.warning :
    CHART_COLORS.danger

  const consistencyRate = (data.overview.activeDays / data.overview.totalDays) * 100

  // Prepare radar chart data
  const radarData = [
    { metric: 'Consistency', value: consistencyRate, fullMark: 100 },
    { metric: 'Volume', value: Math.min((data.averages.wordsPerDay / 1000) * 100, 100), fullMark: 100 },
    { metric: 'Focus', value: Math.min((data.averages.sessionDuration / 60) * 100, 100), fullMark: 100 },
    { metric: 'Frequency', value: Math.min((data.averages.sessionsPerDay / 3) * 100, 100), fullMark: 100 },
  ]

  // Format hourly data for chart
  const hourlyChartData = data.patterns.hourly.map(h => ({
    hour: `${h.hour}:00`,
    words: h.words,
    sessions: h.sessions,
    avgWords: h.sessions > 0 ? Math.round(h.words / h.sessions) : 0
  }))

  return (
    <div className="space-y-6">
      {/* Productivity Score Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Productivity Score</CardTitle>
              <CardDescription>
                Based on consistency, volume, focus, and frequency
              </CardDescription>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold" style={{ color: productivityScoreColor }}>
                {data.overview.productivityScore}
              </div>
              <div className="text-sm text-muted-foreground">out of 100</div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Progress 
            value={data.overview.productivityScore} 
            className="h-3 mb-4"
            style={{ '--progress-color': productivityScoreColor } as any}
          />
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">Consistency</div>
              <div className="text-2xl font-semibold">{Math.round(consistencyRate)}%</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">Daily Average</div>
              <div className="text-2xl font-semibold">{data.averages.wordsPerDay}</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">Session Focus</div>
              <div className="text-2xl font-semibold">{data.averages.sessionDuration}m</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">Total Sessions</div>
              <div className="text-2xl font-semibold">{data.overview.totalSessions}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Metrics Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="patterns">Patterns</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Key Stats */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base font-medium">Total Output</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.overview.totalWords.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  words in {data.overview.totalTime} minutes
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base font-medium">Writing Days</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {data.overview.activeDays} / {data.overview.totalDays}
                </div>
                <Progress value={consistencyRate} className="h-2 mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base font-medium">Average Session</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.averages.wordsPerSession}</div>
                <p className="text-xs text-muted-foreground">
                  words in {data.averages.sessionDuration} minutes
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Productivity Radar */}
          <Card>
            <CardHeader>
              <CardTitle>Productivity Dimensions</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RadarChart data={radarData}>
                  <PolarGrid strokeDasharray="3 3" />
                  <PolarAngleAxis dataKey="metric" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  <Radar 
                    name="Score" 
                    dataKey="value" 
                    stroke={CHART_COLORS.primary} 
                    fill={CHART_COLORS.primary} 
                    fillOpacity={0.6} 
                  />
                  <Tooltip />
                </RadarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="patterns" className="space-y-4">
          {/* Hourly Pattern */}
          <Card>
            <CardHeader>
              <CardTitle>Writing by Hour</CardTitle>
              <CardDescription>
                Discover your most productive hours of the day
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={hourlyChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="words" fill={CHART_COLORS.primary} radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Weekday Pattern */}
          <Card>
            <CardHeader>
              <CardTitle>Writing by Day of Week</CardTitle>
              <CardDescription>
                Your weekly writing patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={data.patterns.weekday}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="words" fill={CHART_COLORS.secondary} radius={[4, 4, 0, 0]}>
                    {data.patterns.weekday.map((entry, index) => (
                      <Cell 
                        key={`cell-${index}`} 
                        fill={entry.words > data.averages.wordsPerDay ? CHART_COLORS.success : CHART_COLORS.secondary} 
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          {data.trends && (
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base font-medium">Word Count Trend</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    {data.trends.wordsTrend > 0 ? (
                      <TrendingUp className="h-5 w-5 text-green-600" />
                    ) : data.trends.wordsTrend < 0 ? (
                      <TrendingDown className="h-5 w-5 text-red-600" />
                    ) : (
                      <Activity className="h-5 w-5 text-muted-foreground" />
                    )}
                    <span className={`text-2xl font-bold ${
                      data.trends.wordsTrend > 0 ? 'text-green-600' : 
                      data.trends.wordsTrend < 0 ? 'text-red-600' : ''
                    }`}>
                      {data.trends.wordsTrend > 0 ? '+' : ''}{data.trends.wordsTrend}%
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    compared to previous {timeframe}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base font-medium">Active Days Trend</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    {data.trends.daysTrend > 0 ? (
                      <TrendingUp className="h-5 w-5 text-green-600" />
                    ) : data.trends.daysTrend < 0 ? (
                      <TrendingDown className="h-5 w-5 text-red-600" />
                    ) : (
                      <Activity className="h-5 w-5 text-muted-foreground" />
                    )}
                    <span className={`text-2xl font-bold ${
                      data.trends.daysTrend > 0 ? 'text-green-600' : 
                      data.trends.daysTrend < 0 ? 'text-red-600' : ''
                    }`}>
                      {data.trends.daysTrend > 0 ? '+' : ''}{data.trends.daysTrend}%
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    compared to previous {timeframe}
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          {data.insights && data.insights.length > 0 ? (
            data.insights.map((insight, index) => (
              <Card key={index}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {insight.type === 'consistency' && <Calendar className="h-4 w-4" />}
                      {insight.type === 'timing' && <Clock className="h-4 w-4" />}
                      {insight.type === 'focus' && <Target className="h-4 w-4" />}
                      {insight.type === 'volume' && <Zap className="h-4 w-4" />}
                      {insight.type === 'pattern' && <Brain className="h-4 w-4" />}
                      <CardTitle className="text-base">{insight.title}</CardTitle>
                    </div>
                    <Badge variant={
                      insight.priority === 'high' ? 'destructive' :
                      insight.priority === 'medium' ? 'default' :
                      'secondary'
                    }>
                      {insight.priority}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{insight.message}</p>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Lightbulb className="h-4 w-4" />
                  <p>No insights available yet. Keep writing to generate personalized recommendations!</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}