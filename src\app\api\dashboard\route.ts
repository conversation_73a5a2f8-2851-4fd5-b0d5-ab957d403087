import { NextRequest, NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { WritingAnalyticsService } from '@/lib/services/writing-analytics-service';
import { ProjectService } from '@/lib/services/project-service';
import { UserService } from '@/lib/services/user-service';
import { TaskService } from '@/lib/services/task-service';
import { handleAPIError, AuthenticationError } from '@/lib/api/error-handler';
import { logger } from '@/lib/services/logger';

export async function GET(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const searchParams = request.nextUrl.searchParams;
    const timeframe = searchParams.get('timeframe') || 'week';
    const includeAnalytics = searchParams.get('analytics') !== 'false';
    const includeProjects = searchParams.get('projects') !== 'false';

    // Initialize services
    const analyticsService = new WritingAnalyticsService();
    const projectService = new ProjectService();
    const userService = new UserService();
    const taskService = new TaskService();

    await Promise.all([
      analyticsService.initialize(),
      projectService.initialize(),
      userService.initialize(),
      taskService.initialize()
    ]);

    // Gather dashboard data in parallel
    const [
      productivityMetrics,
      userProjects,
      userProfile,
      recentTasks,
      sessionAnalytics,
      goalProgress
    ] = await Promise.allSettled([
      // Productivity metrics
      includeAnalytics 
        ? analyticsService.getProductivityMetrics(user.id, { 
            timeframe: timeframe as 'day' | 'week' | 'month' | 'year',
            includeInsights: true 
          })
        : Promise.resolve({ success: false, data: null }),

      // User projects
      includeProjects 
        ? projectService.getUserProjects(user.id, { limit: 20 })
        : Promise.resolve({ success: false, data: [] }),

      // User profile
      userService.getUserProfile(user.id),

      // Recent tasks
      taskService.getUserTasks(user.id, { limit: 5 }),

      // Session analytics
      includeAnalytics 
        ? analyticsService.getSessionAnalytics(user.id, { 
            type: 'overview',
            startDate: getTimeframeDateRange(timeframe).startDate,
            endDate: getTimeframeDateRange(timeframe).endDate
          })
        : Promise.resolve({ success: false, data: null }),

      // Goal progress
      includeAnalytics 
        ? analyticsService.getUserTasks(user.id, { 
            taskType: 'check_writing_goals',
            limit: 3 
          })
        : Promise.resolve({ success: false, data: [] })
    ]);

    // Process results
    const dashboardData: DashboardData = {
      user: {
        id: user.id,
        email: user.email
      },
      timeframe,
      lastUpdated: new Date().toISOString()
    };

    // Add productivity metrics
    if (productivityMetrics.status === 'fulfilled' && productivityMetrics.value.success) {
      dashboardData.productivity = productivityMetrics.value.data;
    } else {
      dashboardData.productivity = {
        overview: {
          totalWords: 0,
          totalTime: 0,
          totalSessions: 0,
          activeDays: 0,
          totalDays: getTimeframeDateRange(timeframe).totalDays,
          productivityScore: 0
        },
        averages: {
          wordsPerDay: 0,
          timePerDay: 0,
          sessionsPerDay: 0,
          wordsPerSession: 0,
          sessionDuration: 0
        }
      };
    }

    // Add projects data
    if (userProjects.status === 'fulfilled' && userProjects.value.success) {
      const projects = userProjects.value.data;
      dashboardData.projects = {
        total: projects.length,
        active: projects.filter(p => p.status === 'active').length,
        completed: projects.filter(p => p.status === 'completed').length,
        recent: projects.slice(0, 5).map(p => ({
          id: p.id,
          title: p.title,
          status: p.status,
          wordCount: p.word_count || 0,
          targetWordCount: p.target_word_count || 0,
          progress: p.target_word_count 
            ? Math.round((p.word_count || 0) / p.target_word_count * 100)
            : 0,
          lastModified: p.updated_at
        }))
      };
    } else {
      dashboardData.projects = {
        total: 0,
        active: 0,
        completed: 0,
        recent: []
      };
    }

    // Add user profile info
    if (userProfile.status === 'fulfilled' && userProfile.value.success) {
      const profile = userProfile.value.data;
      dashboardData.user = {
        ...dashboardData.user,
        name: profile.full_name || profile.username,
        timezone: profile.timezone,
        preferences: profile.preferences || {}
      };
    }

    // Add recent tasks/activity
    if (recentTasks.status === 'fulfilled' && recentTasks.value.success) {
      dashboardData.recentActivity = recentTasks.value.data.map(task => ({
        id: task.id,
        type: task.task_type,
        status: task.status,
        createdAt: task.created_at,
        completedAt: task.completed_at
      }));
    } else {
      dashboardData.recentActivity = [];
    }

    // Add session analytics
    if (sessionAnalytics.status === 'fulfilled' && sessionAnalytics.value.success) {
      const analytics = sessionAnalytics.value.data;
      dashboardData.sessions = {
        overview: analytics.overview || {},
        streak: analytics.currentStreak || 0,
        longestStreak: analytics.longestStreak || 0
      };
    }

    // Calculate quick stats
    dashboardData.quickStats = {
      wordsToday: 0, // Would be calculated from today's sessions
      goalProgress: 0, // Would be calculated from active goals
      streakDays: dashboardData.sessions?.streak || 0,
      projectsActive: dashboardData.projects.active
    };

    // Add writing insights
    if (dashboardData.productivity.insights) {
      dashboardData.insights = dashboardData.productivity.insights.slice(0, 3);
    } else {
      dashboardData.insights = [];
    }

    // Add upcoming deadlines (mock data for now)
    dashboardData.upcomingDeadlines = getUpcomingDeadlines(dashboardData.projects.recent);

    logger.info('Dashboard data fetched', {
      userId: user.id,
      timeframe,
      projectsCount: dashboardData.projects.total,
      totalWords: dashboardData.productivity.overview.totalWords
    });

    return NextResponse.json({
      success: true,
      dashboard: dashboardData
    });

  } catch (error) {
    logger.error('Error fetching dashboard data:', error);
    return handleAPIError(error);
  }
}

function getTimeframeDateRange(timeframe: string) {
  const endDate = new Date();
  let startDate = new Date();
  let totalDays = 7;

  switch (timeframe) {
    case 'week':
      startDate.setDate(endDate.getDate() - 7);
      totalDays = 7;
      break;
    case 'month':
      startDate.setMonth(endDate.getMonth() - 1);
      totalDays = 30;
      break;
    case 'quarter':
      startDate.setMonth(endDate.getMonth() - 3);
      totalDays = 90;
      break;
    case 'year':
      startDate.setFullYear(endDate.getFullYear() - 1);
      totalDays = 365;
      break;
    default:
      startDate.setDate(endDate.getDate() - 7);
      totalDays = 7;
  }

  return {
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    totalDays
  };
}

function getUpcomingDeadlines(projects: Project[]): UpcomingDeadline[] {
  // Mock upcoming deadlines - in a real implementation, this would come from goals or project deadlines
  const mockDeadlines = [
    {
      id: 'goal_1',
      title: 'Complete Chapter 5',
      type: 'chapter',
      dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
      priority: 'high',
      progress: 75
    },
    {
      id: 'goal_2', 
      title: 'Weekly Writing Goal',
      type: 'goal',
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week from now
      priority: 'medium',
      progress: 60
    }
  ];

  return mockDeadlines;
}