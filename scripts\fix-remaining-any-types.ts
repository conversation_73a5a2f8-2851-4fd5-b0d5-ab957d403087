import * as fs from 'fs';
import * as path from 'path';

interface AnyTypeOccurrence {
  file: string;
  line: number;
  content: string;
  context: string;
}

const anyTypeFixes: Record<string, string> = {
  // Common fixes
  'status: \'pending\' as any,': 'status: \'pending\' as const,',
  'status: \'processing\' as any,': 'status: \'processing\' as const,',
  'timeframe: timeframe as any,': 'timeframe: timeframe as \'day\' | \'week\' | \'month\' | \'year\',',
  'data: data as any': 'data: data',
  'const privacyUpdates: any = {}': 'const privacyUpdates: Partial<{ analytics_enabled: boolean; personalization_enabled: boolean; email_notifications_enabled: boolean }> = {}',
  'const dashboardData: any = {': 'const dashboardData: DashboardData = {',
  'projects.filter((p: any) => p.status === \'active\')': 'projects.filter(p => p.status === \'active\')',
  'projects.filter((p: any) => p.status === \'completed\')': 'projects.filter(p => p.status === \'completed\')',
  'projects.slice(0, 5).map((p: any) => ({': 'projects.slice(0, 5).map(p => ({',
  'recentTasks.value.data.map((task: any) => ({': 'recentTasks.value.data.map(task => ({',
  'function getUpcomingDeadlines(projects: any[]): any[]': 'function getUpcomingDeadlines(projects: Project[]): UpcomingDeadline[]',
  '.findIndex((c: any) => c.id === chapterId)': '.findIndex(c => c.id === chapterId)',
  'async function processEmailTask(task: any): Promise<any>': 'async function processEmailTask(task: ProcessingTask): Promise<ProcessingResult>',
  'async function processExportTask(task: any): Promise<any>': 'async function processExportTask(task: ProcessingTask): Promise<ProcessingResult>',
  'async function processErasureTask(task: any): Promise<any>': 'async function processErasureTask(task: ProcessingTask): Promise<ProcessingResult>',
  'async function processDataExportTask(task: any): Promise<any>': 'async function processDataExportTask(task: ProcessingTask): Promise<ProcessingResult>',
  'async function processGoalReminderTask(task: any): Promise<any>': 'async function processGoalReminderTask(task: ProcessingTask): Promise<ProcessingResult>',
  'async function processEmbeddingsTask(task: any): Promise<any>': 'async function processEmbeddingsTask(task: ProcessingTask): Promise<ProcessingResult>',
  'selectionData: z.record(z.any()).optional(),': 'selectionData: z.record(z.unknown()).optional(),',
  'outcomeData: z.record(z.any()).optional()': 'outcomeData: z.record(z.unknown()).optional()',
  'existingProfile: z.any().optional()': 'existingProfile: z.unknown().optional()',
  'parameters: z.record(z.any()).optional(),': 'parameters: z.record(z.unknown()).optional(),',
  'Record<string, any>': 'Record<string, unknown>',
  'Record<string, any[]>': 'Record<string, unknown[]>',
  'const milestonesByAspect: Record<string, any[]>': 'const milestonesByAspect: Record<string, Milestone[]>',
  '(customer as any).deleted': '(\'deleted\' in customer && customer.deleted)',
  'const { collaboration, isOwner, isSelf } = context.customData as any;': 'const { collaboration, isOwner, isSelf } = context.customData as { collaboration: Collaboration; isOwner: boolean; isSelf: boolean };',
};

// Additional type definitions to add
const typeDefinitions = `
// Type definitions for fixing 'any' types
interface DashboardData {
  user: {
    id: string;
    email: string;
    displayName: string;
    avatar: string | null;
  };
  projects: {
    total: number;
    active: number;
    completed: number;
    recent: Array<{
      id: string;
      title: string;
      genre: string;
      wordCount: number;
      lastUpdated: string;
      status: string;
    }>;
  };
  analytics?: any;
  writingGoals?: any;
  recentActivity?: Array<{
    id: string;
    type: string;
    description: string;
    createdAt: string;
  }>;
  upcomingDeadlines?: UpcomingDeadline[];
}

interface UpcomingDeadline {
  id: string;
  title: string;
  dueDate: string;
  type: string;
}

interface ProcessingTask {
  id: string;
  type: string;
  status: string;
  data: Record<string, unknown>;
  user_id: string;
  created_at: string;
}

interface ProcessingResult {
  success: boolean;
  message?: string;
  data?: unknown;
}

interface Milestone {
  type: string;
  description: string;
  timestamp: string;
}

interface Project {
  id: string;
  title: string;
  status: string;
  [key: string]: unknown;
}

interface Collaboration {
  id: string;
  [key: string]: unknown;
}
`;

function findAnyTypes(dir: string): AnyTypeOccurrence[] {
  const occurrences: AnyTypeOccurrence[] = [];
  
  function scanDirectory(currentDir: string) {
    const files = fs.readdirSync(currentDir, { withFileTypes: true });
    
    for (const file of files) {
      const filePath = path.join(currentDir, file.name);
      
      if (file.isDirectory() && !file.name.includes('node_modules')) {
        scanDirectory(filePath);
      } else if (file.name === 'route.ts') {
        const content = fs.readFileSync(filePath, 'utf-8');
        const lines = content.split('\n');
        
        lines.forEach((line, index) => {
          if (line.includes('any') && !line.includes('// @ts-ignore')) {
            occurrences.push({
              file: filePath,
              line: index + 1,
              content: line.trim(),
              context: lines.slice(Math.max(0, index - 2), Math.min(lines.length, index + 3)).join('\n')
            });
          }
        });
      }
    }
  }
  
  scanDirectory(dir);
  return occurrences;
}

function fixAnyTypes(occurrences: AnyTypeOccurrence[]) {
  const fileChanges = new Map<string, string[]>();
  
  for (const occurrence of occurrences) {
    if (!fileChanges.has(occurrence.file)) {
      const content = fs.readFileSync(occurrence.file, 'utf-8');
      fileChanges.set(occurrence.file, content.split('\n'));
    }
    
    const lines = fileChanges.get(occurrence.file)!;
    const lineIndex = occurrence.line - 1;
    
    // Apply known fixes
    let fixed = false;
    for (const [pattern, replacement] of Object.entries(anyTypeFixes)) {
      if (lines[lineIndex].includes(pattern)) {
        lines[lineIndex] = lines[lineIndex].replace(pattern, replacement);
        fixed = true;
        console.log(`✅ Fixed: ${occurrence.file}:${occurrence.line}`);
        console.log(`   From: ${pattern}`);
        console.log(`   To:   ${replacement}`);
        break;
      }
    }
    
    if (!fixed) {
      console.log(`⚠️  Could not auto-fix: ${occurrence.file}:${occurrence.line}`);
      console.log(`   Line: ${occurrence.content}`);
    }
  }
  
  // Write back modified files
  for (const [file, lines] of fileChanges) {
    // Add type definitions to dashboard route if needed
    if (file.includes('dashboard/route.ts') && !lines.join('\n').includes('interface DashboardData')) {
      const importIndex = lines.findIndex(line => line.includes('import'));
      lines.splice(importIndex + 1, 0, '', typeDefinitions);
    }
    
    fs.writeFileSync(file, lines.join('\n'));
    console.log(`\n💾 Updated: ${file}`);
  }
}

// Main execution
console.log('🔍 Searching for remaining \'any\' types in API routes...\n');

const apiDir = path.join(__dirname, '../src/app/api');
const occurrences = findAnyTypes(apiDir);

console.log(`\nFound ${occurrences.length} occurrences of 'any' type\n`);

if (occurrences.length > 0) {
  console.log('🔧 Applying fixes...\n');
  fixAnyTypes(occurrences);
  
  // Re-scan to see what's left
  const remaining = findAnyTypes(apiDir);
  console.log(`\n✨ Fixes applied! ${occurrences.length - remaining.length} occurrences fixed.`);
  
  if (remaining.length > 0) {
    console.log(`\n⚠️  ${remaining.length} occurrences still need manual fixing:`);
    remaining.forEach(occ => {
      console.log(`\n${occ.file}:${occ.line}`);
      console.log(`   ${occ.content}`);
    });
  }
} else {
  console.log('✅ No \'any\' types found in API routes!');
}
