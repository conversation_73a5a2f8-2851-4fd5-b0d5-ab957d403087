# API Authentication Pattern Issues Report

Generated: 2025-08-06T01:37:37.900Z

Total issues found: 147

## C:/Users/<USER>/BookScribe/src/app/api/achievements/check/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

- **Line 18**: Missing error handling after authentication
  - Pattern: `const user = await UnifiedAuthService.authenticateUser(request)`
  - Fix: Add: if (!user) { return handleAPIError(new AuthenticationError()); }

## C:/Users/<USER>/BookScribe/src/app/api/achievements/stats/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/admin/export-data/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/admin/security/health/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/agents/adjust-plan/route.ts

- **Line 65**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/agents/chat/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/agents/generate/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

- **Line 62**: Missing error handling after authentication
  - Pattern: `const user = await UnifiedAuthService.authenticateUser(request)`
  - Fix: Add: if (!user) { return handleAPIError(new AuthenticationError()); }

## C:/Users/<USER>/BookScribe/src/app/api/agents/initialize/route.ts

- **Line 82**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser()`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

- **Line 310**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser()`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/agents/suggestions/route.ts

- **Line 27**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/ai/structured-content/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/ai/suggestions/[id]/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

- **Line 23**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser()`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

- **Line 69**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser()`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

- **Line 133**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser()`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/analysis/arc-predictions/route.ts

- **Line 19**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/analysis/arc-suggestions/route.ts

- **Line 25**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/analysis/auto-fix/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/analysis/book-summary/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/analysis/character-arc-patterns/route.ts

- **Line 65**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/analysis/character-development-grid/route.ts

- **Line 64**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/analysis/progress/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/analytics/chapters/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

- **Line 10**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser()`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

- **Line 88**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser()`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/analytics/export/route.ts

- **Line 17**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/analytics/profiles/performance/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/analytics/recommendations/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

- **Line 37**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser()`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/analytics/selections/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/analytics/selections/success-patterns/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/billing/subscriptions/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

- **Line 23**: Missing error handling after authentication
  - Pattern: `const authResult = await UnifiedAuthService.authenticateUser(request)`
  - Fix: Add: if (!user) { return handleAPIError(new AuthenticationError()); }

- **Line 64**: Missing error handling after authentication
  - Pattern: `const user = await UnifiedAuthService.authenticateUser(request)`
  - Fix: Add: if (!user) { return handleAPIError(new AuthenticationError()); }

## C:/Users/<USER>/BookScribe/src/app/api/billing/webhooks/stripe/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/chapters/[id]/route.ts

- **Line 32**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 88**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 218**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/characters/[id]/share/route.ts

- **Line 21**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 130**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/collaboration/change/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/collaboration/cursor/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/collaboration/join/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/collaboration/leave/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/collaboration/lock/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/collaboration/sessions/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/collaboration/unlock/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/consistency/check/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

- **Line 19**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser()`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/cron/generate-embeddings/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/cron/process-email-queue/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/cron/process-export-queue/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/cron/process-reference-materials/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/demo/generate-story/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/email/preferences/route.ts

- **Line 27**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 49**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/email/queue/process/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/email/send/route.ts

- **Line 55**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/goals/progress/route.ts

- **Line 17**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 61**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/goals/recommendations/route.ts

- **Line 20**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/goals/route.ts

- **Line 28**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 83**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 150**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 189**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/profiles/public/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/project-collaborators/[id]/route.ts

- **Line 21**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 68**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 164**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/projects/[id]/chapters/route.ts

- **Line 19**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 147**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/projects/[id]/characters/route.ts

- **Line 19**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 157**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/projects/[id]/collaborators/invite/route.ts

- **Line 20**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/projects/[id]/locations/route.ts

- **Line 22**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 73**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/projects/[id]/plot-threads/route.ts

- **Line 21**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 100**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/relationships/analyze/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/relationships/graph/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/sample-projects/route.ts

- **Line 14**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/search/character-moments/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/search/emotion/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/search/related/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/search/theme/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/security/validate/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/series/route.ts

- **Line 28**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 76**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/series/[id]/analytics/route.ts

- **Line 18**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/series/[id]/books/route.ts

- **Line 17**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 100**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/series/[id]/character-arcs/route.ts

- **Line 18**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 47**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 80**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/series/[id]/characters/route.ts

- **Line 21**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 60**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/series/[id]/continuity/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/series/[id]/continuity-issues/route.ts

- **Line 18**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 51**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 91**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/series/[id]/route.ts

- **Line 27**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 76**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 133**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/series/[id]/universe/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

## C:/Users/<USER>/BookScribe/src/app/api/series/[id]/universe-rules/route.ts

- **Line 18**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 51**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/services/analytics/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

- **Line 20**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser();`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/services/content/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

- **Line 21**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser();`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

- **Line 91**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser();`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/services/health/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/services/orchestrator/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

- **Line 20**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser();`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

- **Line 83**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser();`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/story-bible/bulk/route.ts

- **Line 60**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/story-bible/[id]/route.ts

- **Line 17**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 69**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 141**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/universes/route.ts

- **Line 13**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 47**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/universes/timeline-events/route.ts

- **Line 14**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 86**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 149**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 214**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/universes/[id]/route.ts

- **Line 21**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 61**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 102**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: user, error: authError } = await supabase.auth.getUser();`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/user/cookie-consent/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

- **Line 21**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user } } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/version-history/[id]/route.ts

- **Line 1**: Missing AuthenticationError import
  - Pattern: `Missing import`
  - Fix: Add AuthenticationError to error-handler import

- **Line 22**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser()`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

- **Line 86**: Using undefined authenticateUser()
  - Pattern: `const authResult = await authenticateUser()`
  - Fix: Use: UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/webhooks/stripe/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/workers/task-queue/route.ts

- **Line 1**: Missing UnifiedAuthService import
  - Pattern: `Missing import`
  - Fix: Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'

## C:/Users/<USER>/BookScribe/src/app/api/writing/goals/progress/route.ts

- **Line 11**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/writing/goals/route.ts

- **Line 20**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 93**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/writing/goals/[id]/route.ts

- **Line 21**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 93**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

- **Line 165**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)

## C:/Users/<USER>/BookScribe/src/app/api/writing/sessions/route.ts

- **Line 193**: Using direct supabase.auth.getUser()
  - Pattern: `const { data: { user }, error: authError } = await supabase.auth.getUser()`
  - Fix: Use: const user = await UnifiedAuthService.authenticateUser(request)


## Quick Fix Commands

```bash
# Add missing imports
grep -L "UnifiedAuthService" src/app/api/**/route.ts | xargs -I {} sed -i '1i\import { UnifiedAuthService } from "@/lib/auth/unified-auth-service"' {}

# Fix authenticateUser patterns
find src/app/api -name "route.ts" -exec sed -i 's/const authResult = await authenticateUser()/const user = await UnifiedAuthService.authenticateUser(request)/g' {} \;
```
