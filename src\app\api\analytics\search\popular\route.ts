import { NextResponse } from 'next/server'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { ServiceManager } from '@/lib/services/service-manager'
import { baseSchemas } from '@/lib/validation/common-schemas'

const querySchema = z.object({
  projectId: baseSchemas.uuid,
  limit: z.string().optional().default('5').transform(val => Math.min(parseInt(val, 10), 10))
})

export const GET = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const { searchParams } = new URL(request.url);
  const queryParams = {
    projectId: searchParams.get('projectId'),
    limit: searchParams.get('limit') || '5'
  };

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    querySchema: querySchema,
    rateLimitKey: 'popular-searches',
    rateLimitCost: 2,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      // Verify project access
      if (queryParams.projectId) {
        const serviceManager = ServiceManager.getInstance();
        const projectService = await serviceManager.getProjectService();
        
        if (!projectService) {
          return { valid: false, error: 'Service temporarily unavailable' };
        }
        
        const accessResponse = await projectService.checkProjectAccess(queryParams.projectId, user.id);
        if (!accessResponse.success || !accessResponse.data) {
          return { valid: false, error: 'Access denied to this project' };
        }
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { projectId, limit } = context.query;

  try {
    const serviceManager = ServiceManager.getInstance();
    const analyticsService = await serviceManager.getWritingAnalyticsService();
    
    if (!analyticsService) {
      logger.error('WritingAnalyticsService not available');
      return UnifiedResponse.error('Service temporarily unavailable', 503);
    }

    // Get popular searches from service
    const response = await analyticsService.getPopularSearches(projectId, {
      limit,
      since: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days
    });

    if (!response.success) {
      logger.error('Failed to get popular searches:', response.error, {
        userId: user.id,
        projectId,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error(response.error || 'Failed to get popular searches');
    }

    logger.info('Popular searches retrieved', {
      userId: user.id,
      projectId,
      searchCount: response.data?.length || 0,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({
      searches: response.data || [],
      limit,
      period: '30days'
    });

  } catch (error) {
    logger.error('Popular searches error:', error, {
      userId: user.id,
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to get popular searches');
  }
})