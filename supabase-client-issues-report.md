# Supabase Client Pattern Issues Report

Generated: 2025-08-06T01:42:12.481Z

Total issues found: 109

## Issues by Type

### Using non-typed createClient (102 occurrences)

- **C:/Users/<USER>/BookScribe/src/app/(app)/playground/page.tsx:43**
  - Pattern: `const supabase = createClient();`
  - Fix: Use: createTypedBrowserClient()

- **C:/Users/<USER>/BookScribe/src/app/(auth)/callback/route.ts:11**
  - Pattern: `const supabase = await createClient()`
  - Fix: Use: createTypedBrowserClient()

- **C:/Users/<USER>/BookScribe/src/app/(auth)/forgot-password/page.tsx:18**
  - Pattern: `const supabase = createClient()`
  - Fix: Use: createTypedBrowserClient()

- **C:/Users/<USER>/BookScribe/src/app/(auth)/reset-password/page.tsx:25**
  - Pattern: `const supabase = createClient()`
  - Fix: Use: createTypedBrowserClient()

- **C:/Users/<USER>/BookScribe/src/app/(auth)/signout/route.ts:5**
  - Pattern: `const supabase = await createClient()`
  - Fix: Use: createTypedBrowserClient()

... and 97 more

### Using old import path (6 occurrences)

- **C:/Users/<USER>/BookScribe/src/app/memory/layout.tsx:2**
  - Pattern: `import { createServerClient } from '@/lib/supabase/server'`
  - Fix: Use: import { createTypedServerClient, createTypedBrowserClient, getBrowserClient } from '@/lib/supabase'

- **C:/Users/<USER>/BookScribe/src/app/story-bible/layout.tsx:2**
  - Pattern: `import { createServerClient } from '@/lib/supabase/server'`
  - Fix: Use: import { createTypedServerClient, createTypedBrowserClient, getBrowserClient } from '@/lib/supabase'

- **C:/Users/<USER>/BookScribe/src/app/tasks/layout.tsx:2**
  - Pattern: `import { createServerClient } from '@/lib/supabase/server'`
  - Fix: Use: import { createTypedServerClient, createTypedBrowserClient, getBrowserClient } from '@/lib/supabase'

- **C:/Users/<USER>/BookScribe/src/app/timeline/layout.tsx:2**
  - Pattern: `import { createServerClient } from '@/lib/supabase/server'`
  - Fix: Use: import { createTypedServerClient, createTypedBrowserClient, getBrowserClient } from '@/lib/supabase'

- **C:/Users/<USER>/BookScribe/src/components/auth/session-refresh-provider.tsx:4**
  - Pattern: `import { createClient } from '@/lib/supabase/client'`
  - Fix: Use: import { createTypedServerClient, createTypedBrowserClient, getBrowserClient } from '@/lib/supabase'

... and 1 more

### Importing createClient from @supabase/supabase-js directly (1 occurrences)

- **C:/Users/<USER>/BookScribe/src/lib/supabase/admin.ts:1**
  - Pattern: `import { createClient } from '@supabase/supabase-js'`
  - Fix: Use: import { createTypedServerClient } from '@/lib/supabase'


## Correct Usage Patterns

### API Routes
```typescript
import { createTypedServerClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  const supabase = await createTypedServerClient()
  // ...
}
```

### Client Components
```typescript
'use client'
import { getBrowserClient } from '@/lib/supabase'

export function Component() {
  const supabase = getBrowserClient()
  // ...
}
```

### Server Components
```typescript
import { createTypedServerClient } from '@/lib/supabase'

export async function Component() {
  const supabase = await createTypedServerClient()
  // ...
}
```
