import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'
import { taskQueueService } from '@/lib/services/task-queue-service'
import { mailerooEmailService as emailService } from '@/lib/services/maileroo-email-service'

export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Create a data export task
    const taskId = await taskQueueService.queueTask('export_user_data', {
      userId: user.id,
      email: user.email!,
      requestedAt: new Date().toISOString()
    }, {
      userId: user.id,
      priority: 'normal'
    })

    // Note: Email notification removed per user request

    // Log the export request
    await supabase
      .from('user_data_requests')
      .insert({
        user_id: user.id,
        request_type: 'export',
        status: 'pending',
        task_id: taskId,
        requested_at: new Date().toISOString()
      })

    return NextResponse.json({
      success: true,
      taskId,
      message: 'Data export requested. You will receive an email when it\'s ready.'
    })
  } catch (error) {
    console.error('Error requesting data export:', error)
    return NextResponse.json(
      { error: 'Failed to request data export' },
      { status: 500 }
    )
  }
})