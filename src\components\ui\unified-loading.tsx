'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'
import { Sparkles } from 'lucide-react'
import { BookOpen } from 'lucide-react'
import { Wand2 } from 'lucide-react'
import { FileText } from 'lucide-react'
import { Users } from 'lucide-react'
import { Skeleton as ShadcnSkeleton } from '@/components/ui/skeleton'
import { Card, CardContent, CardHeader } from '@/components/ui/card'

// ============================================================================
// UNIFIED LOADING SYSTEM - Consolidates all loading components
// ============================================================================

// Types
type LoadingSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl'
type LoadingVariant = 'spinner' | 'dots' | 'pulse' | 'skeleton'
type LoadingTheme = 'default' | 'writing' | 'ai' | 'project' | 'characters'

interface LoadingProps {
  size?: LoadingSize
  variant?: LoadingVariant
  theme?: LoadingTheme
  text?: string
  className?: string
}

// Size mappings
const sizeClasses = {
  xs: 'h-3 w-3',
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
}

const textSizeClasses = {
  xs: 'text-mono-xs',
  sm: 'text-mono-sm',
  md: 'text-mono-base',
  lg: 'text-mono-lg',
  xl: 'text-mono-xl'
}

// Theme icons
const themeIcons = {
  default: Sparkles,
  writing: FileText,
  ai: Wand2,
  project: BookOpen,
  characters: Users,
}

// ============================================================================
// BASE LOADING COMPONENT
// ============================================================================

export function Loading({
  size = 'md',
  variant = 'spinner',
  theme = 'default',
  text,
  className
}: LoadingProps) {
  const renderLoader = () => {
    switch (variant) {
      case 'dots':
        return <DotsLoader size={size} />
      case 'pulse':
        return <PulseLoader size={size} theme={theme} />
      case 'skeleton':
        return <SkeletonLoader size={size} />
      case 'spinner':
      default:
        return <SpinnerLoader size={size} />
    }
  }

  if (text) {
    return (
      <div className={cn('flex flex-col items-center gap-3', className)}>
        {renderLoader()}
        <p className={cn(
          'text-muted-foreground animate-pulse font-mono font-bold',
          textSizeClasses[size]
        )}>
          {text}
        </p>
      </div>
    )
  }

  return (
    <div className={cn('flex items-center justify-center', className)}>
      {renderLoader()}
    </div>
  )
}

// ============================================================================
// LOADER VARIANTS
// ============================================================================

function SpinnerLoader({ size }: { size: LoadingSize }) {
  return (
    <Loader2 className={cn(
      'animate-spin text-primary',
      sizeClasses[size]
    )} />
  )
}

function DotsLoader({ size }: { size: LoadingSize }) {
  const dotSizeMap = {
    xs: 'h-0.5 w-0.5',
    sm: 'h-1 w-1',
    md: 'h-2 w-2',
    lg: 'h-3 w-3',
    xl: 'h-4 w-4'
  }
  
  return (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            'bg-primary rounded-full animate-bounce',
            dotSizeMap[size]
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '0.8s'
          }}
        />
      ))}
    </div>
  )
}

function PulseLoader({ size, theme }: { size: LoadingSize; theme: LoadingTheme }) {
  const Icon = themeIcons[theme]
  
  return (
    <div className="relative">
      <div className={cn(
        'absolute rounded-full bg-primary/20 animate-ping',
        sizeClasses[size]
      )} />
      <Icon className={cn(
        'relative animate-pulse text-primary',
        sizeClasses[size]
      )} />
    </div>
  )
}

function SkeletonLoader({ size }: { size: LoadingSize }) {
  const heights = {
    xs: 12,
    sm: 16,
    md: 24,
    lg: 32,
    xl: 48
  }
  
  return (
    <ShadcnSkeleton 
      className={cn('rounded', sizeClasses[size])}
      style={{ height: heights[size] }}
    />
  )
}

// ============================================================================
// SPECIALIZED LOADING STATES
// ============================================================================

// Page-level loading
export function PageLoading({ text = 'Loading...' }: { text?: string }) {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <Loading size="lg" variant="spinner" text={text} />
    </div>
  )
}

// Section loading
export function SectionLoading({ className }: { className?: string }) {
  return (
    <div className={cn('flex items-center justify-center py-12', className)}>
      <Loading size="md" variant="spinner" />
    </div>
  )
}

// Inline loading (for buttons, etc)
export function InlineLoading({ className }: { className?: string }) {
  return <Loading size="sm" variant="spinner" className={cn('inline-flex', className)} />
}

// ============================================================================
// SKELETON PRESETS
// ============================================================================

export function SkeletonCard({ className }: { className?: string }) {
  return (
    <Card className={className}>
      <CardHeader>
        <ShadcnSkeleton className="h-6 w-48" />
        <ShadcnSkeleton className="h-4 w-32 mt-2" />
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <ShadcnSkeleton className="h-4 w-full" />
          <ShadcnSkeleton className="h-4 w-3/4" />
          <ShadcnSkeleton className="h-4 w-1/2" />
        </div>
      </CardContent>
    </Card>
  )
}

export function SkeletonList({ items = 3, className }: { items?: number; className?: string }) {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: items }).map((_, i) => (
        <div key={i} className="flex items-center space-x-3">
          <ShadcnSkeleton className="h-10 w-10 rounded-full" />
          <div className="space-y-2 flex-1">
            <ShadcnSkeleton className="h-4 w-48" />
            <ShadcnSkeleton className="h-3 w-32" />
          </div>
        </div>
      ))}
    </div>
  )
}

export function SkeletonTable({ rows = 5, cols = 4, className }: { rows?: number; cols?: number; className?: string }) {
  return (
    <div className={cn('w-full', className)}>
      <div className="flex gap-4 pb-2 border-b">
        {Array.from({ length: cols }).map((_, i) => (
          <ShadcnSkeleton key={i} className="h-4 flex-1" />
        ))}
      </div>
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex gap-4 py-2">
          {Array.from({ length: cols }).map((_, j) => (
            <ShadcnSkeleton key={j} className="h-4 flex-1" />
          ))}
        </div>
      ))}
    </div>
  )
}

export function SkeletonForm({ fields = 4, className }: { fields?: number; className?: string }) {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: fields }).map((_, i) => (
        <div key={i} className="space-y-2">
          <ShadcnSkeleton className="h-4 w-20" />
          <ShadcnSkeleton className="h-10 w-full" />
        </div>
      ))}
      <ShadcnSkeleton className="h-10 w-32" />
    </div>
  )
}

// ============================================================================
// CONTEXT-SPECIFIC LOADERS
// ============================================================================

export function WritingLoading({ text = 'Writing your story...' }: { text?: string }) {
  return <Loading variant="pulse" theme="writing" text={text} size="lg" />
}

export function AILoading({ text = 'AI is thinking...' }: { text?: string }) {
  return <Loading variant="pulse" theme="ai" text={text} size="lg" />
}

export function ProjectLoading({ text = 'Loading project...' }: { text?: string }) {
  return <Loading variant="spinner" theme="project" text={text} size="lg" />
}

export function CharacterLoading({ text = 'Loading characters...' }: { text?: string }) {
  return <Loading variant="dots" theme="characters" text={text} size="lg" />
}

// ============================================================================
// BUTTON WITH LOADING STATE
// ============================================================================

interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean
  loadingText?: string
  variant?: 'default' | 'outline' | 'ghost' | 'literary'
  size?: 'sm' | 'default' | 'lg'
}

export function LoadingButton({
  children,
  isLoading,
  loadingText = 'Loading...',
  className,
  disabled,
  variant = 'default',
  size = 'default',
  ...props
}: LoadingButtonProps) {
  // Button styling based on variant
  const variantClasses = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    literary: 'bg-gradient-to-r from-primary to-primary/80 text-primary-foreground hover:from-primary/90 hover:to-primary/70',
  }

  const sizeClasses = {
    sm: 'h-8 px-3 text-xs',
    default: 'h-10 px-4',
    lg: 'h-12 px-6 text-lg',
  }

  return (
    <button
      disabled={isLoading || disabled}
      className={cn(
        'inline-flex items-center justify-center rounded-md font-literary-display font-semibold',
        'ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2',
        'focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      {...props}
    >
      {isLoading && <InlineLoading className="mr-2" />}
      {isLoading ? loadingText : children}
    </button>
  )
}