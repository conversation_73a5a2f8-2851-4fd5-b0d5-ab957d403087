import { NextResponse } from 'next/server'
import { handleAPIError, ValidationError, NotFoundError, AuthenticationError } from '@/lib/api/error-handler';
import type { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { ProjectService } from '@/lib/services/project-service'
import { ConsistencyValidator } from '@/lib/services/consistency-validator'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'

const ConsistencyCheckSchema = z.object({
  projectId: z.string(),
  chapterId: z.string().optional(),
  content: z.string().optional(),
  checkType: z.enum(['chapter', 'book', 'timeline', 'characters']).optional(),
})

export async function POST(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const body = await request.json()
    const { projectId, chapterId, content, checkType = 'chapter' } = ConsistencyCheckSchema.parse(body)

    // Initialize services
    const projectService = new ProjectService()
    await projectService.initialize()

    // Verify project ownership and get project data
    const projectResponse = await projectService.getProject(projectId, user.id)
    if (!projectResponse.success) {
      return handleAPIError(new NotFoundError('Project'))
    }

    const project = projectResponse.data

    // Initialize consistency validator
    const validator = new ConsistencyValidator()
    await validator.initialize()

    // Build book context from project data
    const bookContext = {
      projectId,
      settings: project.project_settings || {},
      projectSelections: project.project_selections || {},
      storyPrompt: project.story_prompt,
      targetWordCount: project.target_word_count,
      targetChapters: project.target_chapters,
      currentChapters: project.chapters || [],
      characters: project.characters || {},
      worldBuilding: project.metadata?.worldBuilding || {},
      timeline: project.metadata?.timeline || [],
    }

    // Perform consistency check based on type
    let consistencyReport
    
    switch (checkType) {
      case 'chapter':
        if (!chapterId || !content) {
          return handleAPIError(new ValidationError('Chapter ID and content required for chapter consistency check'))
        }
        const chapterNumber = project.chapters?.findIndex(c => c.id === chapterId) + 1 || 1
        consistencyReport = await validator.validateChapterConsistency(
          { content, chapterNumber },
          bookContext
        )
        break
        
      case 'book':
        consistencyReport = await validator.validateBookConsistency(bookContext)
        break
        
      case 'timeline':
        consistencyReport = await validator.validateTimelineConsistency(bookContext)
        break
        
      case 'characters':
        consistencyReport = await validator.validateCharacterConsistency(bookContext)
        break
        
      default:
        return handleAPIError(new ValidationError('Invalid request'))
    }

    if (!consistencyReport.success) {
      throw new Error(consistencyReport.error || 'Consistency check failed')
    }

    // Save consistency report using ProjectService
    try {
      await projectService.saveConsistencyReport({
        userId: user.id,
        projectId,
        chapterId: chapterId || null,
        checkType,
        overallScore: consistencyReport.data?.overallScore || 0,
        issues: consistencyReport.data?.issues || [],
        characterConsistency: consistencyReport.data?.characterConsistency || 0,
        timelineConsistency: consistencyReport.data?.timelineConsistency || 0,
        plotConsistency: consistencyReport.data?.plotConsistency || 0,
        worldConsistency: consistencyReport.data?.worldConsistency || 0,
        styleConsistency: consistencyReport.data?.styleConsistency || 0,
      })
    } catch (insertError) {
      logger.error('Error saving consistency report:', insertError)
    }

    return NextResponse.json({
      success: true,
      report: consistencyReport.data,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    logger.error('Error in consistency check route:', error)
    return handleAPIError(error)
  }
}