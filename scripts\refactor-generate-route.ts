#!/usr/bin/env node
import { readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function refactorGenerateRoute() {
  const filePath = join(__dirname, '..', 'src', 'app', 'api', 'agents', 'generate', 'route.ts');
  let content = await readFile(filePath, 'utf-8');
  
  // Remove unused createTypedServerClient import if it exists
  content = content.replace(/import\s*{\s*createTypedServerClient\s*}\s*from\s*['"]@\/lib\/supabase['"]\s*\n?/, '');
  
  // Track if we need to get services
  const needsServices = new Set<string>();
  
  // Check for remaining direct database queries
  if (content.includes('.from(\'chapters\')')) {
    needsServices.add('chapterService');
  }
  if (content.includes('.from(\'projects\')')) {
    needsServices.add('projectService');
  }
  
  // Replace remaining chapter queries
  const chapterPatterns = [
    {
      // Get chapters query
      pattern: /const\s*{\s*data:\s*chapters\s*}\s*=\s*await\s+supabase\s*\n?\s*\.from\(['"]chapters['"]\)\s*\n?\s*\.select\(['"][*]['"]\)\s*\n?\s*\.eq\(['"]project_id['"]\s*,\s*projectId\)\s*\n?\s*\.order\(['"]chapter_number['"]\)/g,
      replacement: 'const chaptersResponse = await chapterService.getProjectChapters(projectId)\n        const chapters = chaptersResponse.success ? chaptersResponse.data : []'
    },
    {
      // Get chapter by ID and number
      pattern: /const\s*{\s*data:\s*chapter\s*}\s*=\s*await\s+supabase\s*\n?\s*\.from\(['"]chapters['"]\)\s*\n?\s*\.select\(['"][*]['"]\)\s*\n?\s*\.eq\(['"]project_id['"]\s*,\s*projectId\)\s*\n?\s*\.eq\(['"]chapter_number['"]\s*,\s*chapterNumber\)\s*\n?\s*\.single\(\)/g,
      replacement: 'const chaptersResponse = await chapterService.getProjectChapters(projectId)\n          const chapter = chaptersResponse.success ? chaptersResponse.data?.find(ch => ch.chapter_number === chapterNumber) : null'
    },
    {
      // Update chapter
      pattern: /await\s+supabase\s*\n?\s*\.from\(['"]chapters['"]\)\s*\n?\s*\.update\(([^)]+)\)\s*\n?\s*\.eq\(['"]id['"]\s*,\s*chapter\.id\)/g,
      replacement: 'await chapterService.updateChapter(chapter.id, $1)'
    },
    {
      // Update project word count
      pattern: /await\s+supabase\s*\n?\s*\.from\(['"]projects['"]\)\s*\n?\s*\.update\({\s*\n?\s*current_word_count:\s*([^}]+)\s*\n?\s*}\)\s*\n?\s*\.eq\(['"]id['"]\s*,\s*projectId\)/g,
      replacement: 'await projectService.updateProject(projectId, user.id, { current_word_count: $1 })'
    }
  ];
  
  // Apply all patterns
  for (const { pattern, replacement } of chapterPatterns) {
    content = content.replace(pattern, replacement);
  }
  
  // Add service initialization if needed
  if (needsServices.size > 0 && !content.includes('const chapterService = await serviceManager.getChapterService()')) {
    // Find a good place to add service initialization (after getting project)
    const projectServiceMatch = content.match(/const project = projectResponse\.data/);
    if (projectServiceMatch) {
      const insertIndex = content.indexOf(projectServiceMatch[0]) + projectServiceMatch[0].length + 1;
      
      let serviceInit = '\n\n          // Get additional services if not already initialized\n';
      if (needsServices.has('chapterService')) {
        serviceInit += '          const chapterService = chapterService || await serviceManager.getChapterService()\n';
        serviceInit += '          if (!chapterService) {\n';
        serviceInit += '            logger.error(\'[API] Chapter service not available\')\n';
        serviceInit += '            return handleAPIError(new Error(\'Service temporarily unavailable\'))\n';
        serviceInit += '          }\n';
      }
      
      content = content.slice(0, insertIndex) + serviceInit + content.slice(insertIndex);
    }
  }
  
  // Save the refactored file
  await writeFile(filePath, content);
  console.log('✅ Refactored generate route to use service layer');
}

// Run the refactoring
refactorGenerateRoute().catch(console.error);