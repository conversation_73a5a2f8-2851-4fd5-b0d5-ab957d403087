#!/usr/bin/env node
import { readdir, readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface RefactorIssue {
  type: 'direct_query' | 'business_logic' | 'data_transform' | 'missing_service';
  line: number;
  code: string;
  suggestion: string;
  severity: 'high' | 'medium' | 'low';
}

interface RefactorResult {
  file: string;
  issues: RefactorIssue[];
  refactored: boolean;
  manualReviewNeeded: boolean;
  complexityScore: number;
}

// Extended mapping for all tables and their service methods
const COMPREHENSIVE_TABLE_MAP = {
  'profiles': {
    service: 'UserService',
    import: '@/lib/services/user-service',
    methods: {
      'get': 'getUserProfile',
      'update': 'updateUserProfile',
      'subscription': 'getUserSubscription'
    }
  },
  'projects': {
    service: 'ContentGenerator',
    import: '@/lib/services/content-generator',
    methods: {
      'list': 'getUserProjects',
      'get': 'getProject',
      'create': 'createProject',
      'update': 'updateProject',
      'delete': 'deleteProject',
      'generate': 'generateProjectContent'
    }
  },
  'chapters': {
    service: 'ContentGenerator',
    import: '@/lib/services/content-generator',
    methods: {
      'list': 'getProjectChapters',
      'get': 'getChapter',
      'create': 'generateChapter',
      'update': 'updateChapter',
      'delete': 'deleteChapter',
      'regenerate': 'regenerateChapter'
    }
  },
  'characters': {
    service: 'ContentGenerator',
    import: '@/lib/services/content-generator',
    methods: {
      'list': 'getProjectCharacters',
      'get': 'getCharacter',
      'create': 'createCharacter',
      'update': 'updateCharacter',
      'delete': 'deleteCharacter',
      'develop': 'developCharacter'
    }
  },
  'story_bible': {
    service: 'ContextManager',
    import: '@/lib/services/context-manager',
    methods: {
      'get': 'getStoryBible',
      'update': 'updateStoryBible',
      'generate': 'generateStoryBible'
    }
  },
  'story_bibles': {
    service: 'ContextManager',
    import: '@/lib/services/context-manager',
    methods: {
      'get': 'getStoryBible',
      'update': 'updateStoryBible'
    }
  },
  'writing_sessions': {
    service: 'AnalyticsEngine',
    import: '@/lib/services/analytics-engine',
    methods: {
      'track': 'trackWritingSession',
      'list': 'getWritingSessions',
      'stats': 'getWritingStats'
    }
  },
  'usage_tracking': {
    service: 'AnalyticsEngine',
    import: '@/lib/services/analytics-engine',
    methods: {
      'track': 'trackUsage',
      'get': 'getUsageStats',
      'report': 'generateUsageReport'
    }
  },
  'collaboration_sessions': {
    service: 'CollaborationService',
    import: '@/lib/services/unified-collaboration-service',
    methods: {
      'create': 'createSession',
      'join': 'joinSession',
      'update': 'updateSession',
      'leave': 'leaveSession'
    }
  },
  'content_embeddings': {
    service: 'SemanticSearch',
    import: '@/lib/services/semantic-search',
    methods: {
      'search': 'search',
      'index': 'indexContent',
      'update': 'updateEmbeddings'
    }
  },
  'agent_logs': {
    service: 'AIOrchestrator',
    import: '@/lib/services/ai-orchestrator',
    methods: {
      'log': 'logAgentExecution',
      'get': 'getAgentLogs',
      'analyze': 'analyzeAgentPerformance'
    }
  }
};

// Business logic patterns that should be in services
const BUSINESS_LOGIC_PATTERNS = [
  {
    pattern: /word_count\s*=\s*content\.split\s*\(\s*['"]\\s+['"]\s*\)\.length/,
    service: 'ContentGenerator',
    method: 'calculateWordCount',
    description: 'Word count calculation'
  },
  {
    pattern: /reading_time\s*=.*word_count\s*\/\s*\d+/,
    service: 'ContentGenerator',
    method: 'calculateReadingTime',
    description: 'Reading time calculation'
  },
  {
    pattern: /\.map\s*\([^)]*\)\s*\.filter\s*\([^)]*\)\s*\.reduce/,
    service: 'appropriate service',
    method: 'processData',
    description: 'Complex data transformation'
  },
  {
    pattern: /for\s*\(.*await.*supabase/,
    service: 'appropriate service',
    method: 'batchOperation',
    description: 'Loop with database queries'
  }
];

async function findAPIRoutes(dir: string): Promise<string[]> {
  const files: string[] = [];
  
  async function walk(currentDir: string) {
    const entries = await readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        if (!['node_modules', '.next', 'dist', '.git'].includes(entry.name)) {
          await walk(fullPath);
        }
      } else if (entry.isFile() && entry.name === 'route.ts' && (fullPath.includes('\\api\\') || fullPath.includes('/api/'))) {
        files.push(fullPath);
      }
    }
  }
  
  await walk(dir);
  return files;
}

function analyzeFile(content: string, filePath: string): RefactorIssue[] {
  const issues: RefactorIssue[] = [];
  const lines = content.split('\n');
  
  // Check for service imports
  const hasServiceManager = content.includes('ServiceManager');
  const hasSpecificServices = Object.values(COMPREHENSIVE_TABLE_MAP).some(
    mapping => content.includes(mapping.service)
  );
  
  if (!hasServiceManager && !hasSpecificServices && content.includes('supabase')) {
    issues.push({
      type: 'missing_service',
      line: 1,
      code: 'No service imports found',
      suggestion: 'Import ServiceManager or specific services',
      severity: 'high'
    });
  }
  
  // Analyze each line
  lines.forEach((line, index) => {
    // Direct database queries
    const tableMatch = line.match(/\.from\(['"](\w+)['"]\)/);
    if (tableMatch) {
      const table = tableMatch[1];
      const mapping = COMPREHENSIVE_TABLE_MAP[table as keyof typeof COMPREHENSIVE_TABLE_MAP];
      
      if (mapping) {
        issues.push({
          type: 'direct_query',
          line: index + 1,
          code: line.trim(),
          suggestion: `Use ${mapping.service} service instead of direct query to '${table}'`,
          severity: 'high'
        });
      }
    }
    
    // Business logic patterns
    BUSINESS_LOGIC_PATTERNS.forEach(({ pattern, service, method, description }) => {
      if (pattern.test(line)) {
        issues.push({
          type: 'business_logic',
          line: index + 1,
          code: line.trim(),
          suggestion: `Move ${description} to ${service}.${method}()`,
          severity: 'medium'
        });
      }
    });
    
    // Complex data transformations
    if (line.includes('.map(') && lines[index + 1]?.includes('.filter(')) {
      issues.push({
        type: 'data_transform',
        line: index + 1,
        code: line.trim() + ' ' + lines[index + 1].trim(),
        suggestion: 'Move complex data transformation to service layer',
        severity: 'low'
      });
    }
  });
  
  return issues;
}

function calculateComplexity(issues: RefactorIssue[]): number {
  let score = 0;
  issues.forEach(issue => {
    switch (issue.severity) {
      case 'high': score += 3; break;
      case 'medium': score += 2; break;
      case 'low': score += 1; break;
    }
  });
  return score;
}

async function generateDetailedReport(results: RefactorResult[]): Promise<void> {
  const reportPath = join(__dirname, '..', 'comprehensive-service-layer-report.md');
  
  let report = '# Comprehensive Service Layer Refactoring Report\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  
  // Calculate totals
  const totalIssues = results.reduce((sum, r) => sum + r.issues.length, 0);
  const highPriorityFiles = results
    .filter(r => r.complexityScore > 10)
    .sort((a, b) => b.complexityScore - a.complexityScore);
  
  report += '## Executive Summary\n\n';
  report += `- **Total API Routes**: ${results.length}\n`;
  report += `- **Total Issues Found**: ${totalIssues}\n`;
  report += `- **High Priority Files**: ${highPriorityFiles.length}\n`;
  report += `- **Files Needing Manual Review**: ${results.filter(r => r.manualReviewNeeded).length}\n\n`;
  
  // Issue breakdown
  const issueTypes = {
    direct_query: 0,
    business_logic: 0,
    data_transform: 0,
    missing_service: 0
  };
  
  results.forEach(r => {
    r.issues.forEach(issue => {
      issueTypes[issue.type]++;
    });
  });
  
  report += '## Issue Breakdown\n\n';
  report += `- **Direct Database Queries**: ${issueTypes.direct_query}\n`;
  report += `- **Business Logic in Routes**: ${issueTypes.business_logic}\n`;
  report += `- **Complex Data Transformations**: ${issueTypes.data_transform}\n`;
  report += `- **Missing Service Imports**: ${issueTypes.missing_service}\n\n`;
  
  // High priority files
  if (highPriorityFiles.length > 0) {
    report += '## High Priority Files (Complexity Score > 10)\n\n';
    highPriorityFiles.slice(0, 20).forEach(result => {
      report += `### ${result.file.replace(/\\/g, '/')}\n`;
      report += `- **Complexity Score**: ${result.complexityScore}\n`;
      report += `- **Issues**: ${result.issues.length}\n`;
      report += `- **Top Issues**:\n`;
      
      result.issues.slice(0, 5).forEach(issue => {
        report += `  - Line ${issue.line}: ${issue.suggestion}\n`;
        report += `    - Code: \`${issue.code.substring(0, 60)}${issue.code.length > 60 ? '...' : ''}\`\n`;
      });
      
      if (result.issues.length > 5) {
        report += `  - ... and ${result.issues.length - 5} more issues\n`;
      }
      report += '\n';
    });
  }
  
  // Service layer mapping guide
  report += '## Service Layer Mapping Guide\n\n';
  report += '### Table to Service Mapping\n\n';
  report += '| Table | Service | Import Path |\n';
  report += '|-------|---------|-------------|\n';
  
  Object.entries(COMPREHENSIVE_TABLE_MAP).forEach(([table, mapping]) => {
    report += `| ${table} | ${mapping.service} | ${mapping.import} |\n`;
  });
  
  report += '\n### Refactoring Examples\n\n';
  report += '#### Before:\n```typescript\n';
  report += 'const { data } = await supabase\n';
  report += '  .from("projects")\n';
  report += '  .select("*")\n';
  report += '  .eq("user_id", userId)\n';
  report += '```\n\n';
  
  report += '#### After:\n```typescript\n';
  report += 'const serviceManager = ServiceManager.getInstance()\n';
  report += 'const contentGenerator = await serviceManager.getContentGenerator()\n';
  report += 'const projects = await contentGenerator.getUserProjects(userId)\n';
  report += '```\n\n';
  
  // Implementation plan
  report += '## Implementation Plan\n\n';
  report += '1. **Phase 1**: Refactor high-priority files (complexity > 10)\n';
  report += '2. **Phase 2**: Update remaining files with direct queries\n';
  report += '3. **Phase 3**: Move business logic to services\n';
  report += '4. **Phase 4**: Optimize data transformations\n';
  report += '5. **Phase 5**: Add comprehensive tests\n\n';
  
  // Next steps
  report += '## Next Steps\n\n';
  report += '1. Review high-priority files manually\n';
  report += '2. Create missing service methods if needed\n';
  report += '3. Run automated refactoring on simple cases\n';
  report += '4. Test thoroughly after each refactoring\n';
  report += '5. Update API documentation\n';
  
  await writeFile(reportPath, report);
  console.log(`\nDetailed report saved to: ${reportPath}`);
}

async function main() {
  console.log('🔍 Performing comprehensive service layer analysis...\n');
  
  const srcDir = join(__dirname, '..', 'src');
  const apiRoutes = await findAPIRoutes(srcDir);
  
  console.log(`Found ${apiRoutes.length} API route files\n`);
  
  const results: RefactorResult[] = [];
  
  // Analyze each file
  for (const route of apiRoutes) {
    try {
      const content = await readFile(route, 'utf-8');
      const issues = analyzeFile(content, route);
      
      if (issues.length > 0) {
        const complexity = calculateComplexity(issues);
        const result: RefactorResult = {
          file: route,
          issues,
          refactored: false,
          manualReviewNeeded: complexity > 15 || issues.some(i => i.type === 'business_logic'),
          complexityScore: complexity
        };
        
        results.push(result);
        console.log(`❌ ${route.replace(/\\/g, '/')}: ${issues.length} issues (complexity: ${complexity})`);
      } else {
        console.log(`✅ ${route.replace(/\\/g, '/')}: Clean`);
      }
    } catch (error) {
      console.error(`Error analyzing ${route}:`, error);
    }
  }
  
  // Generate comprehensive report
  await generateDetailedReport(results);
  
  console.log('\n✅ Comprehensive analysis complete!');
  console.log(`\nTotal issues found: ${results.reduce((sum, r) => sum + r.issues.length, 0)}`);
}

// Run the script
main().catch(console.error);