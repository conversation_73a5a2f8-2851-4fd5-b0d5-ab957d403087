import { NextRequest, NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { UserService } from '@/lib/services/user-service';
import { TaskService } from '@/lib/services/task-service';
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { logger } from '@/lib/services/logger';
import { z } from 'zod';

const dataExportRequestSchema = z.object({
  format: z.enum(['json', 'csv']).default('json'),
  categories: z.array(z.enum([
    'profile',
    'projects',
    'chapters', 
    'characters',
    'sessions',
    'goals',
    'analytics',
    'subscription'
  ])).optional(),
  includeDeleted: z.boolean().default(false)
});

export async function POST(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const body = await request.json();
    const validation = dataExportRequestSchema.safeParse(body);

    if (!validation.success) {
      return handleAPIError(new ValidationError('Invalid export request'));
    }

    const { format, categories, includeDeleted } = validation.data;

    // Initialize services
    const userService = new UserService();
    const taskService = new TaskService();
    await userService.initialize();
    await taskService.initialize();

    // Check if user already has a pending export request
    const existingTasksResponse = await taskService.getUserTasks(user.id, {
      taskType: 'data_export',
      status: 'pending',
      limit: 1
    });

    if (existingTasksResponse.success && existingTasksResponse.data.length > 0) {
      const existingTask = existingTasksResponse.data[0];
      return NextResponse.json({
        success: false,
        error: 'You already have a pending data export request',
        existingExport: {
          taskId: existingTask.id,
          requestedAt: existingTask.created_at,
          status: existingTask.status
        }
      }, { status: 400 });
    }

    // Record the export request for audit purposes
    await userService.recordConsent(user.id, 'data_export', true, {
      format,
      categories: categories || 'all',
      includeDeleted,
      requestedAt: new Date().toISOString(),
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    });

    // Create a background task to handle the export
    const taskResponse = await taskService.createTask(user.id, {
      type: 'data_export',
      payload: {
        userId: user.id,
        userEmail: user.email,
        format,
        categories,
        includeDeleted,
        requestedAt: new Date().toISOString()
      },
      priority: 'medium'
    });

    if (!taskResponse.success) {
      logger.error('Failed to create export task:', taskResponse.error);
      return NextResponse.json({
        success: false,
        error: 'Failed to schedule data export. Please try again.'
      }, { status: 500 });
    }

    // Log the export request
    logger.info('User data export requested', {
      userId: user.id,
      userEmail: user.email,
      format,
      categories,
      taskId: taskResponse.data.id
    });

    return NextResponse.json({
      success: true,
      message: 'Data export request submitted successfully',
      taskId: taskResponse.data.id,
      estimatedCompletion: '72 hours',
      details: {
        requestedAt: new Date().toISOString(),
        status: 'pending',
        format,
        categories: categories || 'all',
        taskId: taskResponse.data.id
      }
    });

  } catch (error) {
    logger.error('Error processing export request:', error);
    return handleAPIError(error);
  }
}

export async function GET(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const searchParams = request.nextUrl.searchParams;
    const taskId = searchParams.get('taskId');

    const taskService = new TaskService();
    await taskService.initialize();

    if (taskId) {
      // Get specific export task
      const taskResponse = await taskService.getTask(taskId);
      
      if (!taskResponse.success) {
        return NextResponse.json({
          success: false,
          error: 'Export not found'
        }, { status: 404 });
      }

      const task = taskResponse.data;
      
      // Verify the task belongs to the requesting user
      if (task.user_id !== user.id) {
        return NextResponse.json({
          success: false,
          error: 'Access denied'
        }, { status: 403 });
      }

      return NextResponse.json({
        success: true,
        export: {
          id: task.id,
          status: task.status,
          requestedAt: task.created_at,
          completedAt: task.completed_at,
          format: task.payload?.format,
          categories: task.payload?.categories,
          downloadUrl: task.status === 'completed' ? task.result?.downloadUrl : null,
          expiresAt: task.status === 'completed' ? new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() : null,
          errorMessage: task.error_message
        }
      });
    } else {
      // Get all export requests for the user
      const tasksResponse = await taskService.getUserTasks(user.id, {
        taskType: 'data_export',
        limit: 10
      });

      if (!tasksResponse.success) {
        logger.error('Failed to fetch export tasks:', tasksResponse.error);
        return NextResponse.json({
          success: false,
          error: 'Failed to fetch export history'
        }, { status: 500 });
      }

      const exports = tasksResponse.data.map(task => ({
        id: task.id,
        status: task.status,
        requestedAt: task.created_at,
        completedAt: task.completed_at,
        format: task.payload?.format,
        categories: task.payload?.categories,
        downloadUrl: task.status === 'completed' ? task.result?.downloadUrl : null,
        expiresAt: task.status === 'completed' ? new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() : null
      }));

      return NextResponse.json({
        success: true,
        exports,
        totalCount: exports.length
      });
    }

  } catch (error) {
    logger.error('Error fetching export data:', error);
    return handleAPIError(error);
  }
}

// Direct data export for immediate download (limited data)
export async function PUT(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const body = await request.json();
    const validation = dataExportRequestSchema.safeParse(body);

    if (!validation.success) {
      return handleAPIError(new ValidationError('Invalid export request'));
    }

    const { format, categories } = validation.data;

    // For immediate export, only allow limited data
    const allowedCategories = ['profile', 'goals'];
    const requestedCategories = categories || allowedCategories;
    
    if (requestedCategories.some(cat => !allowedCategories.includes(cat))) {
      return NextResponse.json({
        success: false,
        error: 'Immediate export only supports profile and goals data. For full export, use POST method.'
      }, { status: 400 });
    }

    const userService = new UserService();
    await userService.initialize();

    // Get user data directly
    const userData: Record<string, unknown> = {};

    if (requestedCategories.includes('profile')) {
      const profileResponse = await userService.getUserProfile(user.id);
      if (profileResponse.success) {
        userData.profile = profileResponse.data;
      }
    }

    // For a full implementation, you would add other data categories here

    // Record the direct export access
    await userService.recordConsent(user.id, 'data_access', true, {
      type: 'immediate_export',
      format,
      categories: requestedCategories,
      accessedAt: new Date().toISOString(),
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown'
    });

    const filename = `bookscribe_data_${user.id}_${new Date().toISOString().split('T')[0]}.${format}`;
    
    if (format === 'csv') {
      // Convert to CSV format
      const csv = convertToCSV(userData);
      return new NextResponse(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      });
    } else {
      // Return as JSON
      return new NextResponse(JSON.stringify(userData, null, 2), {
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      });
    }

  } catch (error) {
    logger.error('Error processing immediate export:', error);
    return handleAPIError(error);
  }
}

function convertToCSV(data: Record<string, unknown>): string {
  // Simple CSV conversion - in a real implementation, this would be more sophisticated
  const rows: string[] = [];
  
  Object.entries(data).forEach(([section, sectionData]) => {
    if (Array.isArray(sectionData)) {
      sectionData.forEach((item, index) => {
        if (typeof item === 'object' && item !== null) {
          const row = Object.entries(item as Record<string, unknown>)
            .map(([key, value]) => `"${String(value).replace(/"/g, '""')}"`)
            .join(',');
          rows.push(`${section}_${index},${row}`);
        }
      });
    } else if (typeof sectionData === 'object' && sectionData !== null) {
      const row = Object.entries(sectionData as Record<string, unknown>)
        .map(([key, value]) => `"${String(value).replace(/"/g, '""')}"`)
        .join(',');
      rows.push(`${section},${row}`);
    }
  });

  return rows.join('\n');
}