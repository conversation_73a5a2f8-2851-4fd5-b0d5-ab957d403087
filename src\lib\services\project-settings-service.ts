import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { ServiceBase, ServiceResponse } from './base-service';
import { Database } from '@/lib/db/database.types';

type ProjectSettings = Database['public']['Tables']['project_settings']['Row'];
type ProjectSettingsInsert = Database['public']['Tables']['project_settings']['Insert'];
type ProjectSettingsUpdate = Database['public']['Tables']['project_settings']['Update'];

export class ProjectSettingsService extends ServiceBase {
  constructor() {
    super({
      name: 'project-settings-service',
      version: '1.0.0',
      endpoints: ['/api/project-settings'],
      dependencies: [],
      healthCheck: '/api/services/project-settings/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, { status: 'healthy' });
  }

  /**
   * Get project settings
   */
  async getProjectSettings(projectId: string): Promise<ServiceResponse<ProjectSettings | null>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('project_settings')
        .select('*')
        .eq('project_id', projectId)
        .single();
      
      if (error) {
        // It's okay if no settings exist
        if (error.code === 'PGRST116') {
          return null;
        }
        logger.error('[ProjectSettingsService] Error fetching settings:', error);
        throw error;
      }
      
      return data;
    });
  }

  /**
   * Create or update project settings
   */
  async upsertProjectSettings(projectId: string, settings: any): Promise<ServiceResponse<ProjectSettings>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('project_settings')
        .upsert({
          project_id: projectId,
          settings: settings,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        logger.error('[ProjectSettingsService] Error upserting settings:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Failed to save project settings');
      }
      
      return data;
    });
  }

  /**
   * Update specific settings fields
   */
  async updateProjectSettings(
    projectId: string, 
    updates: Partial<any>
  ): Promise<ServiceResponse<ProjectSettings>> {
    return this.withErrorHandling(async () => {
      // First get existing settings
      const existingResponse = await this.getProjectSettings(projectId);
      const existingSettings = existingResponse.success && existingResponse.data 
        ? existingResponse.data.settings 
        : {};
      
      // Merge updates with existing settings
      const mergedSettings = {
        ...existingSettings,
        ...updates
      };
      
      // Save the merged settings
      return await this.upsertProjectSettings(projectId, mergedSettings);
    });
  }

  /**
   * Delete project settings
   */
  async deleteProjectSettings(projectId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from('project_settings')
        .delete()
        .eq('project_id', projectId);
      
      if (error) {
        logger.error('[ProjectSettingsService] Error deleting settings:', error);
        throw error;
      }
      
      return true;
    });
  }

  /**
   * Get settings for multiple projects
   */
  async getProjectsSettings(projectIds: string[]): Promise<ServiceResponse<ProjectSettings[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('project_settings')
        .select('*')
        .in('project_id', projectIds);
      
      if (error) {
        logger.error('[ProjectSettingsService] Error fetching multiple settings:', error);
        throw error;
      }
      
      return data || [];
    });
  }
}