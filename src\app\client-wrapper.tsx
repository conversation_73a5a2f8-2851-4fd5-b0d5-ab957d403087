'use client'

import { ReactNode, Suspense, useEffect, useState } from 'react'
import { Toaster } from '@/components/ui/toaster'
import { UnifiedErrorBoundary } from '@/components/error/unified-error-system'
import { SettingsProvider } from '@/components/settings/settings-provider'
import { AuthProvider } from '@/contexts/auth-context'
import { PendingInvitationHandler } from '@/components/auth/pending-invitation-handler'

interface ClientWrapperProps {
  children: ReactNode
  enableAuth?: boolean
  enableSettings?: boolean
  enableKeyboard?: boolean
}

export function ClientWrapper({
  children,
  enableAuth = false,
  enableSettings: _enableSettings = false,
  enableKeyboard: _enableKeyboard = false
}: ClientWrapperProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="min-h-screen">
        {children}
      </div>
    )
  }

  const content = (
    <SettingsProvider>
      {children}
      <Toaster />
    </SettingsProvider>
  )

  return (
    <UnifiedErrorBoundary
      fallback={(error, reset) => (
        <div className="min-h-screen bg-background text-foreground">
          {children}
        </div>
      )}
    >
      <Suspense fallback={
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="animate-pulse">Initializing application...</div>
        </div>
      }>
        {enableAuth ? (
          <AuthProvider>
            <PendingInvitationHandler />
            {content}
          </AuthProvider>
        ) : (
          content
        )}
      </Suspense>
    </UnifiedErrorBoundary>
  )
}

// Specialized wrappers for different use cases
export function MarketingClientWrapper({ children }: { children: ReactNode }) {
  return (
    <ClientWrapper enableAuth={true}>
      {children}
    </ClientWrapper>
  )
}

export function DashboardClientWrapper({ children }: { children: ReactNode }) {
  return (
    <ClientWrapper
      enableAuth={true}
      enableSettings={true}
      enableKeyboard={true}
    >
      {children}
    </ClientWrapper>
  )
}

export function AuthClientWrapper({ children }: { children: ReactNode }) {
  return (
    <ClientWrapper
      enableAuth={true}
    >
      {children}
    </ClientWrapper>
  )
}
