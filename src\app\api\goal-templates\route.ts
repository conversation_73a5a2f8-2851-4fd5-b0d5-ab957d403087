import { NextRequest, NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { WritingAnalyticsService } from '@/lib/services/writing-analytics-service';
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { z } from 'zod';

const createGoalTemplateSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  goal_type: z.enum(['word_count', 'streak', 'quality', 'milestone', 'chapter', 'custom']),
  target_value: z.number().min(1),
  unit: z.string().min(1),
  difficulty: z.enum(['easy', 'medium', 'hard']),
  category: z.string().optional(),
  duration_days: z.number().optional(),
  is_public: z.boolean().optional().default(false)
});

export async function GET(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get('category');
    const difficulty = searchParams.get('difficulty');
    const goalType = searchParams.get('goal_type');

    const analyticsService = new WritingAnalyticsService();
    await analyticsService.initialize();

    // Get goal templates (this would typically come from a goal_templates table)
    // For now, return predefined templates
    const templates = getDefaultGoalTemplates();
    
    // Filter based on query parameters
    let filteredTemplates = templates;
    
    if (category) {
      filteredTemplates = filteredTemplates.filter(t => t.category === category);
    }
    
    if (difficulty) {
      filteredTemplates = filteredTemplates.filter(t => t.difficulty === difficulty);
    }
    
    if (goalType) {
      filteredTemplates = filteredTemplates.filter(t => t.goal_type === goalType);
    }

    return NextResponse.json({
      success: true,
      templates: filteredTemplates,
      totalCount: filteredTemplates.length
    });

  } catch (error) {
    return handleAPIError(error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const body = await request.json();
    const validation = createGoalTemplateSchema.safeParse(body);
    
    if (!validation.success) {
      return handleAPIError(new ValidationError('Invalid template data'));
    }

    const analyticsService = new WritingAnalyticsService();
    await analyticsService.initialize();

    // Create goal from template
    const goalData = {
      goal_type: validation.data.goal_type,
      title: validation.data.name,
      description: validation.data.description || '',
      target_value: validation.data.target_value,
      unit: validation.data.unit,
      difficulty: validation.data.difficulty,
      is_recommended: false,
      metadata: {
        createdFromTemplate: true,
        templateCategory: validation.data.category,
        durationDays: validation.data.duration_days
      }
    };

    // This would typically insert into writing_goals table
    // Using the existing goals API pattern
    return NextResponse.json({
      success: true,
      message: 'Goal template created successfully',
      template: {
        id: `template_${Date.now()}`,
        ...validation.data,
        created_by: user.id,
        created_at: new Date().toISOString()
      }
    });

  } catch (error) {
    return handleAPIError(error);
  }
}

// Default goal templates
function getDefaultGoalTemplates() {
  return [
    {
      id: 'daily_words_500',
      name: 'Daily 500 Words',
      description: 'Write 500 words every day',
      goal_type: 'word_count',
      target_value: 500,
      unit: 'words',
      difficulty: 'easy',
      category: 'productivity',
      duration_days: 30,
      is_public: true
    },
    {
      id: 'daily_words_1000',
      name: 'Daily 1000 Words',
      description: 'Write 1000 words every day',
      goal_type: 'word_count',
      target_value: 1000,
      unit: 'words',
      difficulty: 'medium',
      category: 'productivity',
      duration_days: 30,
      is_public: true
    },
    {
      id: 'weekly_chapter',
      name: 'Weekly Chapter',
      description: 'Complete one chapter every week',
      goal_type: 'chapter',
      target_value: 1,
      unit: 'chapters',
      difficulty: 'medium',
      category: 'productivity',
      duration_days: 7,
      is_public: true
    },
    {
      id: 'seven_day_streak',
      name: '7-Day Writing Streak',
      description: 'Write for 7 consecutive days',
      goal_type: 'streak',
      target_value: 7,
      unit: 'days',
      difficulty: 'easy',
      category: 'consistency',
      duration_days: 7,
      is_public: true
    },
    {
      id: 'thirty_day_streak',
      name: '30-Day Writing Streak',
      description: 'Write for 30 consecutive days',
      goal_type: 'streak',
      target_value: 30,
      unit: 'days',
      difficulty: 'hard',
      category: 'consistency',
      duration_days: 30,
      is_public: true
    },
    {
      id: 'nanowrimo',
      name: 'NaNoWriMo Challenge',
      description: 'Write 50,000 words in 30 days',
      goal_type: 'word_count',
      target_value: 50000,
      unit: 'words',
      difficulty: 'hard',
      category: 'challenge',
      duration_days: 30,
      is_public: true
    },
    {
      id: 'quality_improvement',
      name: 'Quality Improvement',
      description: 'Improve overall writing quality score to 80+',
      goal_type: 'quality',
      target_value: 80,
      unit: 'score',
      difficulty: 'medium',
      category: 'improvement',
      duration_days: 60,
      is_public: true
    },
    {
      id: 'first_draft_complete',
      name: 'Complete First Draft',
      description: 'Finish the first draft of your novel',
      goal_type: 'milestone',
      target_value: 1,
      unit: 'draft',
      difficulty: 'hard',
      category: 'milestone',
      duration_days: 90,
      is_public: true
    }
  ];
}