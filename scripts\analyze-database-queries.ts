#!/usr/bin/env node
import { readdir, readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface QueryPattern {
  table: string;
  columns: string[];
  filters: string[];
  joins: string[];
  orderBy: string[];
  file: string;
  line: number;
  queryType: 'select' | 'insert' | 'update' | 'delete';
}

interface IndexRecommendation {
  table: string;
  columns: string[];
  type: 'btree' | 'gin' | 'gist';
  reason: string;
  impact: 'high' | 'medium' | 'low';
  queryCount: number;
}

// Common query patterns that benefit from indexes
const COMMON_PATTERNS = {
  // Foreign key lookups
  'user_id': { impact: 'high', reason: 'Frequent user-based filtering' },
  'project_id': { impact: 'high', reason: 'Frequent project-based filtering' },
  'chapter_id': { impact: 'medium', reason: 'Chapter-based queries' },
  'series_id': { impact: 'medium', reason: 'Series aggregation queries' },
  
  // Status and type filters
  'status': { impact: 'medium', reason: 'Status-based filtering' },
  'type': { impact: 'medium', reason: 'Type-based filtering' },
  
  // Temporal queries
  'created_at': { impact: 'high', reason: 'Time-based sorting and filtering' },
  'updated_at': { impact: 'medium', reason: 'Recent changes queries' },
  
  // Text search
  'title': { impact: 'high', reason: 'Title-based searches' },
  'content': { impact: 'high', reason: 'Full-text search', type: 'gin' },
};

async function findSourceFiles(dir: string): Promise<string[]> {
  const files: string[] = [];
  
  async function walk(currentDir: string) {
    const entries = await readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        if (!['node_modules', '.next', 'dist', '.git'].includes(entry.name)) {
          await walk(fullPath);
        }
      } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
        files.push(fullPath);
      }
    }
  }
  
  await walk(dir);
  return files;
}

function extractQueryPatterns(content: string, filePath: string): QueryPattern[] {
  const patterns: QueryPattern[] = [];
  const lines = content.split('\n');
  
  // Regex patterns for Supabase queries
  const queryRegexes = [
    // Select queries
    /\.from\(['"](\w+)['"]\)\s*\.select\(([^)]*)\)/g,
    // Filter patterns
    /\.eq\(['"](\w+)['"],/g,
    /\.neq\(['"](\w+)['"],/g,
    /\.gt\(['"](\w+)['"],/g,
    /\.gte\(['"](\w+)['"],/g,
    /\.lt\(['"](\w+)['"],/g,
    /\.lte\(['"](\w+)['"],/g,
    /\.like\(['"](\w+)['"],/g,
    /\.ilike\(['"](\w+)['"],/g,
    /\.in\(['"](\w+)['"],/g,
    // Order patterns
    /\.order\(['"](\w+)['"],/g,
    // Join patterns
    /\.select\([^)]*\w+\([^)]*\)/g,
  ];
  
  lines.forEach((line, index) => {
    // Extract table and select
    const selectMatch = line.match(/\.from\(['"](\w+)['"]\)\s*\.select/);
    if (selectMatch) {
      const table = selectMatch[1];
      
      // Extract filters
      const filters: string[] = [];
      const filterMatches = line.matchAll(/\.(eq|neq|gt|gte|lt|lte|like|ilike|in)\(['"](\w+)['"]/g);
      for (const match of filterMatches) {
        filters.push(match[2]);
      }
      
      // Extract order by
      const orderBy: string[] = [];
      const orderMatches = line.matchAll(/\.order\(['"](\w+)['"]/g);
      for (const match of orderMatches) {
        orderBy.push(match[1]);
      }
      
      patterns.push({
        table,
        columns: [],
        filters,
        joins: [],
        orderBy,
        file: filePath,
        line: index + 1,
        queryType: 'select'
      });
    }
  });
  
  return patterns;
}

function analyzePatterns(patterns: QueryPattern[]): Map<string, IndexRecommendation> {
  const recommendations = new Map<string, IndexRecommendation>();
  const queryStats = new Map<string, number>();
  
  // Count query patterns
  patterns.forEach(pattern => {
    // Count filters
    pattern.filters.forEach(filter => {
      const key = `${pattern.table}.${filter}`;
      queryStats.set(key, (queryStats.get(key) || 0) + 1);
    });
    
    // Count order by
    pattern.orderBy.forEach(col => {
      const key = `${pattern.table}.${col}`;
      queryStats.set(key, (queryStats.get(key) || 0) + 1);
    });
  });
  
  // Generate recommendations based on frequency
  for (const [key, count] of queryStats.entries()) {
    const [table, column] = key.split('.');
    
    if (count >= 3) { // At least 3 queries use this pattern
      const commonPattern = COMMON_PATTERNS[column as keyof typeof COMMON_PATTERNS];
      const impact = count >= 10 ? 'high' : count >= 5 ? 'medium' : 'low';
      
      recommendations.set(key, {
        table,
        columns: [column],
        type: commonPattern?.type === 'gin' ? 'gin' : 'btree',
        reason: commonPattern?.reason || `Frequent filtering on ${column} (${count} queries)`,
        impact: impact as 'high' | 'medium' | 'low',
        queryCount: count
      });
    }
  }
  
  // Add composite indexes for common combinations
  const compositePatterns = new Map<string, number>();
  patterns.forEach(pattern => {
    if (pattern.filters.length > 1) {
      const key = `${pattern.table}.${pattern.filters.sort().join('+')}`;
      compositePatterns.set(key, (compositePatterns.get(key) || 0) + 1);
    }
  });
  
  for (const [key, count] of compositePatterns.entries()) {
    if (count >= 2) {
      const [table, columns] = key.split('.');
      const colArray = columns.split('+');
      
      recommendations.set(key, {
        table,
        columns: colArray,
        type: 'btree',
        reason: `Composite index for frequent multi-column queries (${count} queries)`,
        impact: count >= 5 ? 'high' : 'medium',
        queryCount: count
      });
    }
  }
  
  return recommendations;
}

async function generateOptimizationScript(recommendations: Map<string, IndexRecommendation>): Promise<string> {
  let script = `-- Database Index Optimization Script
-- Generated: ${new Date().toISOString()}
-- Total recommendations: ${recommendations.size}

-- IMPORTANT: Review each index before applying
-- Some indexes may already exist or may not be needed in your specific use case

`;

  // Group by table
  const byTable = new Map<string, IndexRecommendation[]>();
  for (const rec of recommendations.values()) {
    if (!byTable.has(rec.table)) {
      byTable.set(rec.table, []);
    }
    byTable.get(rec.table)!.push(rec);
  }
  
  // Sort tables by importance
  const sortedTables = Array.from(byTable.entries()).sort((a, b) => {
    const aHighCount = a[1].filter(r => r.impact === 'high').length;
    const bHighCount = b[1].filter(r => r.impact === 'high').length;
    return bHighCount - aHighCount;
  });
  
  for (const [table, recs] of sortedTables) {
    script += `\n-- ==========================================\n`;
    script += `-- Table: ${table}\n`;
    script += `-- ==========================================\n\n`;
    
    // Sort recommendations by impact
    const sorted = recs.sort((a, b) => {
      const impactOrder = { high: 3, medium: 2, low: 1 };
      return impactOrder[b.impact] - impactOrder[a.impact];
    });
    
    for (const rec of sorted) {
      const indexName = `idx_${table}_${rec.columns.join('_')}`;
      const indexType = rec.type === 'gin' ? 'USING gin' : '';
      const columns = rec.columns.join(', ');
      
      script += `-- ${rec.reason}\n`;
      script += `-- Impact: ${rec.impact.toUpperCase()} (used in ${rec.queryCount} queries)\n`;
      script += `CREATE INDEX IF NOT EXISTS ${indexName}\n`;
      script += `  ON ${table} ${indexType} (${columns});\n\n`;
    }
  }
  
  // Add additional optimization recommendations
  script += `\n-- ==========================================\n`;
  script += `-- Additional Optimizations\n`;
  script += `-- ==========================================\n\n`;
  
  script += `-- 1. Partial indexes for status fields\n`;
  script += `CREATE INDEX IF NOT EXISTS idx_projects_active\n`;
  script += `  ON projects (user_id, updated_at)\n`;
  script += `  WHERE status = 'active';\n\n`;
  
  script += `-- 2. Text search indexes\n`;
  script += `CREATE INDEX IF NOT EXISTS idx_chapters_content_search\n`;
  script += `  ON chapters USING gin(to_tsvector('english', content));\n\n`;
  
  script += `CREATE INDEX IF NOT EXISTS idx_projects_title_search\n`;
  script += `  ON projects USING gin(to_tsvector('english', title));\n\n`;
  
  script += `-- 3. JSONB indexes for structured data\n`;
  script += `CREATE INDEX IF NOT EXISTS idx_projects_settings\n`;
  script += `  ON projects USING gin(project_settings);\n\n`;
  
  script += `CREATE INDEX IF NOT EXISTS idx_characters_personality\n`;
  script += `  ON characters USING gin(personality_traits);\n\n`;
  
  script += `-- 4. Foreign key indexes (if not automatically created)\n`;
  script += `CREATE INDEX IF NOT EXISTS idx_chapters_project_id\n`;
  script += `  ON chapters (project_id);\n\n`;
  
  script += `CREATE INDEX IF NOT EXISTS idx_characters_project_id\n`;
  script += `  ON characters (project_id);\n\n`;
  
  script += `-- 5. Composite indexes for common queries\n`;
  script += `CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_period\n`;
  script += `  ON usage_tracking (user_id, period_start, period_end);\n\n`;
  
  script += `CREATE INDEX IF NOT EXISTS idx_writing_sessions_user_project\n`;
  script += `  ON writing_sessions (user_id, project_id, started_at DESC);\n\n`;
  
  script += `-- ==========================================\n`;
  script += `-- Query Optimization Tips\n`;
  script += `-- ==========================================\n`;
  script += `-- 1. Use EXPLAIN ANALYZE to verify index usage\n`;
  script += `-- 2. Monitor pg_stat_user_indexes for unused indexes\n`;
  script += `-- 3. Consider partitioning large tables (e.g., agent_logs)\n`;
  script += `-- 4. Use connection pooling for better performance\n`;
  script += `-- 5. Enable pg_stat_statements for query performance monitoring\n`;
  
  return script;
}

async function generateReport(patterns: QueryPattern[], recommendations: Map<string, IndexRecommendation>): Promise<void> {
  const reportPath = join(__dirname, '..', 'database-optimization-report.md');
  
  let report = '# Database Optimization Report\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  report += `Total query patterns analyzed: ${patterns.length}\n`;
  report += `Total index recommendations: ${recommendations.size}\n\n`;
  
  // Summary by impact
  const highImpact = Array.from(recommendations.values()).filter(r => r.impact === 'high').length;
  const mediumImpact = Array.from(recommendations.values()).filter(r => r.impact === 'medium').length;
  const lowImpact = Array.from(recommendations.values()).filter(r => r.impact === 'low').length;
  
  report += '## Impact Summary\n\n';
  report += `- **High Impact**: ${highImpact} indexes (implement immediately)\n`;
  report += `- **Medium Impact**: ${mediumImpact} indexes (implement soon)\n`;
  report += `- **Low Impact**: ${lowImpact} indexes (consider for future)\n\n`;
  
  // Most queried tables
  const tableQueries = new Map<string, number>();
  patterns.forEach(p => {
    tableQueries.set(p.table, (tableQueries.get(p.table) || 0) + 1);
  });
  
  report += '## Most Queried Tables\n\n';
  const sortedTables = Array.from(tableQueries.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10);
  
  sortedTables.forEach(([table, count]) => {
    report += `- **${table}**: ${count} queries\n`;
  });
  
  report += '\n## High Impact Recommendations\n\n';
  Array.from(recommendations.values())
    .filter(r => r.impact === 'high')
    .forEach(rec => {
      report += `### ${rec.table}.${rec.columns.join(', ')}\n`;
      report += `- **Type**: ${rec.type}\n`;
      report += `- **Reason**: ${rec.reason}\n`;
      report += `- **Query Count**: ${rec.queryCount}\n\n`;
    });
  
  report += '## Implementation Steps\n\n';
  report += '1. Review the generated SQL script: `database-indexes.sql`\n';
  report += '2. Test indexes in development first\n';
  report += '3. Apply during low-traffic period\n';
  report += '4. Monitor query performance after implementation\n';
  report += '5. Use `EXPLAIN ANALYZE` to verify index usage\n\n';
  
  report += '## Additional Optimizations\n\n';
  report += '- Consider implementing database connection pooling\n';
  report += '- Enable query performance monitoring with pg_stat_statements\n';
  report += '- Implement proper caching strategies for frequently accessed data\n';
  report += '- Use database views for complex, frequently-used queries\n';
  report += '- Consider read replicas for heavy read workloads\n';
  
  await writeFile(reportPath, report);
  console.log(`Report saved to: ${reportPath}`);
}

async function main() {
  console.log('🔍 Analyzing database query patterns...\n');
  
  const srcDir = join(__dirname, '..', 'src');
  const files = await findSourceFiles(srcDir);
  
  console.log(`Found ${files.length} source files\n`);
  
  const allPatterns: QueryPattern[] = [];
  
  // Extract query patterns
  for (const file of files) {
    const content = await readFile(file, 'utf-8');
    if (content.includes('.from(') && content.includes('supabase')) {
      const patterns = extractQueryPatterns(content, file);
      allPatterns.push(...patterns);
    }
  }
  
  console.log(`Extracted ${allPatterns.length} query patterns\n`);
  
  // Analyze patterns and generate recommendations
  const recommendations = analyzePatterns(allPatterns);
  
  console.log(`Generated ${recommendations.size} index recommendations\n`);
  
  // Generate SQL script
  const sqlScript = await generateOptimizationScript(recommendations);
  const sqlPath = join(__dirname, '..', 'database-indexes.sql');
  await writeFile(sqlPath, sqlScript);
  console.log(`SQL script saved to: ${sqlPath}\n`);
  
  // Generate report
  await generateReport(allPatterns, recommendations);
  
  console.log('\n✅ Database optimization analysis complete!');
}

// Run the script
main().catch(console.error);