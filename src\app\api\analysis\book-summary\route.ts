import { NextRequest } from 'next/server'
import { handleAPIError, ValidationError, NotFoundError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { vercelAIClient } from '@/lib/ai/vercel-ai-client'
import { AI_MODELS } from '@/lib/config/ai-settings'
import { withRetry, ErrorContext } from '@/lib/services/error-handler'
import { withCircuitBreaker, CIRCUIT_BREAKER_PRESETS } from '@/lib/services/circuit-breaker'
import { logger } from '@/lib/services/logger'
import { TIME_MS } from '@/lib/constants'
import { applyRateLimit } from '@/lib/rate-limiter-unified'

import { authenticateUser, handleRouteError } from '@/lib/auth'

interface SummaryRequest {
  projectId: string
  summaryType: string
  targetAudience: string
  tone: string
  regenerate?: boolean
}

interface Character {
  name: string
  role: string
  description: string
}

interface ChapterSummary {
  title: string
  summary: string
}

const summaryPrompts = {
  'elevator-pitch': `Create a compelling 30-second elevator pitch (approximately 50 words) that captures the essence of this book. Focus on the hook and what makes it unique.`,
  
  'back-cover': `Write an engaging back cover blurb (approximately 150 words) that will entice readers to buy the book. Include a hook, main conflict, and leave them wanting more without spoilers.`,
  
  'synopsis': `Write a detailed synopsis (approximately 500 words) suitable for literary agents and publishers. Include all major plot points, character arcs, and the ending.`,
  
  'query-letter': `Write the book description portion of a query letter (approximately 300 words) that would appeal to literary agents. Focus on conflict, stakes, and what makes this book marketable.`,
  
  'amazon-description': `Write an SEO-optimized book description (approximately 200 words) for online retailers like Amazon. Use engaging language, break up text for readability, and highlight key selling points.`
}

// In-memory cache for generated summaries with LRU eviction and TTL
// Keyed by `${projectId}:${summaryType}`
interface CacheEntry {
  summary: string
  wordCount: number
  timestamp: number
}

const CACHE_MAX_SIZE = 100 // Maximum number of entries
const CACHE_TTL_MS = 60 * 60 * 1000 // 1 hour TTL

class LRUCache {
  private cache = new Map<string, CacheEntry>()
  private maxSize: number
  private ttl: number

  constructor(maxSize: number, ttl: number) {
    this.maxSize = maxSize
    this.ttl = ttl
  }

  get(key: string): { summary: string; wordCount: number } | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    // Check if entry has expired
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key)
      return null
    }

    // Move to end (most recently used)
    this.cache.delete(key)
    this.cache.set(key, entry)
    
    return { summary: entry.summary, wordCount: entry.wordCount }
  }

  set(key: string, value: { summary: string; wordCount: number }): void {
    // Delete if exists to update position
    this.cache.delete(key)

    // Add to end
    this.cache.set(key, {
      ...value,
      timestamp: Date.now()
    })

    // Remove oldest entries if over limit
    if (this.cache.size > this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
  }

  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false
    
    // Check expiration
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key)
      return false
    }
    
    return true
  }

  clear(): void {
    this.cache.clear()
  }
}

const summaryCache = new LRUCache(CACHE_MAX_SIZE, CACHE_TTL_MS)

export function resetSummaryCache() {
  summaryCache.clear()
}

export async function POST(request: NextRequest) {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(request, { type: 'ai-analysis', cost: 3 })
    if (rateLimitResponse) {
      return rateLimitResponse
    }

  try {
    // Authentication
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const body: SummaryRequest = await request.json()
    const { projectId, summaryType, targetAudience, tone, regenerate } = body

    if (!projectId || !summaryType) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    // Check cache unless regeneration is forced
    const cacheKey = `${projectId}:${summaryType}`
    if (!regenerate) {
      const cached = summaryCache.get(cacheKey)
      if (cached) {
        return Response.json({
          success: true,
          summary: cached.summary,
          wordCount: cached.wordCount,
          fromCache: true
        })
      }
    }

    // AI client is configured internally

    const supabase = await createTypedServerClient()

    // Get project and chapters
    const { data: project } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single()

    if (!project) {
      return handleAPIError(new NotFoundError('Resource'))
    }

    // Get all chapters
    const { data: chapters } = await supabase
      .from('chapters')
      .select('chapter_number, title, content, summary')
      .eq('project_id', projectId)
      .order('chapter_number')

    if (!chapters || chapters.length === 0) {
      return handleAPIError(new ValidationError('Invalid request'))
    }

    // Get story bible
    const { data: storyBible } = await supabase
      .from('story_bible')
      .select('*')
      .eq('project_id', projectId)
      .single()

    // Prepare context for AI
    const bookContext = {
      title: project.title,
      genre: project.genre,
      subgenre: project.subgenre,
      description: project.description,
      targetAudience: project.target_audience || targetAudience,
      themes: storyBible?.themes || [],
      totalWordCount: project.word_count,
      chapterCount: chapters.length,
      chapterSummaries: chapters.map((ch: {chapter_number: number; title: string; summary?: string}) => ({
        number: ch.chapter_number,
        title: ch.title,
        summary: ch.summary || `Chapter ${ch.chapter_number}: ${ch.title}`
      }))
    }

    // Get the main characters
    const mainCharacters: Character[] = storyBible?.characters?.slice(0, 3).map((char: Character) => ({
      name: char.name,
      role: char.role,
      description: char.description
    })) || []

    // Build the prompt
    const systemPrompt = `You are a professional book marketing expert and literary agent. Generate compelling book summaries that sell books.`
    
    const userPrompt = `
Generate a ${summaryType} for this book:

Title: ${bookContext.title}
Genre: ${bookContext.genre} - ${bookContext.subgenre}
Target Audience: ${targetAudience}
Tone: ${tone}
Word Count: ${bookContext.totalWordCount?.toLocaleString()} words
Chapters: ${bookContext.chapterCount}

Book Description: ${bookContext.description}

Main Characters:
${mainCharacters.map((char: Character) => `- ${char.name} (${char.role}): ${char.description}`).join('\n')}

Themes: ${bookContext.themes.join(', ')}

Chapter Overview:
${bookContext.chapterSummaries.slice(0, 5).map((ch: ChapterSummary) => `- ${ch.title}: ${ch.summary}`).join('\n')}
${bookContext.chapterSummaries.length > 5 ? `...and ${bookContext.chapterSummaries.length - 5} more chapters` : ''}

Instructions: ${summaryPrompts[summaryType as keyof typeof summaryPrompts]}

Make the summary ${tone} in tone and appealing to ${targetAudience}.`

    // Generate summary
    const errorContext: ErrorContext = {
      operation: 'BookSummaryGeneration',
      projectId,
      metadata: {
        summaryType,
        model: AI_MODELS.PRIMARY
      }
    }

    const temperature = tone === 'dramatic' ? 0.9 : tone === 'casual' ? 0.8 : 0.7
    
    const summary = await vercelAIClient.generateTextWithFallback(
      userPrompt,
      {
        model: AI_MODELS.PRIMARY,
        temperature,
        maxTokens: TIME_MS.SECOND,
        systemPrompt
      },
      errorContext
    )

    if (!summary) {
      throw new Error('Failed to generate summary')
    }

    // Save to database - try book_summaries table first, fall back to story_bible
    try {
      const summaryRecord = {
        project_id: projectId,
        type: summaryType,
        content: summary,
        metadata: {
          targetAudience,
          tone,
          generatedAt: new Date().toISOString()
        }
      }

      if (regenerate) {
        const { error } = await supabase
          .from('book_summaries')
          .update(summaryRecord)
          .eq('project_id', projectId)
          .eq('type', summaryType)
        
        if (error) throw error
      } else {
        const { error } = await supabase
          .from('book_summaries')
          .insert(summaryRecord)
        
        if (error) throw error
      }
    } catch (dbError) {
      // If book_summaries table doesn't exist, update story_bible instead
      logger.warn('book_summaries table not found, updating story_bible instead:', dbError)
      
      const { data: currentBible } = await supabase
        .from('story_bible')
        .select('marketing_materials')
        .eq('project_id', projectId)
        .single()
      
      const marketingMaterials = currentBible?.marketing_materials || {}
      marketingMaterials[summaryType] = {
        content: summary,
        targetAudience,
        tone,
        generatedAt: new Date().toISOString()
      }
      
      await supabase
        .from('story_bible')
        .update({ marketing_materials: marketingMaterials })
        .eq('project_id', projectId)
    }

    const wordCount = summary.split(/\s+/).filter(w => w.length > 0).length

    // Store in cache
    summaryCache.set(cacheKey, { summary, wordCount })

    return Response.json({
      success: true,
      summary,
      wordCount,
      fromCache: false
    })

  } catch (error) {
    return handleRouteError(error, 'Book Summary Generation')
  }
}