import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { BaseService } from './base-service';
import { ServiceResponse } from './types';
import { startOfDay, startOfWeek, subDays, differenceInDays } from 'date-fns';
import { DB_TABLES } from '@/lib/config/database-tables';

interface WritingSession {
  id: string;
  user_id: string;
  project_id: string;
  chapter_id?: string | null;
  words_written: number;
  duration_minutes: number;
  created_at: string;
  started_at?: string;
}

interface WritingGoal {
  id: string;
  user_id: string;
  project_id?: string | null;
  daily_word_target?: number | null;
  weekly_word_target?: number | null;
  monthly_word_target?: number | null;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

interface WritingStats {
  dailyWords: number;
  weeklyWords: number;
  streak: number;
  avgWordsPerDay: number;
  totalWords?: number;
  sessionsToday?: number;
  goalsProgress?: {
    daily: { target: number; achieved: number; percentage: number };
    weekly: { target: number; achieved: number; percentage: number };
  };
}

interface SessionStats {
  totalSessions: number;
  totalDuration: number;
  averageDuration: number;
  averageWordsPerSession: number;
  mostProductiveTime?: string;
  longestSession?: number;
}

interface ProjectAnalytics {
  projectId: string;
  stats: WritingStats;
  sessions: SessionStats;
  trends: {
    daily: Array<{ date: string; words: number }>;
    weekly: Array<{ week: string; words: number }>;
  };
}

export class AnalyticsService extends BaseService {
  constructor() {
    super({
      name: 'analytics-service',
      version: '1.0.0',
      endpoints: ['/api/analytics'],
      dependencies: [],
      healthCheck: '/api/services/analytics/health',
      status: 'inactive'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>> {
    return this.createResponse(true, { status: 'healthy', uptime: Date.now() });
  }

  /**
   * Get writing statistics for a user
   */
  async getUserWritingStats(userId: string): Promise<ServiceResponse<WritingStats>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const now = new Date();
      const todayStart = startOfDay(now);
      const weekStart = startOfWeek(now, { weekStartsOn: 1 }); // Monday
      
      // Get daily words
      const { data: todayData } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('words_written')
        .eq('user_id', userId)
        .gte('created_at', todayStart.toISOString());
      
      const dailyWords = todayData?.reduce((sum, session) => sum + (session.words_written || 0), 0) || 0;
      
      // Get weekly words
      const { data: weekData } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('words_written')
        .eq('user_id', userId)
        .gte('created_at', weekStart.toISOString());
      
      const weeklyWords = weekData?.reduce((sum, session) => sum + (session.words_written || 0), 0) || 0;
      
      // Calculate streak
      const streak = await this.calculateWritingStreak(userId);
      
      // Calculate average words per day (last 30 days)
      const thirtyDaysAgo = subDays(now, 30);
      const { data: monthData } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('words_written, created_at')
        .eq('user_id', userId)
        .gte('created_at', thirtyDaysAgo.toISOString());
      
      let avgWordsPerDay = 0;
      if (monthData && monthData.length > 0) {
        const totalWords = monthData.reduce((sum, session) => sum + (session.words_written || 0), 0);
        const daysWithWriting = new Set(monthData.map(s => startOfDay(new Date(s.created_at)).toDateString())).size;
        avgWordsPerDay = Math.round(totalWords / Math.max(daysWithWriting, 1));
      }
      
      return {
        dailyWords,
        weeklyWords,
        streak: streak.data || 0,
        avgWordsPerDay
      };
    });
  }

  /**
   * Get writing statistics for a specific project
   */
  async getProjectWritingStats(projectId: string): Promise<ServiceResponse<WritingStats>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const now = new Date();
      const todayStart = startOfDay(now);
      const weekStart = startOfWeek(now, { weekStartsOn: 1 });
      
      // Get daily words
      const { data: todayData } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('words_written')
        .eq('project_id', projectId)
        .gte('created_at', todayStart.toISOString());
      
      const dailyWords = todayData?.reduce((sum, session) => sum + (session.words_written || 0), 0) || 0;
      
      // Get weekly words
      const { data: weekData } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('words_written')
        .eq('project_id', projectId)
        .gte('created_at', weekStart.toISOString());
      
      const weeklyWords = weekData?.reduce((sum, session) => sum + (session.words_written || 0), 0) || 0;
      
      // Calculate project-specific streak
      const streak = await this.calculateProjectWritingStreak(projectId);
      
      // Calculate average words per day for this project
      const { data: project } = await supabase
        .from(DB_TABLES.PROJECTS)
        .select('created_at')
        .eq('id', projectId)
        .single();
      
      let avgWordsPerDay = 0;
      if (project) {
        const projectAge = differenceInDays(now, new Date(project.created_at));
        const { data: allSessions } = await supabase
          .from(DB_TABLES.WRITING_SESSIONS)
          .select('words_written')
          .eq('project_id', projectId);
        
        const totalWords = allSessions?.reduce((sum, session) => sum + (session.words_written || 0), 0) || 0;
        avgWordsPerDay = Math.round(totalWords / Math.max(projectAge, 1));
      }
      
      return {
        dailyWords,
        weeklyWords,
        streak: streak.data || 0,
        avgWordsPerDay
      };
    });
  }

  /**
   * Get agent logs by user
   */
  async getAgentLogsByUser(userId: string, projectId?: string | null, limit = 50): Promise<ServiceResponse<unknown[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      let query = supabase
        .from(DB_TABLES.AGENT_LOGS)
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);
      
      if (projectId) {
        query = query.eq('project_id', projectId);
      }
      
      const { data, error } = await query;
      
      if (error) {
        logger.error('[AnalyticsService] Error getting agent logs:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Get agent logs by project
   */
  async getAgentLogsByProject(projectId: string, agentType?: string | null): Promise<ServiceResponse<unknown[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      let query = supabase
        .from(DB_TABLES.AGENT_LOGS)
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });
      
      if (agentType) {
        query = query.eq('agent_type', agentType);
      }
      
      const { data, error } = await query;
      
      if (error) {
        logger.error('[AnalyticsService] Error getting agent logs by project:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Calculate writing streak for a user
   */
  async calculateWritingStreak(userId: string): Promise<ServiceResponse<number>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Get all writing days in descending order
      const { data: sessions } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('created_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });
      
      if (!sessions || sessions.length === 0) {
        return 0;
      }
      
      // Group sessions by day
      const writingDays = new Set(
        sessions.map(s => startOfDay(new Date(s.created_at)).toDateString())
      );
      
      let streak = 0;
      const today = startOfDay(new Date());
      let checkDate = today;
      
      while (writingDays.has(checkDate.toDateString())) {
        streak++;
        checkDate = subDays(checkDate, 1);
      }
      
      if (streak === 0 && writingDays.has(subDays(today, 1).toDateString())) {
        checkDate = subDays(today, 1);
        while (writingDays.has(checkDate.toDateString())) {
          streak++;
          checkDate = subDays(checkDate, 1);
        }
      }
      
      return streak;
    });
  }

  /**
   * Calculate writing streak for a specific project
   */
  async calculateProjectWritingStreak(projectId: string): Promise<ServiceResponse<number>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data: sessions } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('created_at')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });
      
      if (!sessions || sessions.length === 0) {
        return 0;
      }
      
      const writingDays = new Set(
        sessions.map(s => startOfDay(new Date(s.created_at)).toDateString())
      );
      
      let streak = 0;
      const today = startOfDay(new Date());
      let checkDate = today;
      
      while (writingDays.has(checkDate.toDateString())) {
        streak++;
        checkDate = subDays(checkDate, 1);
      }
      
      if (streak === 0 && writingDays.has(subDays(today, 1).toDateString())) {
        checkDate = subDays(today, 1);
        while (writingDays.has(checkDate.toDateString())) {
          streak++;
          checkDate = subDays(checkDate, 1);
        }
      }
      
      return streak;
    });
  }

  /**
   * Get comprehensive analytics for a project
   */
  async getProjectAnalytics(projectId: string): Promise<ServiceResponse<ProjectAnalytics>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Get writing stats
      const statsResponse = await this.getProjectWritingStats(projectId);
      const stats = statsResponse.data || {
        dailyWords: 0,
        weeklyWords: 0,
        streak: 0,
        avgWordsPerDay: 0
      };
      
      // Get session statistics
      const { data: sessions } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('*')
        .eq('project_id', projectId);
      
      let sessionStats: SessionStats = {
        totalSessions: 0,
        totalDuration: 0,
        averageDuration: 0,
        averageWordsPerSession: 0
      };
      
      if (sessions && sessions.length > 0) {
        const totalWords = sessions.reduce((sum, s) => sum + (s.words_written || 0), 0);
        const totalDuration = sessions.reduce((sum, s) => sum + (s.duration_minutes || 0), 0);
        
        sessionStats = {
          totalSessions: sessions.length,
          totalDuration,
          averageDuration: Math.round(totalDuration / sessions.length),
          averageWordsPerSession: Math.round(totalWords / sessions.length)
        };
      }
      
      // Get trends (last 7 days and last 4 weeks)
      const sevenDaysAgo = subDays(new Date(), 7);
      const fourWeeksAgo = subDays(new Date(), 28);
      
      // Daily trends
      const { data: dailyData } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('created_at, words_written')
        .eq('project_id', projectId)
        .gte('created_at', sevenDaysAgo.toISOString())
        .order('created_at');
      
      const dailyTrends = this.aggregateDailyTrends(dailyData || []);
      
      // Weekly trends
      const { data: weeklyData } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('created_at, words_written')
        .eq('project_id', projectId)
        .gte('created_at', fourWeeksAgo.toISOString())
        .order('created_at');
      
      const weeklyTrends = this.aggregateWeeklyTrends(weeklyData || []);
      
      return {
        projectId,
        stats,
        sessions: sessionStats,
        trends: {
          daily: dailyTrends,
          weekly: weeklyTrends
        }
      };
    });
  }

  /**
   * Track a writing session
   */
  async trackWritingSession(data: {
    userId: string;
    projectId: string;
    wordsWritten: number;
    durationMinutes: number;
    chapterId?: string;
  }): Promise<ServiceResponse<WritingSession>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data: session, error } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .insert({
          user_id: data.userId,
          project_id: data.projectId,
          chapter_id: data.chapterId,
          words_written: data.wordsWritten,
          duration_minutes: data.durationMinutes,
          created_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        logger.error('[AnalyticsService] Error tracking writing session:', error);
        throw error;
      }
      
      if (!session) {
        throw new Error('Failed to create writing session');
      }
      
      return session;
    });
  }

  /**
   * Get user's writing goals and progress
   */
  async getUserGoalsProgress(userId: string): Promise<ServiceResponse<WritingStats>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Get user's goals
      const { data: goals } = await supabase
        .from(DB_TABLES.WRITING_GOALS)
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();
      
      // Get current stats
      const statsResponse = await this.getUserWritingStats(userId);
      const stats = statsResponse.data || {
        dailyWords: 0,
        weeklyWords: 0,
        streak: 0,
        avgWordsPerDay: 0
      };
      
      if (goals) {
        const dailyTarget = goals.daily_word_target || 1000;
        const weeklyTarget = goals.weekly_word_target || 7000;
        
        const result: WritingStats = {
          ...stats,
          goalsProgress: {
            daily: {
              target: dailyTarget,
              achieved: stats.dailyWords,
              percentage: Math.min(100, Math.round((stats.dailyWords / dailyTarget) * 100))
            },
            weekly: {
              target: weeklyTarget,
              achieved: stats.weeklyWords,
              percentage: Math.min(100, Math.round((stats.weeklyWords / weeklyTarget) * 100))
            }
          }
        };
        return result;
      }
      
      return stats;
    });
  }

  /**
   * Aggregate daily trends from session data
   */
  private aggregateDailyTrends(sessions: Array<{ created_at: string; words_written: number }>): Array<{ date: string; words: number }> {
    const dailyMap = new Map<string, number>();
    
    sessions.forEach(session => {
      const date = startOfDay(new Date(session.created_at)).toISOString().split('T')[0];
      dailyMap.set(date, (dailyMap.get(date) || 0) + (session.words_written || 0));
    });
    
    return Array.from(dailyMap.entries())
      .map(([date, words]) => ({ date, words }))
      .sort((a, b) => {
        // Ensure both values are strings for localeCompare
        const dateA = String(a.date);
        const dateB = String(b.date);
        return dateA.localeCompare(dateB);
      });
  }

  /**
   * Aggregate weekly trends from session data
   */
  private aggregateWeeklyTrends(sessions: Array<{ created_at: string; words_written: number }>): Array<{ week: string; words: number }> {
    const weeklyMap = new Map<string, number>();
    
    sessions.forEach(session => {
      const weekStart = startOfWeek(new Date(session.created_at), { weekStartsOn: 1 });
      const weekKey = weekStart.toISOString().split('T')[0];
      weeklyMap.set(weekKey, (weeklyMap.get(weekKey) || 0) + (session.words_written || 0));
    });
    
    return Array.from(weeklyMap.entries())
      .map(([week, words]) => ({ week, words }))
      .sort((a, b) => {
        // Ensure both values are strings for localeCompare
        const weekA = String(a.week);
        const weekB = String(b.week);
        return weekA.localeCompare(weekB);
      });
  }

  /**
   * Log an editing session
   */
  async logEditingSession(data: {
    userId: string;
    projectId: string;
    chapterId?: string;
    duration: number;
    editsCount: number;
    aiSuggestionsUsed?: number;
  }): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from(DB_TABLES.EDITING_SESSIONS)
        .insert({
          user_id: data.userId,
          project_id: data.projectId,
          chapter_id: data.chapterId,
          duration_minutes: data.duration,
          edits_count: data.editsCount,
          ai_suggestions_used: data.aiSuggestionsUsed || 0,
          created_at: new Date().toISOString()
        });
      
      if (error) {
        logger.error('[AnalyticsService] Error logging editing session:', error);
        throw error;
      }
      
      return { success: true };
    });
  }

  /**
   * Record AI suggestion feedback
   */
  async recordAISuggestionFeedback(data: {
    userId: string;
    projectId: string;
    suggestionId: string;
    accepted: boolean;
    feedbackType?: string;
    rating?: number;
  }): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from(DB_TABLES.AI_SUGGESTION_FEEDBACK)
        .insert({
          user_id: data.userId,
          project_id: data.projectId,
          suggestion_id: data.suggestionId,
          accepted: data.accepted,
          feedback_type: data.feedbackType,
          rating: data.rating,
          created_at: new Date().toISOString()
        });
      
      if (error) {
        logger.error('[AnalyticsService] Error recording AI suggestion feedback:', error);
        throw error;
      }
      
      return { success: true };
    });
  }

  /**
   * Get AI usage analytics
   */
  async getAIUsageAnalytics(userId: string, timeframe: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const startDate = this.getTimeframeStartDate(timeframe);
      
      const { data, error } = await supabase
        .from(DB_TABLES.AGENT_LOGS)
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });
      
      if (error) {
        logger.error('[AnalyticsService] Error getting AI usage analytics:', error);
        throw error;
      }
      
      // Aggregate by agent type
      interface AgentUsage {
        count: number;
        totalTokens: number;
        totalDuration: number;
      }
      
      const agentUsage = data?.reduce((acc, log) => {
        const agentType = log.agent_type || 'unknown';
        if (!acc[agentType]) {
          acc[agentType] = {
            count: 0,
            totalTokens: 0,
            totalDuration: 0
          };
        }
        acc[agentType].count++;
        acc[agentType].totalTokens += log.tokens_used || 0;
        acc[agentType].totalDuration += log.duration_ms || 0;
        return acc;
      }, {} as Record<string, AgentUsage>) || {};
      
      return {
        timeframe,
        totalUsage: data?.length || 0,
        agentUsage,
        logs: data
      };
    });
  }

  /**
   * Log AI usage
   */
  async logAIUsage(data: {
    userId: string;
    projectId?: string;
    agentType: string;
    operation: string;
    tokensUsed: number;
    durationMs: number;
    model?: string;
    success: boolean;
    error?: string;
  }): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from(DB_TABLES.AGENT_LOGS)
        .insert({
          user_id: data.userId,
          project_id: data.projectId,
          agent_type: data.agentType,
          operation: data.operation,
          tokens_used: data.tokensUsed,
          duration_ms: data.durationMs,
          model_used: data.model,
          success: data.success,
          error: data.error,
          created_at: new Date().toISOString()
        });
      
      if (error) {
        logger.error('[AnalyticsService] Error logging AI usage:', error);
        throw error;
      }
      
      return { success: true };
    });
  }

  /**
   * Get behavioral analytics
   */
  async getBehavioralAnalytics(userId: string, timeframe: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const startDate = this.getTimeframeStartDate(timeframe);
      
      const { data, error } = await supabase
        .from(DB_TABLES.BEHAVIORAL_ANALYTICS)
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });
      
      if (error) {
        logger.error('[AnalyticsService] Error getting behavioral analytics:', error);
        throw error;
      }
      
      // Aggregate by event type
      const eventTypes = data?.reduce((acc, event) => {
        const eventType = event.event_type || 'unknown';
        if (!acc[eventType]) {
          acc[eventType] = 0;
        }
        acc[eventType]++;
        return acc;
      }, {} as Record<string, number>) || {};
      
      return {
        timeframe,
        totalEvents: data?.length || 0,
        eventTypes,
        events: data
      };
    });
  }

  /**
   * Create a writing session
   */
  async createWritingSession(data: {
    userId: string;
    projectId: string;
    chapterId?: string;
  }): Promise<ServiceResponse<{ sessionId: string }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data: session, error } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .insert({
          user_id: data.userId,  
          project_id: data.projectId,
          chapter_id: data.chapterId,
          started_at: new Date().toISOString(),
          words_written: 0,
          duration_minutes: 0
        })
        .select()
        .single();
      
      if (error) {
        logger.error('[AnalyticsService] Error creating writing session:', error);
        throw error;
      }
      
      if (!session) {
        throw new Error('Failed to create writing session');
      }
      
      return { sessionId: session.id };
    });
  }

  /**
   * Get chapter analytics
   */
  async getChapterAnalytics(projectId: string): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data: chapters, error } = await supabase
        .from(DB_TABLES.CHAPTERS)
        .select('id, title, word_count, status, created_at, updated_at')
        .eq('project_id', projectId)
        .order('order_index');
      
      if (error) {
        logger.error('[AnalyticsService] Error getting chapter analytics:', error);
        throw error;
      }
      
      const analytics = await Promise.all(
        (chapters || []).map(async (chapter) => {
          const { data: sessions } = await supabase
            .from(DB_TABLES.WRITING_SESSIONS)
            .select('words_written, duration_minutes')
            .eq('chapter_id', chapter.id);
          
          const totalWords = sessions?.reduce((sum, s) => sum + (s.words_written || 0), 0) || 0;
          const totalDuration = sessions?.reduce((sum, s) => sum + (s.duration_minutes || 0), 0) || 0;
          
          return {
            ...chapter,
            totalWordsWritten: totalWords,
            totalTimeSpent: totalDuration,
            averageWordsPerSession: sessions?.length ? Math.round(totalWords / sessions.length) : 0
          };
        })
      );
      
      return analytics;
    });
  }

  /**
   * Get productivity metrics
   */
  async getProductivityMetrics(userId: string, timeframe: 'day' | 'week' | 'month' | 'year' = 'week'): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const startDate = this.getTimeframeStartDate(timeframe);
      const supabase = await createTypedServerClient();
      
      // Get writing sessions
      const { data: sessions } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString());
      
      // Calculate productivity metrics
      const totalWords = sessions?.reduce((sum, s) => sum + (s.words_written || 0), 0) || 0;
      const totalDuration = sessions?.reduce((sum, s) => sum + (s.duration_minutes || 0), 0) || 0;
      const wordsPerMinute = totalDuration > 0 ? totalWords / totalDuration : 0;
      
      // Get best writing time
      const hourlyProductivity = sessions?.reduce((acc, session) => {
        const hour = new Date(session.created_at).getHours();
        if (!acc[hour]) {
          acc[hour] = { words: 0, duration: 0 };
        }
        acc[hour].words += session.words_written || 0;
        acc[hour].duration += session.duration_minutes || 0;
        return acc;
      }, {} as Record<number, { words: number; duration: number }>) || {};
      
      const bestHour = Object.entries(hourlyProductivity)
        .sort(([, a], [, b]) => (b as { words: number; duration: number }).words - (a as { words: number; duration: number }).words)[0];
      
      return {
        totalWords,
        totalDuration,
        averageWordsPerSession: sessions?.length ? Math.round(totalWords / sessions.length) : 0,
        wordsPerMinute: Math.round(wordsPerMinute),
        bestWritingTime: bestHour ? `${bestHour[0]}:00` : 'N/A',
        sessionsCount: sessions?.length || 0
      };
    });
  }

  /**
   * Get profile performance metrics
   */
  async getProfilePerformanceMetrics(userId: string): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Get all projects
      const { data: projects } = await supabase
        .from(DB_TABLES.PROJECTS)
        .select('id, title, word_count, status, created_at')
        .eq('user_id', userId);
      
      // Get total stats
      const totalProjects = projects?.length || 0;
      const completedProjects = projects?.filter(p => p.status === 'completed').length || 0;
      const totalWords = projects?.reduce((sum, p) => sum + (p.word_count || 0), 0) || 0;
      
      // Get recent activity
      const thirtyDaysAgo = subDays(new Date(), 30);
      const { data: recentSessions } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', thirtyDaysAgo.toISOString());
      
      const recentWords = recentSessions?.reduce((sum, s) => sum + (s.words_written || 0), 0) || 0;
      
      return {
        totalProjects,
        completedProjects,
        completionRate: totalProjects > 0 ? (completedProjects / totalProjects) * 100 : 0,
        totalWords,
        recentWords,
        projectsInProgress: totalProjects - completedProjects
      };
    });
  }

  /**
   * Get quality metrics
   */
  async getQualityMetrics(projectId: string): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Get project data
      const { data: project } = await supabase
        .from(DB_TABLES.PROJECTS)
        .select('*')
        .eq('id', projectId)
        .single();
      
      if (!project) {
        throw new Error('Project not found');
      }
      
      // Get chapters
      const { data: chapters } = await supabase
        .from(DB_TABLES.CHAPTERS)
        .select('*')
        .eq('project_id', projectId);
      
      // Calculate quality metrics
      const avgChapterLength = chapters?.length 
        ? chapters.reduce((sum, c) => sum + (c.word_count || 0), 0) / chapters.length 
        : 0;
      
      // Get consistency scores if available
      const { data: consistencyReports } = await supabase
        .from(DB_TABLES.CONSISTENCY_REPORTS)
        .select('score')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false })
        .limit(1);
      
      return {
        projectId,
        totalWords: project.word_count || 0,
        chapterCount: chapters?.length || 0,
        averageChapterLength: Math.round(avgChapterLength),
        consistencyScore: consistencyReports?.[0]?.score || null,
        completionStatus: project.status
      };
    });
  }

  /**
   * Check daily analysis limit
   */
  async checkDailyAnalysisLimit(userId: string, analysisType: string): Promise<ServiceResponse<{ allowed: boolean; remaining: number }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const today = startOfDay(new Date());
      
      // Get user's subscription tier
      const { data: subscription } = await supabase
        .from(DB_TABLES.USER_SUBSCRIPTIONS)
        .select('tier_id')
        .eq('user_id', userId)
        .eq('status', 'active')
        .single();
      
      // Define limits based on tier
      const limits: Record<string, number> = {
        free: 5,
        starter: 20,
        professional: 50,
        studio: 100
      };
      
      const userLimit = limits[subscription?.tier_id || 'free'] || 5;
      
      // Count today's analyses
      const { count } = await supabase
        .from(DB_TABLES.ANALYSIS_LOGS)
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('analysis_type', analysisType)
        .gte('created_at', today.toISOString());
      
      const used = count || 0;
      
      return {
        allowed: used < userLimit,
        remaining: Math.max(0, userLimit - used)
      };
    });
  }

  /**
   * Save quality metrics
   */
  async saveQualityMetrics(data: {
    projectId: string;
    userId: string;
    metrics: Record<string, unknown>;
  }): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from(DB_TABLES.QUALITY_METRICS)
        .insert({
          project_id: data.projectId,
          user_id: data.userId,
          metrics: data.metrics,
          created_at: new Date().toISOString()
        });
      
      if (error) {
        logger.error('[AnalyticsService] Error saving quality metrics:', error);
        throw error;
      }
      
      return { success: true };
    });
  }

  /**
   * Get recommendations based on user behavior
   */
  async getRecommendations(userId: string): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Get user's writing patterns
      const { data: sessions } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(50);
      
      // Analyze patterns
      const recommendations = [];
      
      // Check writing frequency
      if (sessions && sessions.length > 0) {
        const daysSinceLastSession = differenceInDays(new Date(), new Date(sessions[0].created_at));
        if (daysSinceLastSession > 3) {
          recommendations.push({
            type: 'frequency',
            title: 'Get back to writing',
            message: `It's been ${daysSinceLastSession} days since your last writing session`
          });
        }
      }
      
      // Check productivity patterns
      const avgWords = sessions?.reduce((sum, s) => sum + (s.words_written || 0), 0) || 0 / (sessions?.length || 1);
      if (avgWords < 500 && sessions && sessions.length > 5) {
        recommendations.push({
          type: 'productivity',
          title: 'Boost your productivity',
          message: 'Try setting smaller, achievable goals to build momentum'
        });
      }
      
      return recommendations;
    });
  }

  /**
   * Get popular searches
   */
  async getPopularSearches(timeframe: 'day' | 'week' | 'month' = 'week'): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const startDate = this.getTimeframeStartDate(timeframe);
      
      const { data, error } = await supabase
        .from(DB_TABLES.SEARCH_ANALYTICS)
        .select('query, count')
        .gte('created_at', startDate.toISOString())
        .order('count', { ascending: false })
        .limit(20);
      
      if (error) {
        logger.error('[AnalyticsService] Error getting popular searches:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Track search event
   */
  async trackSearchEvent(data: {
    userId: string;
    query: string;
    resultsCount: number;
    clickedResult?: string;
  }): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from(DB_TABLES.SEARCH_ANALYTICS)
        .insert({
          user_id: data.userId,
          query: data.query,
          results_count: data.resultsCount,
          clicked_result: data.clickedResult,
          created_at: new Date().toISOString()
        });
      
      if (error) {
        logger.error('[AnalyticsService] Error tracking search event:', error);
        throw error;
      }
      
      return { success: true };
    });
  }

  /**
   * Track selection event
   */
  async trackSelectionEvent(data: {
    userId: string;
    projectId: string;
    selectionType: string;
    selectionData?: Record<string, unknown>;
    outcomeData?: Record<string, unknown>;
  }): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from(DB_TABLES.SELECTION_ANALYTICS)
        .insert({
          user_id: data.userId,
          project_id: data.projectId,
          selection_type: data.selectionType,
          selection_data: data.selectionData || {},
          outcome_data: data.outcomeData || {},
          created_at: new Date().toISOString()
        });
      
      if (error) {
        logger.error('[AnalyticsService] Error tracking selection event:', error);
        throw error;
      }
      
      return { success: true };
    });
  }

  /**
   * Get selection analytics
   */
  async getSelectionAnalytics(projectId: string): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from(DB_TABLES.SELECTION_ANALYTICS)
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });
      
      if (error) {
        logger.error('[AnalyticsService] Error getting selection analytics:', error);
        throw error;
      }
      
      // Aggregate by selection type
      const typeBreakdown = data?.reduce((acc, event) => {
        const selectionType = event.selection_type || 'unknown';
        if (!acc[selectionType]) {
          acc[selectionType] = 0;
        }
        acc[selectionType]++;
        return acc;
      }, {} as Record<string, number>) || {};
      
      return {
        totalSelections: data?.length || 0,
        typeBreakdown,
        selections: data
      };
    });
  }

  /**
   * Get selection success patterns
   */
  async getSelectionSuccessPatterns(userId: string): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from(DB_TABLES.SELECTION_ANALYTICS)
        .select('*')
        .eq('user_id', userId)
        .not('outcome_data', 'is', null)
        .order('created_at', { ascending: false })
        .limit(100);
      
      if (error) {
        logger.error('[AnalyticsService] Error getting selection success patterns:', error);
        throw error;
      }
      
      // Analyze patterns
      interface SelectionPattern {
        total: number;
        successful: number;
      }
      
      const patterns = data?.reduce((acc, event) => {
        const success = event.outcome_data?.success || false;
        const selectionType = event.selection_type || 'unknown';
        const key = selectionType as string; // Ensure string type
        if (!acc[key]) {
          acc[key] = { total: 0, successful: 0 };
        }
        acc[key].total++;
        if (success) {
          acc[key].successful++;
        }
        return acc;
      }, {} as Record<string, SelectionPattern>) || {};
      
      return Object.entries(patterns).map(([type, stats]) => {
        const pattern = stats as SelectionPattern;
        return {
          selectionType: type,
          totalUses: pattern.total,
          successCount: pattern.successful,
          successRate: pattern.total > 0 ? (pattern.successful / pattern.total) * 100 : 0
        };
      });
    });
  }

  /**
   * Get session analytics
   */
  async getSessionAnalytics(userId: string, timeframe: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const startDate = this.getTimeframeStartDate(timeframe);
      
      const { data: sessions, error } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });
      
      if (error) {
        logger.error('[AnalyticsService] Error getting session analytics:', error);
        throw error;
      }
      
      // Calculate session stats
      const totalSessions = sessions?.length || 0;
      const totalDuration = sessions?.reduce((sum, s) => sum + (s.duration_minutes || 0), 0) || 0;
      const totalWords = sessions?.reduce((sum, s) => sum + (s.words_written || 0), 0) || 0;
      
      // Group by day
      const dailyBreakdown = sessions?.reduce((acc, session) => {
        const date = startOfDay(new Date(session.created_at)).toISOString().split('T')[0];
        if (!acc[date]) {
          acc[date] = { sessions: 0, words: 0, duration: 0 };
        }
        acc[date].sessions++;
        acc[date].words += session.words_written || 0;
        acc[date].duration += session.duration_minutes || 0;
        return acc;
      }, {} as Record<string, { sessions: number; words: number; duration: number }>) || {};
      
      return {
        timeframe,
        totalSessions,
        totalDuration,
        totalWords,
        averageSessionDuration: totalSessions > 0 ? Math.round(totalDuration / totalSessions) : 0,
        averageWordsPerSession: totalSessions > 0 ? Math.round(totalWords / totalSessions) : 0,
        dailyBreakdown
      };
    });
  }

  /**
   * Update daily analytics
   */
  async updateDailyAnalytics(userId: string): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const today = startOfDay(new Date());
      
      // Get today's sessions
      const { data: sessions } = await supabase
        .from(DB_TABLES.WRITING_SESSIONS)
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', today.toISOString());
      
      const wordsToday = sessions?.reduce((sum, s) => sum + (s.words_written || 0), 0) || 0;
      const sessionsToday = sessions?.length || 0;
      const durationToday = sessions?.reduce((sum, s) => sum + (s.duration_minutes || 0), 0) || 0;
      
      // Update or insert daily analytics
      const { error } = await supabase
        .from(DB_TABLES.DAILY_ANALYTICS)
        .upsert({
          user_id: userId,
          date: today.toISOString().split('T')[0],
          words_written: wordsToday,
          sessions_count: sessionsToday,
          total_duration: durationToday,
          updated_at: new Date().toISOString()
        });
      
      if (error) {
        logger.error('[AnalyticsService] Error updating daily analytics:', error);
        throw error;
      }
      
      return { success: true };
    });
  }

  /**
   * Get user tasks
   */
  async getUserTasks(userId: string): Promise<ServiceResponse<unknown>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from(DB_TABLES.PROCESSING_TASKS)
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });
      
      if (error) {
        logger.error('[AnalyticsService] Error getting user tasks:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Get user goals
   */
  async getUserGoals(userId: string): Promise<ServiceResponse<WritingGoal[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from(DB_TABLES.WRITING_GOALS)
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });
      
      if (error) {
        logger.error('[AnalyticsService] Error getting user goals:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Create multiple writing goals
   */
  async createMultipleWritingGoals(userId: string, goals: Array<{
    type: string;
    target: number;
    period: string;
    projectId?: string;
  }>): Promise<ServiceResponse<WritingGoal[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const goalRecords = goals.map(goal => ({
        user_id: userId,
        project_id: goal.projectId,
        daily_word_target: goal.type === 'daily' ? goal.target : null,
        weekly_word_target: goal.type === 'weekly' ? goal.target : null,
        monthly_word_target: goal.type === 'monthly' ? goal.target : null,
        is_active: true,
        created_at: new Date().toISOString()
      }));
      
      const { data, error } = await supabase
        .from(DB_TABLES.WRITING_GOALS)
        .insert(goalRecords)
        .select();
      
      if (error) {
        logger.error('[AnalyticsService] Error creating writing goals:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Create a writing goal
   */
  async createWritingGoal(data: {
    userId: string;
    projectId?: string;
    dailyTarget?: number;
    weeklyTarget?: number;
    monthlyTarget?: number;
  }): Promise<ServiceResponse<WritingGoal>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data: goal, error } = await supabase
        .from(DB_TABLES.WRITING_GOALS)
        .insert({
          user_id: data.userId,
          project_id: data.projectId,
          daily_word_target: data.dailyTarget,
          weekly_word_target: data.weeklyTarget,
          monthly_word_target: data.monthlyTarget,
          is_active: true,
          created_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        logger.error('[AnalyticsService] Error creating writing goal:', error);
        throw error;
      }
      
      if (!goal) {
        throw new Error('Failed to create writing goal');
      }
      
      return goal;
    });
  }

  /**
   * Update a writing goal
   */
  async updateWritingGoal(goalId: string, updates: {
    dailyTarget?: number;
    weeklyTarget?: number;
    monthlyTarget?: number;
    isActive?: boolean;
  }): Promise<ServiceResponse<WritingGoal>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      interface WritingGoalUpdateData {
        updated_at: string;
        daily_word_target?: number;
        weekly_word_target?: number;
        monthly_word_target?: number;
        is_active?: boolean;
      }
      
      const updateData: WritingGoalUpdateData = {
        updated_at: new Date().toISOString()
      };
      
      if (updates.dailyTarget !== undefined) updateData.daily_word_target = updates.dailyTarget;
      if (updates.weeklyTarget !== undefined) updateData.weekly_word_target = updates.weeklyTarget;
      if (updates.monthlyTarget !== undefined) updateData.monthly_word_target = updates.monthlyTarget;
      if (updates.isActive !== undefined) updateData.is_active = updates.isActive;
      
      const { data: goal, error } = await supabase
        .from(DB_TABLES.WRITING_GOALS)
        .update(updateData)
        .eq('id', goalId)
        .select()
        .single();
      
      if (error) {
        logger.error('[AnalyticsService] Error updating writing goal:', error);
        throw error;
      }
      
      if (!goal) {
        throw new Error('Writing goal not found');
      }
      
      return goal;
    });
  }

  /**
   * Delete a writing goal
   */
  async deleteWritingGoal(goalId: string): Promise<ServiceResponse<{ success: boolean }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from(DB_TABLES.WRITING_GOALS)
        .delete()
        .eq('id', goalId);
      
      if (error) {
        logger.error('[AnalyticsService] Error deleting writing goal:', error);
        throw error;
      }
      
      return { success: true };
    });
  }

  /**
   * Helper method to get timeframe start date
   */
  private getTimeframeStartDate(timeframe: 'day' | 'week' | 'month' | 'year'): Date {
    const now = new Date();
    switch (timeframe) {
      case 'day':
        return startOfDay(now);
      case 'week':
        return startOfWeek(now, { weekStartsOn: 1 });
      case 'month':
        return new Date(now.getFullYear(), now.getMonth(), 1);
      case 'year':
        return new Date(now.getFullYear(), 0, 1);
      default:
        return startOfDay(now);
    }
  }
}
