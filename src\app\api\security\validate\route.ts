import { NextRequest, NextResponse } from 'next/server';
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware';
import { AdminSecurityMiddleware } from '@/lib/api/admin-security-middleware';
import { apiSchemas } from '@/lib/validation/api-schemas';
import { logger } from '@/lib/services/logger';
import { z } from 'zod';

// Security test validation schema
const securityTestSchema = z.object({
  testType: z.enum([
    'xss_protection',
    'sql_injection',
    'command_injection',
    'path_traversal',
    'prototype_pollution',
    'size_limits',
    'rate_limiting',
    'csrf_protection',
    'input_sanitization'
  ]),
  payload: z.unknown(),
  expectedResult: z.enum(['blocked', 'sanitized', 'allowed']),
  description: z.string().max(500).optional()
});

export async function POST(request: NextRequest) {
  // Enhanced request validation with strict security checks
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: securityTestSchema,
    rateLimitKey: 'api',
    rateLimitCost: 2,
    maxBodySize: 10 * 1024, // 10KB max for security tests
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    validateOrigin: true,
    allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    requiredHeaders: ['content-type'],
    customValidator: async (req) => {
      // Only allow security testing in development or staging
      if (process.env.NODE_ENV === 'production') {
        return {
          valid: false,
          error: 'Security testing endpoint not available in production'
        };
      }
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;

  try {
    const { testType, payload, expectedResult, description } = context.body;

    // Run the specific security test
    const testResult = await runSecurityTest(testType, payload, context);

    // Log the security test
    logger.info('Security validation test performed', {
      testType,
      expectedResult,
      actualResult: testResult.result,
      passed: testResult.result === expectedResult,
      clientIP: context.clientIP,
      userAgent: context.userAgent,
      description
    });

    return NextResponse.json({
      success: true,
      test: {
        type: testType,
        description,
        expected: expectedResult,
        actual: testResult.result,
        passed: testResult.result === expectedResult,
        details: testResult.details,
        securityScore: calculateSecurityScore(testResult),
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Security validation test error:', {
      error,
      clientIP: context.clientIP,
      requestBody: context.body
    });

    return NextResponse.json(
      { error: 'Security test failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * Runs specific security tests based on test type
 */
interface SecurityTestContext {
  clientIP: string;
  userAgent: string;
  [key: string]: unknown;
}

interface SecurityTestResult {
  result: 'blocked' | 'sanitized' | 'allowed';
  details: Record<string, unknown>;
}

async function runSecurityTest(testType: string, payload: unknown, context: SecurityTestContext): Promise<SecurityTestResult> {
  const details: Record<string, unknown> = {};

  switch (testType) {
    case 'xss_protection':
      return testXSSProtection(payload, details);
    
    case 'sql_injection':
      return testSQLInjectionProtection(payload, details);
    
    case 'command_injection':
      return testCommandInjectionProtection(payload, details);
    
    case 'path_traversal':
      return testPathTraversalProtection(payload, details);
    
    case 'prototype_pollution':
      return testPrototypePollutionProtection(payload, details);
    
    case 'size_limits':
      return testSizeLimits(payload, details);
    
    case 'rate_limiting':
      return testRateLimiting(context, details);
    
    case 'csrf_protection':
      return testCSRFProtection(context, details);
    
    case 'input_sanitization':
      return testInputSanitization(payload, details);
    
    default:
      throw new Error(`Unknown test type: ${testType}`);
  }
}

/**
 * Test XSS protection
 */
function testXSSProtection(payload: unknown, details: Record<string, unknown>): {
  result: 'blocked' | 'sanitized' | 'allowed';
  details: Record<string, unknown>;
} {
  const maliciousPatterns = [
    '<script>alert("xss")</script>',
    'javascript:alert("xss")',
    '<img src="x" onerror="alert(1)">',
    '<iframe src="javascript:alert(1)"></iframe>',
    '"><script>alert(1)</script>',
    "';alert(String.fromCharCode(88,83,83))//';",
    '<svg onload="alert(1)">',
    '<body onload="alert(1)">'
  ];

  const payloadString = JSON.stringify(payload);
  details.payloadString = payloadString;
  details.detectedPatterns = [];

  for (const pattern of maliciousPatterns) {
    if (payloadString.toLowerCase().includes(pattern.toLowerCase())) {
      details.detectedPatterns.push(pattern);
    }
  }

  // Check if any malicious patterns were detected
  if (details.detectedPatterns.length > 0) {
    details.threatLevel = 'high';
    details.recommendation = 'Block request - XSS attempt detected';
    return { result: 'blocked', details };
  }

  // Check for suspicious patterns that might be sanitized
  const suspiciousPatterns = ['<script', 'javascript:', 'onclick=', 'onerror='];
  const suspiciousFound = suspiciousPatterns.some(pattern => 
    payloadString.toLowerCase().includes(pattern)
  );

  if (suspiciousFound) {
    details.threatLevel = 'medium';
    details.recommendation = 'Sanitize input - Suspicious patterns detected';
    return { result: 'sanitized', details };
  }

  details.threatLevel = 'low';
  details.recommendation = 'Allow - No XSS patterns detected';
  return { result: 'allowed', details };
}

/**
 * Test SQL injection protection
 */
function testSQLInjectionProtection(payload: unknown, details: Record<string, unknown>): {
  result: 'blocked' | 'sanitized' | 'allowed';
  details: Record<string, unknown>;
} {
  const sqlInjectionPatterns = [
    'union select',
    'drop table',
    'delete from',
    'insert into',
    'update set',
    '\' or \'1\'=\'1',
    '" or "1"="1',
    '; drop table',
    '-- ',
    '/*',
    'xp_cmdshell',
    'sp_executesql'
  ];

  const payloadString = JSON.stringify(payload).toLowerCase();
  details.payloadString = payloadString;
  details.detectedPatterns = [];

  for (const pattern of sqlInjectionPatterns) {
    if (payloadString.includes(pattern.toLowerCase())) {
      details.detectedPatterns.push(pattern);
    }
  }

  if (details.detectedPatterns.length > 0) {
    details.threatLevel = 'high';
    details.recommendation = 'Block request - SQL injection attempt detected';
    return { result: 'blocked', details };
  }

  details.threatLevel = 'low';
  details.recommendation = 'Allow - No SQL injection patterns detected';
  return { result: 'allowed', details };
}

/**
 * Test command injection protection
 */
function testCommandInjectionProtection(payload: unknown, details: Record<string, unknown>): {
  result: 'blocked' | 'sanitized' | 'allowed';
  details: Record<string, unknown>;
} {
  const commandInjectionPatterns = [
    '| cat',
    '| ls',
    '| rm',
    '| curl',
    '| wget',
    '&& cat',
    '&& ls',
    '; cat',
    '; ls',
    '`cat',
    '$(cat',
    'exec(',
    'system(',
    'shell_exec('
  ];

  const payloadString = JSON.stringify(payload).toLowerCase();
  details.payloadString = payloadString;
  details.detectedPatterns = [];

  for (const pattern of commandInjectionPatterns) {
    if (payloadString.includes(pattern.toLowerCase())) {
      details.detectedPatterns.push(pattern);
    }
  }

  if (details.detectedPatterns.length > 0) {
    details.threatLevel = 'high';
    details.recommendation = 'Block request - Command injection attempt detected';
    return { result: 'blocked', details };
  }

  details.threatLevel = 'low';
  details.recommendation = 'Allow - No command injection patterns detected';
  return { result: 'allowed', details };
}

/**
 * Test path traversal protection
 */
function testPathTraversalProtection(payload: unknown, details: Record<string, unknown>): {
  result: 'blocked' | 'sanitized' | 'allowed';
  details: Record<string, unknown>;
} {
  const pathTraversalPatterns = [
    '../',
    '..\\',
    '..../',
    '....\\',
    '%2e%2e%2f',
    '%2e%2e%5c',
    '..%2f',
    '..%5c'
  ];

  const payloadString = JSON.stringify(payload).toLowerCase();
  details.payloadString = payloadString;
  details.detectedPatterns = [];

  for (const pattern of pathTraversalPatterns) {
    if (payloadString.includes(pattern.toLowerCase())) {
      details.detectedPatterns.push(pattern);
    }
  }

  if (details.detectedPatterns.length > 0) {
    details.threatLevel = 'high';
    details.recommendation = 'Block request - Path traversal attempt detected';
    return { result: 'blocked', details };
  }

  details.threatLevel = 'low';
  details.recommendation = 'Allow - No path traversal patterns detected';
  return { result: 'allowed', details };
}

/**
 * Test prototype pollution protection
 */
function testPrototypePollutionProtection(payload: unknown, details: Record<string, unknown>): {
  result: 'blocked' | 'sanitized' | 'allowed';
  details: Record<string, unknown>;
} {
  const prototypePollutionPatterns = [
    '__proto__',
    'constructor',
    'prototype'
  ];

  const payloadString = JSON.stringify(payload).toLowerCase();
  details.payloadString = payloadString;
  details.detectedPatterns = [];

  // Check for prototype pollution attempts
  for (const pattern of prototypePollutionPatterns) {
    if (payloadString.includes(pattern.toLowerCase())) {
      details.detectedPatterns.push(pattern);
    }
  }

  // Check if payload has dangerous structure
  if (typeof payload === 'object' && payload !== null) {
    if ('__proto__' in payload || 'constructor' in payload || 'prototype' in payload) {
      details.detectedPatterns.push('dangerous_object_structure');
    }
  }

  if (details.detectedPatterns.length > 0) {
    details.threatLevel = 'high';
    details.recommendation = 'Block request - Prototype pollution attempt detected';
    return { result: 'blocked', details };
  }

  details.threatLevel = 'low';
  details.recommendation = 'Allow - No prototype pollution patterns detected';
  return { result: 'allowed', details };
}

/**
 * Test size limits
 */
function testSizeLimits(payload: unknown, details: Record<string, unknown>): {
  result: 'blocked' | 'sanitized' | 'allowed';
  details: Record<string, unknown>;
} {
  const payloadSize = JSON.stringify(payload).length;
  const maxSize = 10 * 1024; // 10KB limit for this test

  details.payloadSize = payloadSize;
  details.maxSize = maxSize;
  details.sizeExceeded = payloadSize > maxSize;

  if (payloadSize > maxSize) {
    details.threatLevel = 'medium';
    details.recommendation = 'Block request - Size limit exceeded';
    return { result: 'blocked', details };
  }

  details.threatLevel = 'low';
  details.recommendation = 'Allow - Within size limits';
  return { result: 'allowed', details };
}

/**
 * Test rate limiting
 */
function testRateLimiting(context: SecurityTestContext, details: Record<string, unknown>): {
  result: 'blocked' | 'sanitized' | 'allowed';
  details: Record<string, unknown>;
} {
  // This is a mock test since real rate limiting is handled by middleware
  const clientIP = context.clientIP;
  details.clientIP = clientIP;
  details.rateLimitApplied = true;
  details.recommendation = 'Rate limiting is active';

  // In a real scenario, this would check actual rate limit status
  details.threatLevel = 'low';
  details.recommendation = 'Allow - Rate limiting is functioning';
  return { result: 'allowed', details };
}

/**
 * Test CSRF protection
 */
function testCSRFProtection(context: SecurityTestContext, details: Record<string, unknown>): {
  result: 'blocked' | 'sanitized' | 'allowed';
  details: Record<string, unknown>;
} {
  const headers = context.headers || {};
  const csrfToken = headers['x-csrf-token'] || headers['x-xsrf-token'];
  
  details.csrfTokenPresent = !!csrfToken;
  details.tokenFormat = csrfToken ? 'valid_format' : 'missing';

  if (!csrfToken) {
    details.threatLevel = 'high';
    details.recommendation = 'Block request - CSRF token missing';
    return { result: 'blocked', details };
  }

  details.threatLevel = 'low';
  details.recommendation = 'Allow - CSRF token present';
  return { result: 'allowed', details };
}

/**
 * Test input sanitization
 */
function testInputSanitization(payload: unknown, details: Record<string, unknown>): {
  result: 'blocked' | 'sanitized' | 'allowed';
  details: Record<string, unknown>;
} {
  const originalPayload = JSON.stringify(payload);
  
  // Simulate sanitization process
  let sanitizedPayload = originalPayload;
  const sanitizationActions = [];

  // Remove script tags
  if (/<script[^>]*>.*?<\/script>/gi.test(sanitizedPayload)) {
    sanitizedPayload = sanitizedPayload.replace(/<script[^>]*>.*?<\/script>/gi, '');
    sanitizationActions.push('removed_script_tags');
  }

  // Remove event handlers
  if (/on\w+\s*=/gi.test(sanitizedPayload)) {
    sanitizedPayload = sanitizedPayload.replace(/on\w+\s*=[^>\s]*/gi, '');
    sanitizationActions.push('removed_event_handlers');
  }

  // Escape HTML characters
  const htmlEscaped = sanitizedPayload
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;');

  if (htmlEscaped !== sanitizedPayload) {
    sanitizationActions.push('escaped_html_characters');
    sanitizedPayload = htmlEscaped;
  }

  details.originalSize = originalPayload.length;
  details.sanitizedSize = sanitizedPayload.length;
  details.sanitizationActions = sanitizationActions;
  details.wasModified = originalPayload !== sanitizedPayload;

  if (sanitizationActions.length > 0) {
    details.threatLevel = 'medium';
    details.recommendation = 'Sanitize input - Potentially dangerous content cleaned';
    return { result: 'sanitized', details };
  }

  details.threatLevel = 'low';
  details.recommendation = 'Allow - No sanitization needed';
  return { result: 'allowed', details };
}

/**
 * Calculate security score based on test results
 */
function calculateSecurityScore(testResult: SecurityTestResult): number {
  let score = 100;

  // Deduct points based on threat level
  switch (testResult.details.threatLevel) {
    case 'high':
      score -= 50;
      break;
    case 'medium':
      score -= 25;
      break;
    case 'low':
      score -= 5;
      break;
  }

  // Deduct points for detected patterns
  if (testResult.details.detectedPatterns?.length > 0) {
    score -= testResult.details.detectedPatterns.length * 10;
  }

  return Math.max(0, Math.min(100, score));
}

// GET endpoint for security health check
export async function GET(request: NextRequest) {
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'api',
    rateLimitCost: 1,
    maxRequestSize: 1024
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;

  try {
    // Return security configuration status
    const securityStatus = {
      environment: process.env.NODE_ENV,
      securityFeatures: {
        requestValidation: true,
        rateLimiting: true,
        csrfProtection: process.env.NODE_ENV === 'production',
        originValidation: process.env.NODE_ENV === 'production',
        contentTypeValidation: true,
        sizeLimits: true,
        maliciousContentDetection: true,
        auditLogging: true
      },
      testingEnabled: process.env.NODE_ENV !== 'production',
      lastUpdated: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      security: securityStatus
    });

  } catch (error) {
    logger.error('Security status check error:', error);
    return NextResponse.json(
      { error: 'Failed to get security status' },
      { status: 500 }
    );
  }
}