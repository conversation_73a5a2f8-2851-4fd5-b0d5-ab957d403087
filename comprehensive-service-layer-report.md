# Comprehensive Service Layer Refactoring Report

Generated: 2025-08-06T07:34:53.390Z

## Executive Summary

- **Total API Routes**: 156
- **Total Issues Found**: 446
- **High Priority Files**: 39
- **Files Needing Manual Review**: 16

## Issue Breakdown

- **Direct Database Queries**: 288
- **Business Logic in Routes**: 0
- **Complex Data Transformations**: 7
- **Missing Service Imports**: 151

## High Priority Files (Complexity Score > 10)

### C:/Users/<USER>/BookScribe/src/app/api/agents/generate/route.ts
- **Complexity Score**: 64
- **Issues**: 22
- **Top Issues**:
  - Line 130: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 269: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `await supabase.from('characters').delete().eq('project_id', ...`
  - Line 278: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `await supabase.from('characters').insert({`
  - Line 294: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `await supabase.from('chapters').delete().eq('project_id', pr...`
  - Line 299: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `await supabase.from('chapters').insert({`
  - ... and 17 more issues

### C:/Users/<USER>/BookScribe/src/app/api/story-bible/update/route.ts
- **Complexity Score**: 42
- **Issues**: 14
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 22: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 60: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `await supabase.from('characters').insert({`
  - Line 77: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `await supabase.from('story_bible').insert({`
  - Line 86: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `await supabase.from('story_bible').insert({`
  - ... and 9 more issues

### C:/Users/<USER>/BookScribe/src/app/api/chapters/[id]/route.ts
- **Complexity Score**: 30
- **Issues**: 10
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 49: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `.from('chapters')`
  - Line 120: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `.from('chapters')`
  - Line 152: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `.from('chapters')`
  - Line 170: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `.from('chapters')`
  - ... and 5 more issues

### C:/Users/<USER>/BookScribe/src/app/api/story-bible/bulk/route.ts
- **Complexity Score**: 27
- **Issues**: 9
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 147: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 159: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`
  - Line 172: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`
  - Line 199: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`
  - ... and 4 more issues

### C:/Users/<USER>/BookScribe/src/app/api/writing/sessions/route.ts
- **Complexity Score**: 27
- **Issues**: 9
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 46: Use AnalyticsEngine service instead of direct query to 'writing_sessions'
    - Code: `.from('writing_sessions')`
  - Line 73: Use AnalyticsEngine service instead of direct query to 'writing_sessions'
    - Code: `.from('writing_sessions')`
  - Line 134: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 146: Use AnalyticsEngine service instead of direct query to 'writing_sessions'
    - Code: `.from('writing_sessions')`
  - ... and 4 more issues

### C:/Users/<USER>/BookScribe/src/app/api/characters/route.ts
- **Complexity Score**: 24
- **Issues**: 8
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 37: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 75: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 106: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `.from('characters')`
  - Line 200: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - ... and 3 more issues

### C:/Users/<USER>/BookScribe/src/app/api/projects/[id]/chapters/route.ts
- **Complexity Score**: 24
- **Issues**: 8
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 40: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 52: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `.from('chapters')`
  - Line 171: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 183: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `.from('chapters')`
  - ... and 3 more issues

### C:/Users/<USER>/BookScribe/src/app/api/projects/[id]/voice-profile/route.ts
- **Complexity Score**: 24
- **Issues**: 8
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 24: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 95: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 122: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 143: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - ... and 3 more issues

### C:/Users/<USER>/BookScribe/src/app/api/characters/bulk/route.ts
- **Complexity Score**: 22
- **Issues**: 8
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 50: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 63: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `.from('characters')`
  - Line 79: Move complex data transformation to service layer
    - Code: `.map(c => ('id' in c ? c.id : null)) .filter((id): id is str...`
  - Line 84: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `.from('characters')`
  - ... and 3 more issues

### C:/Users/<USER>/BookScribe/src/app/api/agents/adjust-plan/route.ts
- **Complexity Score**: 21
- **Issues**: 7
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 97: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 206: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `supabase.from('projects').select('*').eq('id', projectId).si...`
  - Line 207: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `supabase.from('chapters')`
  - Line 212: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `supabase.from('chapters')`
  - ... and 2 more issues

### C:/Users/<USER>/BookScribe/src/app/api/analytics/sessions/route.ts
- **Complexity Score**: 21
- **Issues**: 7
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 53: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 92: Use AnalyticsEngine service instead of direct query to 'writing_sessions'
    - Code: `.from('writing_sessions')`
  - Line 285: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 307: Use AnalyticsEngine service instead of direct query to 'writing_sessions'
    - Code: `.from('writing_sessions')`
  - ... and 2 more issues

### C:/Users/<USER>/BookScribe/src/app/api/characters/[id]/route.ts
- **Complexity Score**: 21
- **Issues**: 7
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 54: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `.from('characters')`
  - Line 178: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `.from('characters')`
  - Line 223: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `.from('characters')`
  - Line 252: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `.from('characters')`
  - ... and 2 more issues

### C:/Users/<USER>/BookScribe/src/app/api/ai/story-bible-assistant/route.ts
- **Complexity Score**: 18
- **Issues**: 6
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 51: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 193: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `.from('characters')`
  - Line 206: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`
  - Line 221: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`
  - ... and 1 more issues

### C:/Users/<USER>/BookScribe/src/app/api/analysis/book-summary/route.ts
- **Complexity Score**: 18
- **Issues**: 6
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 165: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 176: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `.from('chapters')`
  - Line 187: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`
  - Line 304: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`
  - ... and 1 more issues

### C:/Users/<USER>/BookScribe/src/app/api/projects/[id]/characters/route.ts
- **Complexity Score**: 18
- **Issues**: 6
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 40: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 52: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `.from('characters')`
  - Line 181: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 193: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `.from('characters')`
  - ... and 1 more issues

### C:/Users/<USER>/BookScribe/src/app/api/story-bible/[id]/route.ts
- **Complexity Score**: 18
- **Issues**: 6
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 27: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`
  - Line 85: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`
  - Line 116: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`
  - Line 157: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`
  - ... and 1 more issues

### C:/Users/<USER>/BookScribe/src/app/api/analysis/arc-suggestions/route.ts
- **Complexity Score**: 15
- **Issues**: 5
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 34: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 46: Use ContentGenerator service instead of direct query to 'characters'
    - Code: `.from('characters')`
  - Line 52: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `.from('chapters')`
  - Line 57: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`

### C:/Users/<USER>/BookScribe/src/app/api/cron/generate-embeddings/route.ts
- **Complexity Score**: 15
- **Issues**: 5
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 24: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `.from('chapters')`
  - Line 56: Use ContentGenerator service instead of direct query to 'chapters'
    - Code: `.from('chapters')`
  - Line 81: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`
  - Line 103: Use ContextManager service instead of direct query to 'story_bible'
    - Code: `.from('story_bible')`

### C:/Users/<USER>/BookScribe/src/app/api/project-collaborators/route.ts
- **Complexity Score**: 15
- **Issues**: 5
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 52: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 136: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 212: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 259: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`

### C:/Users/<USER>/BookScribe/src/app/api/projects/route.ts
- **Complexity Score**: 15
- **Issues**: 5
- **Top Issues**:
  - Line 1: Import ServiceManager or specific services
    - Code: `No service imports found`
  - Line 48: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 155: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 186: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`
  - Line 288: Use ContentGenerator service instead of direct query to 'projects'
    - Code: `.from('projects')`

## Service Layer Mapping Guide

### Table to Service Mapping

| Table | Service | Import Path |
|-------|---------|-------------|
| profiles | UserService | @/lib/services/user-service |
| projects | ContentGenerator | @/lib/services/content-generator |
| chapters | ContentGenerator | @/lib/services/content-generator |
| characters | ContentGenerator | @/lib/services/content-generator |
| story_bible | ContextManager | @/lib/services/context-manager |
| story_bibles | ContextManager | @/lib/services/context-manager |
| writing_sessions | AnalyticsEngine | @/lib/services/analytics-engine |
| usage_tracking | AnalyticsEngine | @/lib/services/analytics-engine |
| collaboration_sessions | CollaborationService | @/lib/services/unified-collaboration-service |
| content_embeddings | SemanticSearch | @/lib/services/semantic-search |
| agent_logs | AIOrchestrator | @/lib/services/ai-orchestrator |

### Refactoring Examples

#### Before:
```typescript
const { data } = await supabase
  .from("projects")
  .select("*")
  .eq("user_id", userId)
```

#### After:
```typescript
const serviceManager = ServiceManager.getInstance()
const contentGenerator = await serviceManager.getContentGenerator()
const projects = await contentGenerator.getUserProjects(userId)
```

## Implementation Plan

1. **Phase 1**: Refactor high-priority files (complexity > 10)
2. **Phase 2**: Update remaining files with direct queries
3. **Phase 3**: Move business logic to services
4. **Phase 4**: Optimize data transformations
5. **Phase 5**: Add comprehensive tests

## Next Steps

1. Review high-priority files manually
2. Create missing service methods if needed
3. Run automated refactoring on simple cases
4. Test thoroughly after each refactoring
5. Update API documentation
