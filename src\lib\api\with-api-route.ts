/**
 * Standardized API Route Wrapper
 * Consolidates authentication, rate limiting, and error handling
 */

import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { applyRateLimit } from '@/lib/rate-limiter-unified'
import { handleAPIError } from '@/lib/api/error-handler'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'

export interface APIRouteOptions {
  auth?: 'user' | 'admin' | 'project' | 'series' | false
  rateLimit?: {
    type: 'ai-generation' | 'ai-analysis' | 'api-call' | 'file-upload'
    cost?: number
  }
  validateBody?: z.ZodSchema<any>
  projectIdParam?: string
  seriesIdParam?: string
}

export type APIRouteHandler<T = any> = (
  request: AuthenticatedRequest,
  context?: {
    params?: any
    body?: T
  }
) => Promise<NextResponse>

/**
 * Standardized API route wrapper that handles:
 * - Authentication (user, admin, project, series)
 * - Rate limiting
 * - Request body validation
 * - Error handling
 * - Logging
 */
export function withAPIRoute<T = any>(
  handler: APIRouteHandler<T>,
  options: APIRouteOptions = {}
) {
  return async function wrappedHandler(
    request: NextRequest,
    context?: { params?: any }
  ): Promise<NextResponse> {
    try {
      // Apply rate limiting if configured
      if (options.rateLimit) {
        const rateLimitResponse = await applyRateLimit(request, {
          type: options.rateLimit.type,
          cost: options.rateLimit.cost || 1
        })
        if (rateLimitResponse) {
          return rateLimitResponse
        }
      }

      // Handle authentication
      let user = null
      if (options.auth !== false) {
        switch (options.auth) {
          case 'admin':
            user = await UnifiedAuthService.authenticateAdmin(request)
            if (!user) {
              return NextResponse.json(
                { error: 'Admin access required' },
                { status: 403 }
              )
            }
            break

          case 'project':
            if (!context?.params || !options.projectIdParam) {
              throw new Error('Project authentication requires projectIdParam option')
            }
            const projectId = context.params[options.projectIdParam]
            user = await UnifiedAuthService.authenticateProjectAccess(request, projectId)
            if (!user) {
              return NextResponse.json(
                { error: 'Project access denied' },
                { status: 403 }
              )
            }
            break

          case 'series':
            if (!context?.params || !options.seriesIdParam) {
              throw new Error('Series authentication requires seriesIdParam option')
            }
            const seriesId = context.params[options.seriesIdParam]
            user = await UnifiedAuthService.authenticateSeriesAccess(request, seriesId)
            if (!user) {
              return NextResponse.json(
                { error: 'Series access denied' },
                { status: 403 }
              )
            }
            break

          case 'user':
          default:
            user = await UnifiedAuthService.authenticateUser(request)
            if (!user) {
              return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
              )
            }
        }
      }

      // Attach user to request
      const authenticatedRequest = request as AuthenticatedRequest
      if (user) {
        authenticatedRequest.user = user
      }

      // Parse and validate body if schema provided
      let body: T | undefined
      if (options.validateBody && request.method !== 'GET' && request.method !== 'DELETE') {
        try {
          const rawBody = await request.json()
          body = options.validateBody.parse(rawBody)
        } catch (error) {
          if (error instanceof z.ZodError) {
            return NextResponse.json(
              { 
                error: 'Invalid request data', 
                details: error.errors 
              },
              { status: 400 }
            )
          }
          throw error
        }
      }

      // Call the actual handler
      return await handler(authenticatedRequest, {
        params: context?.params,
        body
      })

    } catch (error) {
      logger.error('API route error:', {
        method: request.method,
        url: request.url,
        error
      })
      return handleAPIError(error)
    }
  }
}

/**
 * Convenience wrappers for common patterns
 */
export const withUserAuth = <T = any>(
  handler: APIRouteHandler<T>,
  options: Omit<APIRouteOptions, 'auth'> = {}
) => withAPIRoute(handler, { ...options, auth: 'user' })

export const withAdminAuth = <T = any>(
  handler: APIRouteHandler<T>,
  options: Omit<APIRouteOptions, 'auth'> = {}
) => withAPIRoute(handler, { ...options, auth: 'admin' })

export const withProjectAuth = <T = any>(
  handler: APIRouteHandler<T>,
  projectIdParam: string,
  options: Omit<APIRouteOptions, 'auth' | 'projectIdParam'> = {}
) => withAPIRoute(handler, { ...options, auth: 'project', projectIdParam })

export const withSeriesAuth = <T = any>(
  handler: APIRouteHandler<T>,
  seriesIdParam: string,
  options: Omit<APIRouteOptions, 'auth' | 'seriesIdParam'> = {}
) => withAPIRoute(handler, { ...options, auth: 'series', seriesIdParam })

export const withPublicRoute = <T = any>(
  handler: APIRouteHandler<T>,
  options: Omit<APIRouteOptions, 'auth'> = {}
) => withAPIRoute(handler, { ...options, auth: false })