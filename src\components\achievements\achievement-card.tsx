'use client'

import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Trophy } from 'lucide-react'
import { Star } from 'lucide-react'
import { Zap } from 'lucide-react'
import { Award } from 'lucide-react'
import { Lock } from 'lucide-react'
import { cn } from '@/lib/utils'
import { formatDistanceToNow } from 'date-fns'

interface Achievement {
  id: string
  code: string
  name: string
  description: string
  points: number
  tier: 'bronze' | 'silver' | 'gold' | 'platinum'
  category: string
  criteria: any
  icon?: string
  // User-specific fields
  unlocked?: boolean
  unlocked_at?: string
  progress?: number
  max_progress?: number
}

interface AchievementCardProps {
  achievement: Achievement
  onClick?: () => void
}

export function AchievementCard({ achievement, onClick }: AchievementCardProps) {
  const getTierColor = (tier: string, unlocked: boolean) => {
    if (!unlocked) return 'text-muted-foreground bg-muted/50 border-muted'
    
    switch (tier) {
      case 'bronze':
        return 'text-orange-600 bg-orange-100 border-orange-300'
      case 'silver':
        return 'text-gray-600 bg-gray-100 border-gray-300'
      case 'gold':
        return 'text-yellow-600 bg-yellow-100 border-yellow-300'
      case 'platinum':
        return 'text-purple-600 bg-purple-100 border-purple-300'
      default:
        return 'text-gray-600 bg-gray-100 border-gray-300'
    }
  }

  const getTierIcon = (tier: string) => {
    const className = cn(
      "h-8 w-8",
      achievement.unlocked ? "" : "text-muted-foreground"
    )
    
    switch (tier) {
      case 'bronze':
        return <Trophy className={className} />
      case 'silver':
        return <Award className={className} />
      case 'gold':
        return <Star className={className} />
      case 'platinum':
        return <Zap className={className} />
      default:
        return <Trophy className={className} />
    }
  }

  const progressPercentage = achievement.max_progress 
    ? Math.min((achievement.progress || 0) / achievement.max_progress * 100, 100)
    : 0

  return (
    <Card 
      className={cn(
        "relative overflow-hidden transition-all cursor-pointer",
        achievement.unlocked 
          ? "hover:shadow-lg hover:scale-105" 
          : "opacity-75 hover:opacity-100"
      )}
      onClick={onClick}
    >
      {/* Background pattern for unlocked achievements */}
      {achievement.unlocked && (
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-gradient-to-br from-primary to-primary/50" />
        </div>
      )}
      
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className={cn(
              "p-2 rounded-lg",
              getTierColor(achievement.tier, achievement.unlocked || false)
            )}>
              {achievement.unlocked ? getTierIcon(achievement.tier) : <Lock className="h-8 w-8" />}
            </div>
            <div>
              <h3 className={cn(
                "font-semibold text-sm",
                !achievement.unlocked && "text-muted-foreground"
              )}>
                {achievement.name}
              </h3>
              <p className="text-xs text-muted-foreground">
                {achievement.category}
              </p>
            </div>
          </div>
          <Badge 
            variant="secondary" 
            className={cn(
              "text-xs",
              !achievement.unlocked && "opacity-50"
            )}
          >
            {achievement.points} pts
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <p className={cn(
          "text-sm",
          !achievement.unlocked && "text-muted-foreground"
        )}>
          {achievement.description}
        </p>
        
        {/* Progress bar for locked achievements with progress */}
        {!achievement.unlocked && achievement.max_progress && (
          <div className="space-y-1">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Progress</span>
              <span>{achievement.progress || 0} / {achievement.max_progress}</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
        )}
        
        {/* Unlock date for completed achievements */}
        {achievement.unlocked && achievement.unlocked_at && (
          <div className="flex items-center justify-between pt-2 border-t">
            <Badge 
              variant="outline" 
              className={cn(
                "capitalize text-xs",
                getTierColor(achievement.tier, true)
              )}
            >
              {achievement.tier}
            </Badge>
            <span className="text-xs text-muted-foreground">
              Unlocked {formatDistanceToNow(new Date(achievement.unlocked_at), { addSuffix: true })}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}