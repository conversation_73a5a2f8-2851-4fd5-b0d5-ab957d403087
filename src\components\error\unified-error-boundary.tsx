'use client';

import React, { Component, ReactNode } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react'
import { RefreshCw } from 'lucide-react'
import { Home } from 'lucide-react'
import { FileWarning } from 'lucide-react'
import { Wifi } from 'lucide-react'
import { Zap } from 'lucide-react';
import { logError } from '@/lib/error-handling';
import { clientConfig } from '@/lib/config/client-config';

export type ErrorBoundaryType = 
  | 'general'
  | 'api'
  | 'payment'
  | 'editor'
  | 'collaboration'
  | 'ai-generation'
  | 'analytics'
  | 'component';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  type?: ErrorBoundaryType;
  componentName?: string;
  resetKeys?: Array<string | number>;
  isolate?: boolean;
  onReset?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorCount: number;
}

export class UnifiedErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: NodeJS.Timeout | null = null;
  private previousResetKeys: Array<string | number> = [];

  constructor(props: Props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorCount: 0 
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const { type = 'general', componentName, onError } = this.props;
    
    // Log error to error tracking service
    logError(error, {
      action: `${type} Error: ${componentName || 'Unknown'}`,
      metadata: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
        errorType: type,
        errorCount: this.state.errorCount + 1,
      },
    });

    // Call custom error handler if provided
    onError?.(error, errorInfo);

    // Update state with error info
    this.setState(prevState => ({
      error,
      errorInfo,
      errorCount: prevState.errorCount + 1,
    }));

    // Auto-reset after 5 seconds for non-critical errors
    if (type === 'component' && this.state.errorCount < 3) {
      this.resetTimeoutId = setTimeout(() => {
        this.handleReset();
      }, 5000);
    }
  }

  componentDidUpdate(prevProps: Props) {
    const { resetKeys = [] } = this.props;
    const hasResetKeyChanged = resetKeys.some(
      (key, index) => key !== this.previousResetKeys[index]
    );

    if (hasResetKeyChanged && this.state.hasError) {
      this.handleReset();
    }

    this.previousResetKeys = resetKeys;
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  handleReset = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
      this.resetTimeoutId = null;
    }
    
    this.props.onReset?.();
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorCount: 0 
    });
  };

  handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  getErrorContent() {
    const { type = 'general', componentName } = this.props;
    const { error } = this.state;

    switch (type) {
      case 'api':
        return {
          icon: <Wifi className="h-5 w-5 text-destructive" />,
          title: 'API Connection Error',
          description: 'Failed to connect to the server. Please check your connection.',
          showRetry: true,
        };

      case 'payment':
        return {
          icon: <AlertTriangle className="h-5 w-5 text-destructive" />,
          title: 'Payment Processing Error',
          description: 'There was an issue processing your payment. Please try again.',
          showRetry: true,
        };

      case 'editor':
        return {
          icon: <FileWarning className="h-5 w-5 text-destructive" />,
          title: 'Editor Error',
          description: 'The editor encountered an issue. Your work has been saved.',
          showRetry: true,
        };

      case 'collaboration':
        return {
          icon: <Wifi className="h-5 w-5 text-destructive" />,
          title: 'Collaboration Error',
          description: 'Lost connection to collaboration session. Attempting to reconnect...',
          showRetry: true,
        };

      case 'ai-generation':
        return {
          icon: <Zap className="h-5 w-5 text-destructive" />,
          title: 'AI Generation Error',
          description: 'Failed to generate content. Please try again or use a different prompt.',
          showRetry: true,
        };

      case 'analytics':
        return {
          icon: <AlertTriangle className="h-5 w-5 text-destructive" />,
          title: 'Analytics Error',
          description: 'Failed to load analytics data. This won\'t affect your writing.',
          showRetry: true,
        };

      case 'component':
        return {
          icon: <AlertTriangle className="h-5 w-5 text-destructive" />,
          title: componentName ? `${componentName} Error` : 'Component Error',
          description: this.state.errorCount > 1 
            ? 'This component is experiencing repeated errors. Try refreshing the page.'
            : 'A component encountered an error. It will auto-recover shortly.',
          showRetry: this.state.errorCount > 1,
        };

      default:
        return {
          icon: <AlertTriangle className="h-5 w-5 text-destructive" />,
          title: 'Something went wrong',
          description: 'An unexpected error occurred. Please try again.',
          showRetry: true,
        };
    }
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return <>{this.props.fallback}</>;
      }

      const { icon, title, description, showRetry } = this.getErrorContent();
      const { isolate } = this.props;

      // Minimal error UI for isolated/component errors
      if (isolate || this.props.type === 'component') {
        return (
          <Alert variant="destructive" className="m-2">
            <div className="flex items-center gap-2">
              {icon}
              <AlertTitle>{title}</AlertTitle>
            </div>
            <AlertDescription className="mt-2">
              {description}
              {showRetry && (
                <Button 
                  onClick={this.handleReset} 
                  variant="link" 
                  size="sm"
                  className="ml-2 p-0 h-auto"
                >
                  Try again
                </Button>
              )}
            </AlertDescription>
          </Alert>
        );
      }

      // Full error UI for page-level errors
      return (
        <div className="flex items-center justify-center min-h-[400px] p-4">
          <Card className="max-w-lg w-full">
            <CardHeader>
              <div className="flex items-center space-x-2">
                {icon}
                <CardTitle>{title}</CardTitle>
              </div>
              <CardDescription>{description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {clientConfig.isDevelopment && this.state.error && (
                <Alert variant="destructive">
                  <AlertTitle>Error Details</AlertTitle>
                  <AlertDescription className="mt-2 font-mono text-sm">
                    {this.state.error.message}
                  </AlertDescription>
                </Alert>
              )}
              
              <div className="flex gap-2">
                {showRetry && (
                  <Button onClick={this.handleReset} variant="default">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Try Again
                  </Button>
                )}
                <Button onClick={this.handleGoHome} variant="outline">
                  <Home className="mr-2 h-4 w-4" />
                  Go to Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Specialized error boundary components for convenience
export const ApiErrorBoundary: React.FC<Omit<Props, 'type'>> = (props) => (
  <UnifiedErrorBoundary {...props} type="api" />
);

export const PaymentErrorBoundary: React.FC<Omit<Props, 'type'>> = (props) => (
  <UnifiedErrorBoundary {...props} type="payment" />
);

export const EditorErrorBoundary: React.FC<Omit<Props, 'type'>> = (props) => (
  <UnifiedErrorBoundary {...props} type="editor" />
);

export const CollaborationErrorBoundary: React.FC<Omit<Props, 'type'>> = (props) => (
  <UnifiedErrorBoundary {...props} type="collaboration" />
);

export const AIGenerationErrorBoundary: React.FC<Omit<Props, 'type'>> = (props) => (
  <UnifiedErrorBoundary {...props} type="ai-generation" />
);

export const AnalyticsErrorBoundary: React.FC<Omit<Props, 'type'>> = (props) => (
  <UnifiedErrorBoundary {...props} type="analytics" />
);

export const ComponentErrorBoundary: React.FC<Omit<Props, 'type'>> = (props) => (
  <UnifiedErrorBoundary {...props} type="component" />
);

// HOC for easier usage
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  type: ErrorBoundaryType = 'component',
  componentName?: string
) {
  return function WithErrorBoundaryComponent(props: P) {
    return (
      <UnifiedErrorBoundary type={type} componentName={componentName}>
        <Component {...props} />
      </UnifiedErrorBoundary>
    );
  };
}