import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { ServiceBase, ServiceResponse } from './base-service';
import { Database } from '@/lib/db/database.types';

type Location = Database['public']['Tables']['locations']['Row'];
type LocationInsert = Database['public']['Tables']['locations']['Insert'];
type LocationUpdate = Database['public']['Tables']['locations']['Update'];

export class LocationService extends ServiceBase {
  constructor() {
    super({
      name: 'location-service',
      version: '1.0.0',
      endpoints: ['/api/projects/*/locations'],
      dependencies: [],
      healthCheck: '/api/services/location/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, { status: 'healthy' });
  }

  /**
   * Get all locations for a project
   */
  async getProjectLocations(projectId: string): Promise<ServiceResponse<Location[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data: locations, error } = await supabase
        .from('locations')
        .select(`
          *,
          parent_location:locations!parent_location_id(
            id,
            name
          ),
          series:series(
            id,
            title
          ),
          universe:universes(
            id,
            name
          )
        `)
        .eq('project_id', projectId)
        .order('name', { ascending: true });

      if (error) {
        logger.error('[LocationService] Error fetching project locations:', error);
        throw error;
      }

      return locations || [];
    });
  }

  /**
   * Get a single location by ID
   */
  async getLocation(locationId: string): Promise<ServiceResponse<Location>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('locations')
        .select(`
          *,
          parent_location:locations!parent_location_id(
            id,
            name
          ),
          series:series(
            id,
            title
          ),
          universe:universes(
            id,
            name
          )
        `)
        .eq('id', locationId)
        .single();

      if (error) {
        logger.error('[LocationService] Error fetching location:', error);
        throw error;
      }

      if (!data) {
        throw new Error('Location not found');
      }

      return data;
    });
  }

  /**
   * Create a new location
   */
  async createLocation(location: LocationInsert): Promise<ServiceResponse<Location>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('locations')
        .insert(location)
        .select(`
          *,
          parent_location:locations!parent_location_id(
            id,
            name
          )
        `)
        .single();

      if (error) {
        logger.error('[LocationService] Error creating location:', error);
        throw error;
      }

      if (!data) {
        throw new Error('Failed to create location');
      }

      return data;
    });
  }

  /**
   * Update a location
   */
  async updateLocation(
    locationId: string,
    updates: LocationUpdate
  ): Promise<ServiceResponse<Location>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('locations')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', locationId)
        .select(`
          *,
          parent_location:locations!parent_location_id(
            id,
            name
          ),
          series:series(
            id,
            title
          ),
          universe:universes(
            id,
            name
          )
        `)
        .single();

      if (error) {
        logger.error('[LocationService] Error updating location:', error);
        throw error;
      }

      if (!data) {
        throw new Error('Location not found');
      }

      return data;
    });
  }

  /**
   * Delete a location
   */
  async deleteLocation(locationId: string): Promise<ServiceResponse<{ success: boolean }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      // First check if this location has child locations
      const { data: children } = await supabase
        .from('locations')
        .select('id')
        .eq('parent_location_id', locationId)
        .limit(1);

      if (children && children.length > 0) {
        throw new Error('Cannot delete location with child locations');
      }

      const { error } = await supabase
        .from('locations')
        .delete()
        .eq('id', locationId);

      if (error) {
        logger.error('[LocationService] Error deleting location:', error);
        throw error;
      }

      return { success: true };
    });
  }

  /**
   * Get locations by type for a project
   */
  async getLocationsByType(
    projectId: string,
    locationType: string
  ): Promise<ServiceResponse<Location[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data: locations, error } = await supabase
        .from('locations')
        .select('*')
        .eq('project_id', projectId)
        .eq('location_type', locationType)
        .order('name', { ascending: true });

      if (error) {
        logger.error('[LocationService] Error fetching locations by type:', error);
        throw error;
      }

      return locations || [];
    });
  }

  /**
   * Get shareable locations for a series or universe
   */
  async getShareableLocations(
    seriesId?: string,
    universeId?: string
  ): Promise<ServiceResponse<Location[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      let query = supabase
        .from('locations')
        .select(`
          *,
          parent_location:locations!parent_location_id(
            id,
            name
          )
        `)
        .eq('is_shareable', true)
        .order('name', { ascending: true });

      if (seriesId) {
        query = query.eq('series_id', seriesId);
      } else if (universeId) {
        query = query.eq('universe_id', universeId);
      }

      const { data: locations, error } = await query;

      if (error) {
        logger.error('[LocationService] Error fetching shareable locations:', error);
        throw error;
      }

      return locations || [];
    });
  }

  /**
   * Search locations by name
   */
  async searchLocations(
    projectId: string,
    searchTerm: string
  ): Promise<ServiceResponse<Location[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data: locations, error } = await supabase
        .from('locations')
        .select('*')
        .eq('project_id', projectId)
        .ilike('name', `%${searchTerm}%`)
        .order('name', { ascending: true })
        .limit(20);

      if (error) {
        logger.error('[LocationService] Error searching locations:', error);
        throw error;
      }

      return locations || [];
    });
  }

  /**
   * Get location hierarchy (parent-child relationships)
   */
  async getLocationHierarchy(projectId: string): Promise<ServiceResponse<any[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      // Get all locations for the project
      const { data: locations, error } = await supabase
        .from('locations')
        .select('*')
        .eq('project_id', projectId)
        .order('name', { ascending: true });

      if (error) {
        logger.error('[LocationService] Error fetching location hierarchy:', error);
        throw error;
      }

      if (!locations) {
        return [];
      }

      // Build hierarchy tree
      const locationMap = new Map();
      const rootLocations: any[] = [];

      // First pass: create map and identify root locations
      for (const location of locations) {
        locationMap.set(location.id, { ...location, children: [] });
        if (!location.parent_location_id) {
          rootLocations.push(locationMap.get(location.id));
        }
      }

      // Second pass: build parent-child relationships
      for (const location of locations) {
        if (location.parent_location_id && locationMap.has(location.parent_location_id)) {
          const parent = locationMap.get(location.parent_location_id);
          const child = locationMap.get(location.id);
          parent.children.push(child);
        }
      }

      return rootLocations;
    });
  }
}

// Create and export singleton instance
export const locationService = new LocationService();