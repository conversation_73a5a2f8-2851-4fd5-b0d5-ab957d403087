import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { ServiceBase, ServiceResponse } from './base-service';
import { Database } from '@/lib/db/database.types';

type StoryBible = Database['public']['Tables']['story_bible']['Row'];
type StoryBibleInsert = Database['public']['Tables']['story_bible']['Insert'];
type StoryBibleUpdate = Database['public']['Tables']['story_bible']['Update'];

type StoryArc = Database['public']['Tables']['story_arcs']['Row'];
type StoryArcInsert = Database['public']['Tables']['story_arcs']['Insert'];

export class StoryBibleService extends ServiceBase {
  constructor() {
    super({
      name: 'story-bible-service',
      version: '1.0.0',
      endpoints: ['/api/story-bible'],
      dependencies: [],
      healthCheck: '/api/services/story-bible/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, { status: 'healthy' });
  }

  /**
   * Get all story bible entries for a project
   */
  async getProjectStoryBible(projectId: string): Promise<ServiceResponse<StoryBible[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('story_bible')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });
      
      if (error) {
        logger.error('[StoryBibleService] Error fetching story bible:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Get story bible entries with filters
   */
  async getStoryBibleEntries(
    userId: string,
    filters?: {
      project_id?: string;
      entry_type?: string;
      is_active?: boolean;
      chapter_introduced?: number;
      limit?: number;
      offset?: number;
    }
  ): Promise<ServiceResponse<{ entries: StoryBible[]; total: number }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Build query with project ownership verification
      let query = supabase
        .from('story_bible')
        .select(`
          *,
          projects!inner (
            id,
            title,
            user_id
          )
        `, { count: 'exact' })
        .eq('projects.user_id', userId);
      
      // Apply filters
      if (filters?.project_id) {
        query = query.eq('project_id', filters.project_id);
      }
      if (filters?.entry_type) {
        query = query.eq('entry_type', filters.entry_type);
      }
      if (filters?.is_active !== undefined) {
        query = query.eq('is_active', filters.is_active);
      }
      if (filters?.chapter_introduced) {
        query = query.eq('chapter_introduced', filters.chapter_introduced);
      }
      
      // Apply pagination
      const limit = filters?.limit || 50;
      const offset = filters?.offset || 0;
      
      if (filters?.limit) {
        query = query.limit(limit);
      }
      if (filters?.offset) {
        query = query.range(offset, offset + limit - 1);
      }
      
      // Order by creation date
      query = query.order('created_at', { ascending: false });
      
      const { data, error, count } = await query;
      
      if (error) {
        logger.error('[StoryBibleService] Error fetching story bible entries:', error);
        throw error;
      }
      
      // Clean up the response to remove nested project data
      const cleanEntries = data?.map(entry => {
        const { projects: _, ...storyBibleEntry } = entry;
        return storyBibleEntry as StoryBible;
      }) || [];
      
      return { entries: cleanEntries, total: count || 0 };
    });
  }

  /**
   * Get story bible entries by type
   */
  async getStoryBibleByType(projectId: string, entryType: string): Promise<ServiceResponse<StoryBible[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('story_bible')
        .select('*')
        .eq('project_id', projectId)
        .eq('entry_type', entryType)
        .order('entry_key', { ascending: true });
      
      if (error) {
        logger.error('[StoryBibleService] Error fetching story bible by type:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Create a story bible entry
   */
  async createStoryBibleEntry(entryData: StoryBibleInsert): Promise<ServiceResponse<StoryBible>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('story_bible')
        .insert({
          ...entryData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        logger.error('[StoryBibleService] Error creating story bible entry:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Failed to create story bible entry');
      }
      
      return data;
    });
  }

  /**
   * Create multiple story bible entries
   */
  async createStoryBibleEntries(entriesData: StoryBibleInsert[]): Promise<ServiceResponse<StoryBible[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const entriesWithTimestamps = entriesData.map(entry => ({
        ...entry,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));
      
      const { data, error } = await supabase
        .from('story_bible')
        .insert(entriesWithTimestamps)
        .select();
      
      if (error) {
        logger.error('[StoryBibleService] Error creating story bible entries:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Update a story bible entry
   */
  async updateStoryBibleEntry(entryId: string, updates: StoryBibleUpdate): Promise<ServiceResponse<StoryBible>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('story_bible')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', entryId)
        .select()
        .single();
      
      if (error) {
        logger.error('[StoryBibleService] Error updating story bible entry:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Story bible entry not found');
      }
      
      return data;
    });
  }

  /**
   * Delete a single story bible entry
   */
  async deleteStoryBibleEntry(entryId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from('story_bible')
        .delete()
        .eq('id', entryId);
      
      if (error) {
        logger.error('[StoryBibleService] Error deleting story bible entry:', error);
        throw error;
      }
      
      return true;
    });
  }

  /**
   * Delete all story bible entries for a project
   */
  async deleteProjectStoryBible(projectId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from('story_bible')
        .delete()
        .eq('project_id', projectId);
      
      if (error) {
        logger.error('[StoryBibleService] Error deleting project story bible:', error);
        throw error;
      }
      
      return true;
    });
  }

  /**
   * Get all story arcs for a project
   */
  async getProjectStoryArcs(projectId: string): Promise<ServiceResponse<StoryArc[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('story_arcs')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: true });
      
      if (error) {
        logger.error('[StoryBibleService] Error fetching story arcs:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Create a story arc
   */
  async createStoryArc(arcData: StoryArcInsert): Promise<ServiceResponse<StoryArc>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('story_arcs')
        .insert({
          ...arcData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        logger.error('[StoryBibleService] Error creating story arc:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Failed to create story arc');
      }
      
      return data;
    });
  }

  /**
   * Delete all story arcs for a project
   */
  async deleteProjectStoryArcs(projectId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from('story_arcs')
        .delete()
        .eq('project_id', projectId);
      
      if (error) {
        logger.error('[StoryBibleService] Error deleting project story arcs:', error);
        throw error;
      }
      
      return true;
    });
  }
}