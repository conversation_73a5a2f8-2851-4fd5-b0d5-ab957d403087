#!/usr/bin/env node
import { readdir, readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface ImportAnalysis {
  file: string;
  imports: {
    source: string;
    type: 'named' | 'default' | 'namespace' | 'side-effect';
    importedItems?: string[];
    lineNumber: number;
  }[];
  issues: string[];
}

interface BundleIssue {
  type: 'barrel-import' | 'large-library' | 'missing-tree-shaking' | 'duplicate-import' | 'dev-dependency';
  file: string;
  line: number;
  description: string;
  recommendation: string;
}

// Known large libraries that should use specific imports
const LARGE_LIBRARIES = {
  'lodash': 'Use lodash-es or specific imports like lodash/debounce',
  'moment': 'Consider using date-fns or native Date',
  '@fortawesome/free-solid-svg-icons': 'Import specific icons only',
  'react-icons': 'Use react-icons/fa, react-icons/md etc.',
  'd3': 'Import specific d3 modules like d3-scale',
  'antd': 'Use babel-plugin-import for tree-shaking',
  'material-ui': 'Import from @mui/material/Component',
};

// Libraries that should not be in production bundles
const DEV_ONLY_PACKAGES = [
  '@types/', 'jest', 'eslint', 'prettier', '@testing-library', 
  'playwright', 'webpack', 'vite', 'rollup', 'babel'
];

async function findSourceFiles(dir: string): Promise<string[]> {
  const files: string[] = [];
  
  async function walk(currentDir: string) {
    const entries = await readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        if (!['node_modules', '.next', 'dist', '.git', 'tests', 'scripts'].includes(entry.name)) {
          await walk(fullPath);
        }
      } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
        files.push(fullPath);
      }
    }
  }
  
  await walk(dir);
  return files;
}

function analyzeImports(content: string, filePath: string): ImportAnalysis {
  const lines = content.split('\n');
  const imports: ImportAnalysis['imports'] = [];
  const issues: string[] = [];
  
  // Regex patterns for different import types
  const importPatterns = [
    // Named imports: import { x, y } from 'z'
    /import\s*{\s*([^}]+)\s*}\s*from\s*['"]([^'"]+)['"]/,
    // Default import: import x from 'y'
    /import\s+(\w+)\s+from\s*['"]([^'"]+)['"]/,
    // Namespace import: import * as x from 'y'
    /import\s*\*\s*as\s+(\w+)\s+from\s*['"]([^'"]+)['"]/,
    // Side effect import: import 'x'
    /import\s*['"]([^'"]+)['"]/,
    // Type imports: import type { x } from 'y'
    /import\s+type\s*{\s*([^}]+)\s*}\s*from\s*['"]([^'"]+)['"]/,
  ];
  
  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    if (!trimmedLine.startsWith('import')) return;
    
    // Named imports
    const namedMatch = trimmedLine.match(importPatterns[0]);
    if (namedMatch) {
      const items = namedMatch[1].split(',').map(i => i.trim());
      imports.push({
        source: namedMatch[2],
        type: 'named',
        importedItems: items,
        lineNumber: index + 1
      });
      
      // Check for potential issues
      if (items.length > 10) {
        issues.push(`Line ${index + 1}: Importing ${items.length} items - consider barrel export`);
      }
      
      return;
    }
    
    // Default import
    const defaultMatch = trimmedLine.match(importPatterns[1]);
    if (defaultMatch && !trimmedLine.includes('{')) {
      imports.push({
        source: defaultMatch[2],
        type: 'default',
        importedItems: [defaultMatch[1]],
        lineNumber: index + 1
      });
      return;
    }
    
    // Namespace import
    const namespaceMatch = trimmedLine.match(importPatterns[2]);
    if (namespaceMatch) {
      imports.push({
        source: namespaceMatch[2],
        type: 'namespace',
        importedItems: [namespaceMatch[1]],
        lineNumber: index + 1
      });
      
      issues.push(`Line ${index + 1}: Namespace import may include unnecessary code`);
      return;
    }
    
    // Side effect import
    const sideEffectMatch = trimmedLine.match(importPatterns[3]);
    if (sideEffectMatch && !trimmedLine.includes('from')) {
      imports.push({
        source: sideEffectMatch[1],
        type: 'side-effect',
        lineNumber: index + 1
      });
    }
  });
  
  return { file: filePath, imports, issues };
}

function identifyBundleIssues(analyses: ImportAnalysis[]): BundleIssue[] {
  const issues: BundleIssue[] = [];
  const importCounts = new Map<string, number>();
  
  for (const analysis of analyses) {
    for (const imp of analysis.imports) {
      // Count imports to find duplicates
      const key = `${imp.source}:${imp.importedItems?.join(',')}`;
      importCounts.set(key, (importCounts.get(key) || 0) + 1);
      
      // Check for large library imports
      for (const [lib, recommendation] of Object.entries(LARGE_LIBRARIES)) {
        if (imp.source === lib || imp.source.startsWith(lib + '/')) {
          if (imp.type === 'namespace' || (imp.type === 'default' && imp.source === lib)) {
            issues.push({
              type: 'large-library',
              file: analysis.file,
              line: imp.lineNumber,
              description: `Importing entire ${lib} library`,
              recommendation
            });
          }
        }
      }
      
      // Check for dev dependencies
      for (const devPkg of DEV_ONLY_PACKAGES) {
        if (imp.source.includes(devPkg)) {
          issues.push({
            type: 'dev-dependency',
            file: analysis.file,
            line: imp.lineNumber,
            description: `Importing dev dependency: ${imp.source}`,
            recommendation: 'Remove from production code or use dynamic import'
          });
        }
      }
      
      // Check for potential barrel imports
      if (imp.source.endsWith('/index') || 
          (imp.importedItems && imp.importedItems.length > 5 && !imp.source.includes('/'))) {
        issues.push({
          type: 'barrel-import',
          file: analysis.file,
          line: imp.lineNumber,
          description: `Possible barrel import from ${imp.source}`,
          recommendation: 'Import specific modules to reduce bundle size'
        });
      }
    }
  }
  
  // Check for duplicate imports
  for (const [key, count] of importCounts.entries()) {
    if (count > 5) {
      const [source] = key.split(':');
      issues.push({
        type: 'duplicate-import',
        file: 'Multiple files',
        line: 0,
        description: `${source} imported ${count} times`,
        recommendation: 'Consider creating a shared export'
      });
    }
  }
  
  return issues;
}

async function generateReport(issues: BundleIssue[]): Promise<void> {
  const reportPath = join(__dirname, '..', 'bundle-optimization-report.md');
  
  let report = '# Bundle Optimization Report\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  report += `Total issues found: ${issues.length}\n\n`;
  
  // Group by issue type
  const byType = issues.reduce((acc, issue) => {
    if (!acc[issue.type]) acc[issue.type] = [];
    acc[issue.type].push(issue);
    return acc;
  }, {} as Record<string, BundleIssue[]>);
  
  // Summary
  report += '## Summary\n\n';
  for (const [type, typeIssues] of Object.entries(byType)) {
    report += `- **${type}**: ${typeIssues.length} issues\n`;
  }
  report += '\n';
  
  // Detailed issues
  for (const [type, typeIssues] of Object.entries(byType)) {
    report += `## ${type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}\n\n`;
    
    typeIssues.slice(0, 10).forEach(issue => {
      report += `### ${issue.file.replace(/\\/g, '/')}:${issue.line}\n`;
      report += `- **Issue**: ${issue.description}\n`;
      report += `- **Fix**: ${issue.recommendation}\n\n`;
    });
    
    if (typeIssues.length > 10) {
      report += `... and ${typeIssues.length - 10} more\n\n`;
    }
  }
  
  // Optimization tips
  report += '## Optimization Tips\n\n';
  report += '1. **Use dynamic imports** for components only needed on specific routes\n';
  report += '2. **Implement code splitting** with Next.js dynamic imports\n';
  report += '3. **Tree-shake unused exports** with proper webpack configuration\n';
  report += '4. **Use production builds** of libraries (react.production.min.js)\n';
  report += '5. **Analyze with @next/bundle-analyzer** for detailed insights\n';
  
  await writeFile(reportPath, report);
  console.log(`Report saved to: ${reportPath}`);
}

async function main() {
  console.log('🔍 Analyzing imports for bundle optimization...\n');
  
  const srcDir = join(__dirname, '..', 'src');
  const files = await findSourceFiles(srcDir);
  
  console.log(`Found ${files.length} source files\n`);
  
  const analyses: ImportAnalysis[] = [];
  
  // Analyze each file
  for (const file of files) {
    const content = await readFile(file, 'utf-8');
    const analysis = analyzeImports(content, file);
    if (analysis.imports.length > 0) {
      analyses.push(analysis);
    }
  }
  
  console.log(`Analyzed ${analyses.length} files with imports\n`);
  
  // Identify issues
  const issues = identifyBundleIssues(analyses);
  
  console.log(`\n📊 Issues found:`);
  const typeCounts = issues.reduce((acc, issue) => {
    acc[issue.type] = (acc[issue.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  for (const [type, count] of Object.entries(typeCounts)) {
    console.log(`  - ${type}: ${count}`);
  }
  
  if (issues.length > 0) {
    await generateReport(issues);
  }
}

// Run the script
main().catch(console.error);