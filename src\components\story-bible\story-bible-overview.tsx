'use client'

import React, { useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Users } from 'lucide-react'
import { Map } from 'lucide-react'
import { Clock } from 'lucide-react'
import { Lightbulb } from 'lucide-react'
import { TrendingUp } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { CheckCircle } from 'lucide-react'
import { BookOpen } from 'lucide-react'
import { Target } from 'lucide-react'
import { Zap } from 'lucide-react'
import { Crown } from 'lucide-react'
import { Shield } from 'lucide-react'
import { Heart } from 'lucide-react'

interface Character {
  id: string
  name: string
  role: string
  description: string
  backstory: string
  personality: Record<string, unknown>
  relationships: Array<Record<string, unknown>>
}

interface TimelineEvent {
  id?: string
  event: string
  chapter: number
  importance?: 'high' | 'medium' | 'low'
}

interface PlotThread {
  id: string
  description: string
  status: 'active' | 'resolved' | 'paused'
  priority?: 'high' | 'medium' | 'low'
}

interface StoryBibleOverviewProps {
  storyBible: {
    characters: Character[]
    worldRules: Record<string, string>
    timeline: TimelineEvent[]
    plotThreads: PlotThread[]
  }
}

export function StoryBibleOverview({ storyBible }: StoryBibleOverviewProps) {
  const analysis = useMemo(() => {
    // Character analysis
    const charactersByRole = storyBible.characters.reduce((acc, char) => {
      acc[char.role] = (acc[char.role] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const protagonists = storyBible.characters.filter(c => c.role === 'protagonist').length
    const antagonists = storyBible.characters.filter(c => c.role === 'antagonist').length
    const supportingChars = storyBible.characters.filter(c => c.role === 'supporting').length

    // Plot analysis
    const activePlots = storyBible.plotThreads.filter(p => p.status === 'active').length
    const resolvedPlots = storyBible.plotThreads.filter(p => p.status === 'resolved').length
    const pausedPlots = storyBible.plotThreads.filter(p => p.status === 'paused').length
    const plotCompletion = storyBible.plotThreads.length > 0 
      ? Math.round((resolvedPlots / storyBible.plotThreads.length) * 100)
      : 0

    // Timeline analysis
    const timelineSpan = storyBible.timeline.length > 0
      ? Math.max(...storyBible.timeline.map(e => e.chapter)) - Math.min(...storyBible.timeline.map(e => e.chapter)) + 1
      : 0
    
    const highPriorityEvents = storyBible.timeline.filter(e => e.importance === 'high').length

    // World building analysis
    const worldRuleCategories = Object.keys(storyBible.worldRules).reduce((acc, key) => {
      const category = key.includes('magic') ? 'magic' :
                     key.includes('tech') ? 'technology' :
                     key.includes('society') || key.includes('culture') ? 'society' :
                     key.includes('geo') || key.includes('location') ? 'geography' : 'other'
      acc[category] = (acc[category] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Overall completeness score
    const completenessScore = Math.round(
      ((storyBible.characters.length > 0 ? 25 : 0) +
       (Object.keys(storyBible.worldRules).length > 0 ? 25 : 0) +
       (storyBible.timeline.length > 0 ? 25 : 0) +
       (storyBible.plotThreads.length > 0 ? 25 : 0))
    )

    return {
      characters: {
        total: storyBible.characters.length,
        byRole: charactersByRole,
        protagonists,
        antagonists,
        supporting: supportingChars
      },
      plot: {
        total: storyBible.plotThreads.length,
        active: activePlots,
        resolved: resolvedPlots,
        paused: pausedPlots,
        completion: plotCompletion
      },
      timeline: {
        total: storyBible.timeline.length,
        span: timelineSpan,
        highPriority: highPriorityEvents
      },
      world: {
        total: Object.keys(storyBible.worldRules).length,
        categories: worldRuleCategories
      },
      completeness: completenessScore
    }
  }, [storyBible])

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'protagonist': return Crown
      case 'antagonist': return Shield
      case 'love_interest': return Heart
      default: return Users
    }
  }

  const getCompletionColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-blue-600'
    if (score >= 40) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getHealthScore = () => {
    let score = 0
    let maxScore = 0

    // Character diversity (max 20 points)
    maxScore += 20
    if (analysis.characters.protagonists > 0) score += 5
    if (analysis.characters.antagonists > 0) score += 5
    if (analysis.characters.supporting > 0) score += 5
    if (analysis.characters.total >= 5) score += 5

    // Plot complexity (max 20 points)
    maxScore += 20
    if (analysis.plot.active > 0) score += 8
    if (analysis.plot.total >= 3) score += 6
    if (analysis.plot.resolved > 0) score += 6

    // Timeline depth (max 20 points)
    maxScore += 20
    if (analysis.timeline.total > 0) score += 10
    if (analysis.timeline.span >= 5) score += 5
    if (analysis.timeline.highPriority > 0) score += 5

    // World building (max 20 points)
    maxScore += 20
    if (analysis.world.total > 0) score += 10
    if (Object.keys(analysis.world.categories).length >= 2) score += 10

    // Balance (max 20 points)
    maxScore += 20
    const sections = [
      analysis.characters.total > 0,
      analysis.plot.total > 0,
      analysis.timeline.total > 0,
      analysis.world.total > 0
    ].filter(Boolean).length
    score += sections * 5

    return Math.round((score / maxScore) * 100)
  }

  const healthScore = getHealthScore()

  return (
    <div className="space-y-6">
      {/* Header Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completeness</p>
                <p className={`text-2xl font-bold ${getCompletionColor(analysis.completeness)}`}>
                  {analysis.completeness}%
                </p>
              </div>
              <CheckCircle className={`h-8 w-8 ${getCompletionColor(analysis.completeness)}`} />
            </div>
            <Progress value={analysis.completeness} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Story Health</p>
                <p className={`text-2xl font-bold ${getCompletionColor(healthScore)}`}>
                  {healthScore}%
                </p>
              </div>
              <TrendingUp className={`h-8 w-8 ${getCompletionColor(healthScore)}`} />
            </div>
            <Progress value={healthScore} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Plots</p>
                <p className="text-2xl font-bold">{analysis.plot.active}</p>
              </div>
              <Zap className="h-8 w-8 text-yellow-600" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              {analysis.plot.total} total threads
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Timeline Span</p>
                <p className="text-2xl font-bold">{analysis.timeline.span}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              {analysis.timeline.total} events
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Character Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Character Analysis
            </CardTitle>
            <CardDescription>
              Overview of your character roster and roles
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-1">
                <Crown className="h-6 w-6 mx-auto text-yellow-600" />
                <p className="text-2xl font-bold">{analysis.characters.protagonists}</p>
                <p className="text-xs text-muted-foreground">Protagonists</p>
              </div>
              <div className="space-y-1">
                <Shield className="h-6 w-6 mx-auto text-red-600" />
                <p className="text-2xl font-bold">{analysis.characters.antagonists}</p>
                <p className="text-xs text-muted-foreground">Antagonists</p>
              </div>
              <div className="space-y-1">
                <Users className="h-6 w-6 mx-auto text-blue-600" />
                <p className="text-2xl font-bold">{analysis.characters.supporting}</p>
                <p className="text-xs text-muted-foreground">Supporting</p>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Role Distribution</h4>
              {Object.entries(analysis.characters.byRole).map(([role, count]) => (
                <div key={role} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {React.createElement(getRoleIcon(role), { className: "h-4 w-4" })}
                    <span className="text-sm capitalize">{role.replace('_', ' ')}</span>
                  </div>
                  <Badge variant="outline">{count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Plot Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              Plot Analysis
            </CardTitle>
            <CardDescription>
              Status and progression of your plot threads
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-1">
                <div className="h-6 w-6 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <p className="text-2xl font-bold">{analysis.plot.active}</p>
                <p className="text-xs text-muted-foreground">Active</p>
              </div>
              <div className="space-y-1">
                <div className="h-6 w-6 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                  <Target className="h-4 w-4 text-blue-600" />
                </div>
                <p className="text-2xl font-bold">{analysis.plot.resolved}</p>
                <p className="text-xs text-muted-foreground">Resolved</p>
              </div>
              <div className="space-y-1">
                <div className="h-6 w-6 mx-auto bg-yellow-100 rounded-full flex items-center justify-center">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                </div>
                <p className="text-2xl font-bold">{analysis.plot.paused}</p>
                <p className="text-xs text-muted-foreground">Paused</p>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Plot Completion</span>
                <span className="text-sm text-muted-foreground">{analysis.plot.completion}%</span>
              </div>
              <Progress value={analysis.plot.completion} />
            </div>

            {analysis.plot.active === 0 && analysis.plot.total > 0 && (
              <div className="flex items-center gap-2 p-2 bg-yellow-50 rounded-lg border border-yellow-200">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">No active plot threads</span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* World Building Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Map className="h-5 w-5" />
              World Building
            </CardTitle>
            <CardDescription>
              Categories and depth of your world rules
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <p className="text-3xl font-bold">{analysis.world.total}</p>
              <p className="text-sm text-muted-foreground">Total Rules</p>
            </div>

            <Separator />

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Categories</h4>
              {Object.entries(analysis.world.categories).map(([category, count]) => (
                <div key={category} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{category}</span>
                  <Badge variant="outline">{count}</Badge>
                </div>
              ))}
              {Object.keys(analysis.world.categories).length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-2">
                  No world rules defined yet
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Timeline Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Timeline Analysis
            </CardTitle>
            <CardDescription>
              Story chronology and key events
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div className="space-y-1">
                <p className="text-2xl font-bold">{analysis.timeline.total}</p>
                <p className="text-xs text-muted-foreground">Total Events</p>
              </div>
              <div className="space-y-1">
                <p className="text-2xl font-bold">{analysis.timeline.highPriority}</p>
                <p className="text-xs text-muted-foreground">High Priority</p>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Chapter Span</span>
                <Badge variant="outline">{analysis.timeline.span} chapters</Badge>
              </div>
              
              {analysis.timeline.total === 0 && (
                <div className="flex items-center gap-2 p-2 bg-blue-50 rounded-lg border border-blue-200">
                  <BookOpen className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-blue-800">No timeline events yet</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Recommendations
          </CardTitle>
          <CardDescription>
            Suggestions to improve your story bible
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            {analysis.characters.total === 0 && (
              <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Add Characters</p>
                  <p className="text-xs text-yellow-700">Start by defining your main characters and their roles in the story.</p>
                </div>
              </div>
            )}

            {analysis.characters.protagonists === 0 && analysis.characters.total > 0 && (
              <div className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
                <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-orange-800">Define Protagonist</p>
                  <p className="text-xs text-orange-700">Consider marking one of your characters as the main protagonist.</p>
                </div>
              </div>
            )}

            {analysis.plot.active === 0 && analysis.plot.total > 0 && (
              <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-red-800">Activate Plot Threads</p>
                  <p className="text-xs text-red-700">You have plot threads but none are marked as active. Consider which plots are currently driving your story.</p>
                </div>
              </div>
            )}

            {analysis.world.total === 0 && (
              <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <BookOpen className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-blue-800">Build Your World</p>
                  <p className="text-xs text-blue-700">Define the rules and systems that govern your story world.</p>
                </div>
              </div>
            )}

            {analysis.timeline.total === 0 && (
              <div className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg border border-purple-200">
                <Clock className="h-5 w-5 text-purple-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-purple-800">Create Timeline</p>
                  <p className="text-xs text-purple-700">Map out key events in your story's chronology.</p>
                </div>
              </div>
            )}

            {analysis.completeness >= 80 && (
              <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-green-800">Well Done!</p>
                  <p className="text-xs text-green-700">Your story bible is comprehensive and well-structured.</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}