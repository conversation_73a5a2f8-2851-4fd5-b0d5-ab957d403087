#!/usr/bin/env node
import { readdir, readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface ClientPatternIssue {
  file: string;
  line: number;
  issue: string;
  pattern: string;
  recommendation: string;
}

async function findSourceFiles(dir: string): Promise<string[]> {
  const files: string[] = [];
  
  async function walk(currentDir: string) {
    const entries = await readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        if (!['node_modules', '.next', 'dist', '.git'].includes(entry.name)) {
          await walk(fullPath);
        }
      } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
        files.push(fullPath);
      }
    }
  }
  
  await walk(dir);
  return files;
}

async function analyzeFile(filePath: string): Promise<ClientPatternIssue[]> {
  const content = await readFile(filePath, 'utf-8');
  const lines = content.split('\n');
  const issues: ClientPatternIssue[] = [];
  
  // Check if this is an API route
  const isAPIRoute = filePath.includes('/api/') && filePath.endsWith('route.ts');
  const isServerComponent = filePath.includes('/app/') && !filePath.includes('/api/') && !content.includes("'use client'");
  const isClientComponent = content.includes("'use client'");
  
  lines.forEach((line, index) => {
    // Check for incorrect patterns
    
    // 1. Using createClient directly from @supabase
    if (line.includes("from '@supabase/supabase-js'") && line.includes('createClient')) {
      issues.push({
        file: filePath,
        line: index + 1,
        issue: 'Importing createClient from @supabase/supabase-js directly',
        pattern: line.trim(),
        recommendation: "Use: import { createTypedServerClient } from '@/lib/supabase'"
      });
    }
    
    // 2. Using non-typed clients
    if (line.includes('createClient(') && !line.includes('createTypedServerClient') && !line.includes('createTypedBrowserClient')) {
      issues.push({
        file: filePath,
        line: index + 1,
        issue: 'Using non-typed createClient',
        pattern: line.trim(),
        recommendation: isAPIRoute ? 'Use: createTypedServerClient()' : 'Use: createTypedBrowserClient()'
      });
    }
    
    // 3. Using createServerClient in browser context
    if (isClientComponent && line.includes('createServerClient')) {
      issues.push({
        file: filePath,
        line: index + 1,
        issue: 'Using createServerClient in client component',
        pattern: line.trim(),
        recommendation: 'Use: createTypedBrowserClient() or getBrowserClient()'
      });
    }
    
    // 4. Using createBrowserClient in server context
    if ((isAPIRoute || isServerComponent) && line.includes('createBrowserClient')) {
      issues.push({
        file: filePath,
        line: index + 1,
        issue: 'Using createBrowserClient in server context',
        pattern: line.trim(),
        recommendation: 'Use: createTypedServerClient()'
      });
    }
    
    // 5. Multiple client instantiations in client components
    if (isClientComponent && line.includes('createTypedBrowserClient()') && !line.includes('getBrowserClient')) {
      const createCount = (content.match(/createTypedBrowserClient\(\)/g) || []).length;
      if (createCount > 1) {
        issues.push({
          file: filePath,
          line: index + 1,
          issue: 'Multiple browser client instantiations',
          pattern: line.trim(),
          recommendation: 'Use: getBrowserClient() for singleton pattern'
        });
      }
    }
    
    // 6. Wrong import paths
    if (line.includes("from '@/lib/supabase/client'") || 
        line.includes("from '@/lib/supabase/server'") ||
        line.includes("from '@/lib/supabase/supabase-client'")) {
      issues.push({
        file: filePath,
        line: index + 1,
        issue: 'Using old import path',
        pattern: line.trim(),
        recommendation: "Use: import { createTypedServerClient, createTypedBrowserClient, getBrowserClient } from '@/lib/supabase'"
      });
    }
  });
  
  return issues;
}

async function generateReport(issues: ClientPatternIssue[]): Promise<void> {
  const reportPath = join(__dirname, '..', 'supabase-client-issues-report.md');
  
  let report = '# Supabase Client Pattern Issues Report\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  report += `Total issues found: ${issues.length}\n\n`;
  
  // Group by issue type
  const byType = issues.reduce((acc, issue) => {
    if (!acc[issue.issue]) acc[issue.issue] = [];
    acc[issue.issue].push(issue);
    return acc;
  }, {} as Record<string, ClientPatternIssue[]>);
  
  report += '## Issues by Type\n\n';
  for (const [issueType, typeIssues] of Object.entries(byType)) {
    report += `### ${issueType} (${typeIssues.length} occurrences)\n\n`;
    
    typeIssues.slice(0, 5).forEach(issue => {
      report += `- **${issue.file.replace(/\\/g, '/')}:${issue.line}**\n`;
      report += `  - Pattern: \`${issue.pattern}\`\n`;
      report += `  - Fix: ${issue.recommendation}\n\n`;
    });
    
    if (typeIssues.length > 5) {
      report += `... and ${typeIssues.length - 5} more\n\n`;
    }
  }
  
  // Add usage guidelines
  report += '\n## Correct Usage Patterns\n\n';
  report += '### API Routes\n';
  report += '```typescript\n';
  report += "import { createTypedServerClient } from '@/lib/supabase'\n\n";
  report += 'export async function GET(request: NextRequest) {\n';
  report += '  const supabase = await createTypedServerClient()\n';
  report += '  // ...\n';
  report += '}\n';
  report += '```\n\n';
  
  report += '### Client Components\n';
  report += '```typescript\n';
  report += "'use client'\n";
  report += "import { getBrowserClient } from '@/lib/supabase'\n\n";
  report += 'export function Component() {\n';
  report += '  const supabase = getBrowserClient()\n';
  report += '  // ...\n';
  report += '}\n';
  report += '```\n\n';
  
  report += '### Server Components\n';
  report += '```typescript\n';
  report += "import { createTypedServerClient } from '@/lib/supabase'\n\n";
  report += 'export async function Component() {\n';
  report += '  const supabase = await createTypedServerClient()\n';
  report += '  // ...\n';
  report += '}\n';
  report += '```\n';
  
  await writeFile(reportPath, report);
  console.log(`Report generated: ${reportPath}`);
}

async function main() {
  console.log('🔍 Scanning for Supabase client pattern issues...\n');
  
  const srcDir = join(__dirname, '..', 'src');
  const files = await findSourceFiles(srcDir);
  
  console.log(`Found ${files.length} source files\n`);
  
  const allIssues: ClientPatternIssue[] = [];
  let filesWithIssues = 0;
  
  for (const file of files) {
    const issues = await analyzeFile(file);
    if (issues.length > 0) {
      filesWithIssues++;
      allIssues.push(...issues);
      
      // Only show first few files to avoid spam
      if (filesWithIssues <= 10) {
        console.log(`❌ ${file.replace(/\\/g, '/')}: ${issues.length} issues`);
      }
    }
  }
  
  if (filesWithIssues > 10) {
    console.log(`... and ${filesWithIssues - 10} more files with issues`);
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`- Total files scanned: ${files.length}`);
  console.log(`- Files with issues: ${filesWithIssues}`);
  console.log(`- Total issues found: ${allIssues.length}`);
  
  if (allIssues.length > 0) {
    await generateReport(allIssues);
    
    // Show most common issues
    const issueTypes = allIssues.reduce((acc, issue) => {
      acc[issue.issue] = (acc[issue.issue] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    console.log('\n🔝 Most common issues:');
    Object.entries(issueTypes)
      .sort(([, a], [, b]) => b - a)
      .forEach(([issue, count]) => {
        console.log(`  - ${issue}: ${count} occurrences`);
      });
  }
}

// Run the script
main().catch(console.error);