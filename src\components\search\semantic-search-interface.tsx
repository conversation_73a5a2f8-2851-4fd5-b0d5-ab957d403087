'use client'

import { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Label } from '@/components/ui/label'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Sparkles } from 'lucide-react'
import { Search } from 'lucide-react'
import { Brain } from 'lucide-react'
import { Loader2 } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { Zap } from 'lucide-react'
import { SlidersHorizontal } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useDebounce } from '@/hooks/use-debounce'
import { SearchResults } from './search-results'
import type { SearchResult } from './content-search-interface'
import { cn } from '@/lib/utils'

interface SemanticSearchInterfaceProps {
  projectId: string
  onResultSelect?: (result: SearchResult) => void
  className?: string
}

export function SemanticSearchInterface({ 
  projectId, 
  onResultSelect,
  className 
}: SemanticSearchInterfaceProps) {
  const { toast } = useToast()
  const [query, setQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [threshold, setThreshold] = useState(0.7)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [hasEmbeddings, setHasEmbeddings] = useState<boolean | null>(null)
  
  const debouncedQuery = useDebounce(query, 500)

  // Check if embeddings are available
  const checkEmbeddings = useCallback(async () => {
    try {
      const response = await fetch(`/api/search/index?projectId=${projectId}`)
      if (response.ok) {
        const data = await response.json()
        setHasEmbeddings(data.data.indexedContent > 0)
      }
    } catch (error) {
      console.error('Failed to check embeddings:', error)
    }
  }, [projectId])

  // Perform semantic search
  const performSearch = useCallback(async () => {
    if (!debouncedQuery.trim()) {
      setResults([])
      return
    }

    setIsSearching(true)
    try {
      const response = await fetch('/api/search/semantic', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: debouncedQuery,
          projectId,
          threshold,
          limit: 20
        })
      })

      if (!response.ok) throw new Error('Search failed')

      const data = await response.json()
      
      // Transform results to match SearchResult interface
      const transformedResults: SearchResult[] = (data.data.results || []).map((item: any) => ({
        id: item.id,
        type: item.type,
        title: item.title,
        content: item.content,
        excerpt: item.excerpt,
        relevanceScore: Math.round(item.similarity * 100),
        highlights: [],
        metadata: item.metadata
      }))

      setResults(transformedResults)
    } catch (error) {
      toast({
        title: 'Search Error',
        description: 'Failed to perform semantic search. Please try again.',
        variant: 'destructive'
      })
      console.error('Semantic search error:', error)
    } finally {
      setIsSearching(false)
    }
  }, [debouncedQuery, projectId, threshold, toast])

  // Generate embeddings for project
  const generateEmbeddings = async () => {
    try {
      const response = await fetch(`/api/search/semantic?projectId=${projectId}`)
      if (!response.ok) throw new Error('Failed to start embedding generation')

      toast({
        title: 'Embeddings Generation Started',
        description: 'This may take a few minutes. You can continue working while this processes.'
      })

      // Recheck embeddings status after a delay
      setTimeout(checkEmbeddings, 5000)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to start embedding generation',
        variant: 'destructive'
      })
    }
  }

  // Initial embeddings check
  useState(() => {
    checkEmbeddings()
  })

  // Perform search when query or threshold changes
  useState(() => {
    if (hasEmbeddings) {
      performSearch()
    }
  }, [debouncedQuery, threshold, hasEmbeddings])

  if (hasEmbeddings === false) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            Semantic Search
          </CardTitle>
          <CardDescription>
            AI-powered search that understands meaning and context
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center py-8">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
              <Sparkles className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="font-semibold mb-2">Enable Semantic Search</h3>
            <p className="text-sm text-muted-foreground mb-4 max-w-sm mx-auto">
              Generate AI embeddings to enable powerful semantic search that understands the meaning of your content.
            </p>
            <Button onClick={generateEmbeddings}>
              <Zap className="w-4 h-4 mr-2" />
              Generate Embeddings
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Brain className="w-5 h-5" />
              Semantic Search
            </CardTitle>
            <CardDescription>
              Find content by meaning, not just keywords
            </CardDescription>
          </div>
          <Badge variant="secondary" className="gap-1">
            <Sparkles className="w-3 h-3" />
            AI Powered
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <Tabs defaultValue="search" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="search">Search</TabsTrigger>
            <TabsTrigger value="similar">Find Similar</TabsTrigger>
          </TabsList>
          
          <TabsContent value="search" className="space-y-4">
            {/* Search Input */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Describe what you're looking for..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Advanced Options */}
            <div className="space-y-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="w-full justify-start"
              >
                <SlidersHorizontal className="w-4 h-4 mr-2" />
                Advanced Options
              </Button>
              
              {showAdvanced && (
                <div className="p-4 border rounded-lg space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="threshold">Similarity Threshold</Label>
                      <span className="text-sm text-muted-foreground">
                        {Math.round(threshold * 100)}%
                      </span>
                    </div>
                    <Slider
                      id="threshold"
                      min={0}
                      max={1}
                      step={0.05}
                      value={[threshold]}
                      onValueChange={(values) => setThreshold(values[0])}
                      className="w-full"
                    />
                    <p className="text-xs text-muted-foreground">
                      Lower values return more results, higher values return only very similar content
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Results */}
            {isSearching ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
              </div>
            ) : results.length > 0 ? (
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Found {results.length} semantically similar results
                </p>
                <SearchResults
                  results={results}
                  onResultClick={onResultSelect}
                  viewMode="detailed"
                />
              </div>
            ) : query ? (
              <div className="text-center py-8">
                <AlertCircle className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">
                  No semantically similar content found
                </p>
              </div>
            ) : null}
          </TabsContent>
          
          <TabsContent value="similar" className="space-y-4">
            <div className="text-center py-8 text-muted-foreground">
              <p className="text-sm">
                Select any content in your project to find similar items
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}