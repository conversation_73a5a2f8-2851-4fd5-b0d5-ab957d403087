// Service Layer Usage Guide for API Routes
// Follow these patterns for proper service layer usage

import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { ServiceManager } from '@/lib/services/service-manager'
import { handleAPIError, ValidationError } from '@/lib/api/error-handler'
import { z } from 'zod'

// ============================================
// Correct Pattern: Using Service Layer
// ============================================

export async function POST(request: NextRequest) {
  return UnifiedAuthService.withAuth(async (req) => {
    try {
      const user = req.user!
      const body = await req.json()
      
      // Validate input
      const schema = z.object({
        title: z.string().min(1),
        content: z.string(),
      })
      const validated = schema.parse(body)
      
      // Use service layer for business logic
      const serviceManager = ServiceManager.getInstance()
      const contentGenerator = await serviceManager.getContentGenerator()
      
      const result = await contentGenerator.generateContent({
        userId: user.id,
        ...validated
      })
      
      return NextResponse.json({ success: true, data: result })
    } catch (error) {
      return handleAPIError(error as Error)
    }
  })(request)
}

// ============================================
// Service Layer Examples
// ============================================

// 1. Project Management
export async function handleProjectOperation(userId: string, projectId: string) {
  const serviceManager = ServiceManager.getInstance()
  const orchestrator = await serviceManager.getAIOrchestrator()
  
  // Let service handle all business logic
  return orchestrator.orchestrateGeneration({
    projectId,
    userId,
    mode: 'full'
  })
}

// 2. Analytics
export async function getAnalytics(userId: string, dateRange: any) {
  const serviceManager = ServiceManager.getInstance()
  const analytics = await serviceManager.getAnalyticsEngine()
  
  return analytics.generateReport({
    userId,
    dateRange,
    metrics: ['productivity', 'quality', 'progress']
  })
}

// 3. Search Operations
export async function performSearch(query: string, filters: any) {
  const serviceManager = ServiceManager.getInstance()
  const search = await serviceManager.getSemanticSearch()
  
  return search.search({
    query,
    filters,
    limit: 20
  })
}

// ============================================
// What NOT to do in API Routes
// ============================================

// ❌ DON'T: Direct database queries
// const { data } = await supabase.from('projects').select('*')

// ❌ DON'T: Business logic in routes
// const wordCount = content.split(' ').length
// const readingTime = Math.ceil(wordCount / 200)

// ❌ DON'T: Complex data transformations
// const formatted = data.map(...).filter(...).reduce(...)

// ✅ DO: Use services for all of the above
// const result = await service.processData(input)

// ============================================
// Available Services
// ============================================

/*
ServiceManager provides access to:
- getAIOrchestrator(): AI agent coordination
- getContentGenerator(): Content creation
- getContextManager(): Context and memory management
- getAnalyticsEngine(): Analytics and reporting
- getCollaborationHub(): Real-time collaboration
- getSemanticSearch(): Search functionality
- getMailerooEmailService(): Email operations

Each service encapsulates:
- Business logic
- Database operations
- Data transformations
- Error handling
- Caching strategies
*/
