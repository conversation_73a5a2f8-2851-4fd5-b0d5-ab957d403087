#!/usr/bin/env node
import { readdir, readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface AuthPatternIssue {
  file: string;
  line: number;
  issue: string;
  pattern: string;
  recommendation: string;
}

const AUTH_PATTERNS = {
  // Old patterns to replace
  OLD_PATTERNS: [
    // Direct supabase auth.getUser
    /const\s*{\s*data:\s*{\s*user\s*}\s*.*?}\s*=\s*await\s+supabase\.auth\.getUser\(\)/g,
    // Using authenticateUser() without UnifiedAuthService
    /const\s+authResult\s*=\s*await\s+authenticateUser\(/g,
    // Old createClient patterns
    /import\s*{\s*createClient\s*}\s*from\s*['"]@supabase\/auth-helpers-nextjs['"]/g,
    // Direct JWT verification
    /jwt\.verify\(/g,
    // Manual cookie parsing for auth
    /cookies\(\)\.get\(['"]supabase-auth-token['"]\)/g,
  ],
  
  // Correct patterns to use
  CORRECT_PATTERNS: {
    userAuth: `const user = await UnifiedAuthService.authenticateUser(request);\n    if (!user) {\n      return handleAPIError(new AuthenticationError());\n    }`,
    projectAuth: `return UnifiedAuthService.withProjectAccess(async (req) => {\n      const user = req.user!;\n      const projectId = params.id;\n      // Route logic here\n    })(request, { params });`,
    adminAuth: `return UnifiedAuthService.withAdmin(async (req) => {\n      const admin = req.user!;\n      // Admin logic here\n    })(request);`,
  }
};

async function findAPIRoutes(dir: string): Promise<string[]> {
  const files: string[] = [];
  
  async function walk(currentDir: string) {
    const entries = await readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        // Skip node_modules and other non-source directories
        if (!['node_modules', '.next', 'dist', '.git'].includes(entry.name)) {
          await walk(fullPath);
        }
      } else if (entry.isFile() && entry.name === 'route.ts') {
        // Found an API route file
        files.push(fullPath);
      }
    }
  }
  
  await walk(dir);
  return files;
}

async function analyzeFile(filePath: string): Promise<AuthPatternIssue[]> {
  const content = await readFile(filePath, 'utf-8');
  const lines = content.split('\n');
  const issues: AuthPatternIssue[] = [];
  
  // Check if file imports UnifiedAuthService
  const hasUnifiedAuthImport = content.includes('UnifiedAuthService');
  const hasAuthErrorImport = content.includes('AuthenticationError');
  
  // Look for API route handlers
  const routeHandlers = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
  const hasRouteHandler = routeHandlers.some(method => 
    content.includes(`export async function ${method}`)
  );
  
  if (!hasRouteHandler) {
    return issues;
  }
  
  // Check for missing imports
  if (!hasUnifiedAuthImport) {
    issues.push({
      file: filePath,
      line: 1,
      issue: 'Missing UnifiedAuthService import',
      pattern: 'Missing import',
      recommendation: "Add: import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'"
    });
  }
  
  if (!hasAuthErrorImport && content.includes('authenticateUser')) {
    issues.push({
      file: filePath,
      line: 1,
      issue: 'Missing AuthenticationError import',
      pattern: 'Missing import',
      recommendation: "Add AuthenticationError to error-handler import"
    });
  }
  
  // Check for old auth patterns
  lines.forEach((line, index) => {
    // Direct supabase.auth.getUser
    if (line.includes('supabase.auth.getUser()')) {
      issues.push({
        file: filePath,
        line: index + 1,
        issue: 'Using direct supabase.auth.getUser()',
        pattern: line.trim(),
        recommendation: 'Use: const user = await UnifiedAuthService.authenticateUser(request)'
      });
    }
    
    // Old authenticateUser pattern
    if (line.includes('authenticateUser()') && !line.includes('UnifiedAuthService')) {
      issues.push({
        file: filePath,
        line: index + 1,
        issue: 'Using undefined authenticateUser()',
        pattern: line.trim(),
        recommendation: 'Use: UnifiedAuthService.authenticateUser(request)'
      });
    }
    
    // Check for missing error handling
    if (line.includes('UnifiedAuthService.authenticateUser') && 
        !lines[index + 1]?.includes('if (!user)')) {
      issues.push({
        file: filePath,
        line: index + 1,
        issue: 'Missing error handling after authentication',
        pattern: line.trim(),
        recommendation: 'Add: if (!user) { return handleAPIError(new AuthenticationError()); }'
      });
    }
    
    // Check for old import patterns
    if (line.includes('@supabase/auth-helpers-nextjs')) {
      issues.push({
        file: filePath,
        line: index + 1,
        issue: 'Using deprecated @supabase/auth-helpers-nextjs',
        pattern: line.trim(),
        recommendation: 'Use unified Supabase client utilities'
      });
    }
  });
  
  return issues;
}

async function generateFixReport(issues: AuthPatternIssue[]): Promise<void> {
  const reportPath = join(__dirname, '..', 'api-auth-issues-report.md');
  
  let report = '# API Authentication Pattern Issues Report\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  report += `Total issues found: ${issues.length}\n\n`;
  
  // Group by file
  const byFile = issues.reduce((acc, issue) => {
    if (!acc[issue.file]) acc[issue.file] = [];
    acc[issue.file].push(issue);
    return acc;
  }, {} as Record<string, AuthPatternIssue[]>);
  
  for (const [file, fileIssues] of Object.entries(byFile)) {
    report += `## ${file.replace(/\\/g, '/')}\n\n`;
    
    for (const issue of fileIssues) {
      report += `- **Line ${issue.line}**: ${issue.issue}\n`;
      report += `  - Pattern: \`${issue.pattern}\`\n`;
      report += `  - Fix: ${issue.recommendation}\n\n`;
    }
  }
  
  // Add fix script
  report += '\n## Quick Fix Commands\n\n';
  report += '```bash\n';
  report += '# Add missing imports\n';
  report += `grep -L "UnifiedAuthService" src/app/api/**/route.ts | xargs -I {} sed -i '1i\\import { UnifiedAuthService } from "@/lib/auth/unified-auth-service"' {}\n\n`;
  report += '# Fix authenticateUser patterns\n';
  report += `find src/app/api -name "route.ts" -exec sed -i 's/const authResult = await authenticateUser()/const user = await UnifiedAuthService.authenticateUser(request)/g' {} \\;\n`;
  report += '```\n';
  
  await writeFile(reportPath, report);
  console.log(`Report generated: ${reportPath}`);
}

async function main() {
  console.log('🔍 Scanning for API routes with authentication issues...\n');
  
  const apiDir = join(__dirname, '..', 'src', 'app', 'api');
  const routes = await findAPIRoutes(apiDir);
  
  console.log(`Found ${routes.length} API route files\n`);
  
  const allIssues: AuthPatternIssue[] = [];
  
  for (const route of routes) {
    const issues = await analyzeFile(route);
    if (issues.length > 0) {
      console.log(`❌ ${route}: ${issues.length} issues`);
      allIssues.push(...issues);
    } else {
      console.log(`✅ ${route}: No issues`);
    }
  }
  
  console.log(`\n📊 Summary: ${allIssues.length} total issues found`);
  
  if (allIssues.length > 0) {
    await generateFixReport(allIssues);
    
    // Show most common issues
    const issueTypes = allIssues.reduce((acc, issue) => {
      acc[issue.issue] = (acc[issue.issue] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    console.log('\n🔝 Most common issues:');
    Object.entries(issueTypes)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .forEach(([issue, count]) => {
        console.log(`  - ${issue}: ${count} occurrences`);
      });
  }
}

// Run the script
main().catch(console.error);