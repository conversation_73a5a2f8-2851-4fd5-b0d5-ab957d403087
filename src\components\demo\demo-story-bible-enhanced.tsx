"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { BookOpen } from 'lucide-react'
import { Users } from 'lucide-react'
import { Globe } from 'lucide-react'
import { Clock } from 'lucide-react'
import { Target } from 'lucide-react'
import { Heart } from 'lucide-react'
import { Sword } from 'lucide-react'
import { Crown } from 'lucide-react'
import { Sparkles } from 'lucide-react'
import { MapPin } from 'lucide-react'
import { <PERSON>roll } from 'lucide-react'
import { Plus } from 'lucide-react'
import { <PERSON> } from 'lucide-react'
import { Search } from 'lucide-react'
import { <PERSON><PERSON> } from 'lucide-react'
import { Star } from 'lucide-react'
import { Zap } from 'lucide-react'
import { Shield } from 'lucide-react'
import { Flame } from 'lucide-react'
import { Eye } from 'lucide-react'
import { Brain } from 'lucide-react'
import { TreePine } from 'lucide-react'
import { Mountain } from 'lucide-react'
import { Castle } from 'lucide-react'
import { Home } from 'lucide-react'
import { CheckCircle } from 'lucide-react';

const characters = [
  {
    id: 1,
    name: "Aria Moonwhisper",
    role: "Protagonist",
    status: "Active",
    chapters: "1-12",
    age: 19,
    location: "Crystal Chamber",
    description: "A young Crystal Mage discovering her true power",
    personality: ["Determined", "Curious", "Compassionate", "Impulsive"],
    abilities: ["Crystal Resonance", "Vision Sight", "Elemental Affinity"],
    relationships: [
      { character: "Marcus Stormwind", type: "Mentor", strength: 85 },
      { character: "Zara Nightblade", type: "Close Friend", strength: 92 },
      { character: "Shadow King", type: "Enemy", strength: 60 }
    ],
    arc: { current: 75, total: 100, milestone: "Discovering Shadow King's origin" },
    voiceNotes: "Speaks with wonder and determination. Uses metaphors related to light and crystals."
  },
  {
    id: 2,
    name: "Marcus Stormwind",
    role: "Mentor",
    status: "Active",
    chapters: "2-12",
    age: 45,
    location: "Crystal Chamber",
    description: "Former Crystal Mage turned protector and guide",
    personality: ["Wise", "Protective", "Haunted", "Patient"],
    abilities: ["Storm Magic", "Ancient Knowledge", "Combat Mastery"],
    relationships: [
      { character: "Aria Moonwhisper", type: "Student", strength: 85 },
      { character: "Zara Nightblade", type: "Ally", strength: 70 }
    ],
    arc: { current: 60, total: 100, milestone: "Confronting his past failures" },
    voiceNotes: "Speaks formally but warmly. Often references ancient wisdom and past experiences."
  },
  {
    id: 3,
    name: "Zara Nightblade",
    role: "Ally",
    status: "Active",
    chapters: "4-12",
    age: 22,
    location: "Crystal Chamber",
    description: "Skilled rogue with a sharp wit and loyal heart",
    personality: ["Pragmatic", "Loyal", "Sarcastic", "Brave"],
    abilities: ["Shadow Step", "Blade Mastery", "Stealth"],
    relationships: [
      { character: "Aria Moonwhisper", type: "Best Friend", strength: 92 },
      { character: "Marcus Stormwind", type: "Ally", strength: 70 }
    ],
    arc: { current: 45, total: 100, milestone: "Learning to trust others completely" },
    voiceNotes: "Quick wit, often sarcastic. Uses modern expressions and practical language."
  },
  {
    id: 4,
    name: "Shadow King",
    role: "Antagonist",
    status: "Mentioned",
    chapters: "1,5,8,12",
    age: "Unknown",
    location: "Shadow Realm",
    description: "Ancient entity seeking to corrupt the crystal magic",
    personality: ["Manipulative", "Ancient", "Powerful", "Tragic"],
    abilities: ["Shadow Manipulation", "Mind Control", "Reality Warping"],
    relationships: [
      { character: "Aria Moonwhisper", type: "Nemesis", strength: 60 }
    ],
    arc: { current: 25, total: 100, milestone: "Origin story revelation" },
    voiceNotes: "Speaks in riddles and ancient phrases. Voice echoes with power and sorrow."
  }
];

const locations = [
  {
    id: 1,
    name: "Crystal Chamber",
    type: "Sacred Site",
    status: "Current",
    region: "Heart of Aethermoor",
    description: "Ancient chamber housing the Heart of Aethermoor crystal",
    atmosphere: "Mystical, ethereal, humming with power",
    keyFeatures: ["Central Crystal Pedestal", "Crystalline Formations", "Ancient Archway", "Glowing Walls"],
    history: "Built by the first Crystal Mages as a conduit to the realm's power source",
    connections: ["Whispering Woods", "Ancient Temple Complex"],
    significance: "Major plot location - where Aria discovers the Shadow King's origin"
  },
  {
    id: 2,
    name: "Whispering Woods",
    type: "Forest",
    status: "Previous",
    region: "Outer Aethermoor",
    description: "Mystical forest surrounding the sacred sites",
    atmosphere: "Mysterious, alive with ancient magic",
    keyFeatures: ["Singing Trees", "Moonlit Paths", "Hidden Clearings", "Spirit Guardians"],
    history: "Natural barrier protecting the sacred sites from intruders",
    connections: ["Crystal Chamber", "Village of Moonhaven"],
    significance: "Journey location - tests characters before reaching the chamber"
  },
  {
    id: 3,
    name: "Moonhaven Village",
    type: "Settlement",
    status: "Future",
    region: "Eastern Aethermoor",
    description: "Peaceful village where Aria grew up",
    atmosphere: "Warm, welcoming, touched by gentle magic",
    keyFeatures: ["Crystal Gardens", "Moonstone Plaza", "Elder's Hall", "Training Grounds"],
    history: "Founded by refugees from the Crystal Wars centuries ago",
    connections: ["Whispering Woods", "Trade Routes"],
    significance: "Aria's origin point and potential safe haven"
  }
];

const plotThreads = [
  {
    id: 1,
    name: "Crystal Quest",
    status: "Active",
    progress: 75,
    priority: "High",
    chapters: "1-12",
    description: "Aria's journey to master crystal magic and save Aethermoor",
    keyEvents: ["Discovery of Powers", "Meeting Marcus", "First Vision", "Crystal Chamber"],
    nextMilestone: "Mastering the Heart Crystal",
    resolution: "Planned for Chapter 18"
  },
  {
    id: 2,
    name: "Shadow King's Origin",
    status: "Developing",
    progress: 25,
    priority: "High",
    chapters: "1,5,8,12",
    description: "Uncovering the truth about the Shadow King's past and motivations",
    keyEvents: ["First Mention", "Dream Visions", "Ancient Texts", "Crystal Revelation"],
    nextMilestone: "Full origin story reveal",
    resolution: "Planned for Chapter 15"
  },
  {
    id: 3,
    name: "Aria's Powers",
    status: "Ongoing",
    progress: 60,
    priority: "Medium",
    chapters: "1-12",
    description: "Aria's gradual awakening and mastery of her crystal magic abilities",
    keyEvents: ["First Manifestation", "Training Begins", "Power Surge", "Vision Ability"],
    nextMilestone: "Controlling visions at will",
    resolution: "Ongoing throughout series"
  },
  {
    id: 4,
    name: "Marcus's Redemption",
    status: "Building",
    progress: 35,
    priority: "Medium",
    chapters: "2,6,9,12",
    description: "Marcus confronting his past failures and finding redemption",
    keyEvents: ["Reluctant Mentor", "Past Hints", "Guilt Revealed", "Protective Instinct"],
    nextMilestone: "Confession of past mistakes",
    resolution: "Planned for Chapter 16"
  }
];

const magicSystem = {
  name: "Crystal Magic",
  source: "Aethermoor's Heart Crystal",
  types: [
    { name: "Resonance", description: "Connecting with crystal energy", users: ["Aria", "Marcus"] },
    { name: "Elemental", description: "Manipulating natural elements", users: ["Marcus", "Ancient Mages"] },
    { name: "Vision", description: "Seeing past and future events", users: ["Aria"] },
    { name: "Shadow", description: "Corrupted crystal magic", users: ["Shadow King"] }
  ],
  rules: [
    "Magic requires emotional connection to crystals",
    "Overuse leads to crystal sickness",
    "Shadow magic corrupts the user over time",
    "Pure crystal magic can cleanse shadow corruption"
  ]
};

export function DemoStoryBibleEnhanced() {
  const [selectedCharacter, setSelectedCharacter] = useState<number | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<number | null>(null);
  const [selectedThread, setSelectedThread] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState("characters");
  const [searchTerm, setSearchTerm] = useState("");

  return (
    <div className="w-full h-full bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card/50 backdrop-blur-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
            <div className="flex items-center gap-2">
              <BookOpen className="w-6 h-6 text-primary" />
              <h2 className="text-2xl font-bold">Story Bible & World Management</h2>
              <Badge variant="outline" className="border-primary/50 text-primary">
                Demo Content
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Search className="w-4 h-4 mr-2" />
              Search
            </Button>
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Add Entry
            </Button>
          </div>
        </div>

        <p className="text-muted-foreground mt-2 max-w-3xl">
          Explore how BookScribe AI organizes and tracks all elements of your story world, 
          ensuring perfect consistency across your entire narrative universe.
        </p>
      </div>

      <div className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full max-w-2xl lg:max-w-3xl xl:max-w-4xl grid-cols-5">
            <TabsTrigger value="characters">Characters</TabsTrigger>
            <TabsTrigger value="world">World</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
            <TabsTrigger value="threads">Plot Threads</TabsTrigger>
            <TabsTrigger value="magic">Magic System</TabsTrigger>
          </TabsList>

          <TabsContent value="characters" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Character List */}
              <div className="lg:col-span-2 space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Character Roster ({characters.length})
                  </h3>
                  <Button variant="outline" size="sm">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Character
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
                  {characters.map((character) => (
                    <Card 
                      key={character.id}
                      className={`cursor-pointer transition-all duration-300 hover:scale-105 ${
                        selectedCharacter === character.id 
                          ? 'border-primary/50 bg-primary/10 shadow-lg' 
                          : 'hover:border-primary/30'
                      }`}
                      onClick={() => setSelectedCharacter(selectedCharacter === character.id ? null : character.id)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-lg">{character.name}</CardTitle>
                            <p className="text-sm text-muted-foreground">{character.role} • Age {character.age}</p>
                          </div>
                          <Badge 
                            variant={character.status === 'Active' ? 'default' : 'secondary'}
                            className={character.status === 'Active' ? 'bg-green-500/20 text-green-700 border-green-500/50' : ''}
                          >
                            {character.status}
                          </Badge>
                        </div>
                      </CardHeader>

                      <CardContent className="space-y-3">
                        <p className="text-sm text-muted-foreground">{character.description}</p>
                        
                        <div className="flex items-center justify-between text-xs">
                          <span>Chapters: {character.chapters}</span>
                          <span>Location: {character.location}</span>
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-xs font-medium">Character Arc</span>
                            <span className="text-xs text-muted-foreground">{character.arc.current}%</span>
                          </div>
                          <Progress value={character.arc.current} className="h-1" />
                          <p className="text-xs text-muted-foreground mt-1">{character.arc.milestone}</p>
                        </div>

                        <div className="flex flex-wrap gap-1">
                          {character.personality.slice(0, 3).map((trait, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {trait}
                            </Badge>
                          ))}
                          {character.personality.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{character.personality.length - 3}
                            </Badge>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Character Details */}
              <div className="space-y-4">
                {selectedCharacter ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        Character Details
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {(() => {
                        const character = characters.find(c => c.id === selectedCharacter);
                        if (!character) return null;
                        
                        return (
                          <>
                            <div>
                              <h4 className="font-medium mb-2">Abilities</h4>
                              <div className="space-y-1">
                                {character.abilities.map((ability, idx) => (
                                  <div key={idx} className="flex items-center gap-2 text-sm">
                                    <Zap className="w-3 h-3 text-primary" />
                                    <span>{ability}</span>
                                  </div>
                                ))}
                              </div>
                            </div>

                            <Separator />

                            <div>
                              <h4 className="font-medium mb-2">Relationships</h4>
                              <div className="space-y-2">
                                {character.relationships.map((rel, idx) => (
                                  <div key={idx} className="flex items-center justify-between text-sm">
                                    <div>
                                      <div className="font-medium">{rel.character}</div>
                                      <div className="text-muted-foreground text-xs">{rel.type}</div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <Progress value={rel.strength} className="w-12 h-1" />
                                      <span className="text-xs text-muted-foreground">{rel.strength}%</span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>

                            <Separator />

                            <div>
                              <h4 className="font-medium mb-2">Voice Notes</h4>
                              <p className="text-sm text-muted-foreground">{character.voiceNotes}</p>
                            </div>
                          </>
                        );
                      })()}
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="p-6 text-center text-muted-foreground">
                      <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>Select a character to view detailed information</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="world" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Location List */}
              <div className="lg:col-span-2 space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Globe className="w-5 h-5" />
                    World Locations ({locations.length})
                  </h3>
                  <Button variant="outline" size="sm">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Location
                  </Button>
                </div>
                
                <div className="space-y-4">
                  {locations.map((location) => (
                    <Card 
                      key={location.id}
                      className={`cursor-pointer transition-all duration-300 hover:scale-105 ${
                        selectedLocation === location.id 
                          ? 'border-primary/50 bg-primary/10 shadow-lg' 
                          : 'hover:border-primary/30'
                      }`}
                      onClick={() => setSelectedLocation(selectedLocation === location.id ? null : location.id)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-primary/20">
                              {location.type === 'Sacred Site' && <Sparkles className="w-5 h-5 text-primary" />}
                              {location.type === 'Forest' && <TreePine className="w-5 h-5 text-green-500" />}
                              {location.type === 'Settlement' && <Home className="w-5 h-5 text-blue-500" />}
                            </div>
                            <div>
                              <CardTitle className="text-lg">{location.name}</CardTitle>
                              <p className="text-sm text-muted-foreground">{location.type} • {location.region}</p>
                            </div>
                          </div>
                          <Badge 
                            variant={
                              location.status === 'Current' ? 'default' : 
                              location.status === 'Previous' ? 'secondary' : 'outline'
                            }
                            className={
                              location.status === 'Current' ? 'bg-green-500/20 text-green-700 border-green-500/50' :
                              location.status === 'Previous' ? 'bg-blue-500/20 text-blue-700 border-blue-500/50' : ''
                            }
                          >
                            {location.status}
                          </Badge>
                        </div>
                      </CardHeader>

                      <CardContent className="space-y-3">
                        <p className="text-sm text-muted-foreground">{location.description}</p>
                        
                        <div>
                          <p className="text-xs font-medium mb-1">Atmosphere:</p>
                          <p className="text-xs text-muted-foreground">{location.atmosphere}</p>
                        </div>

                        <div>
                          <p className="text-xs font-medium mb-1">Key Features:</p>
                          <div className="flex flex-wrap gap-1">
                            {location.keyFeatures.slice(0, 3).map((feature, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {feature}
                              </Badge>
                            ))}
                            {location.keyFeatures.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{location.keyFeatures.length - 3}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Location Details */}
              <div className="space-y-4">
                {selectedLocation ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        Location Details
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {(() => {
                        const location = locations.find(l => l.id === selectedLocation);
                        if (!location) return null;
                        
                        return (
                          <>
                            <div>
                              <h4 className="font-medium mb-2">History</h4>
                              <p className="text-sm text-muted-foreground">{location.history}</p>
                            </div>

                            <Separator />

                            <div>
                              <h4 className="font-medium mb-2">Connections</h4>
                              <div className="space-y-1">
                                {location.connections.map((connection, idx) => (
                                  <div key={idx} className="flex items-center gap-2 text-sm">
                                    <MapPin className="w-3 h-3 text-primary" />
                                    <span>{connection}</span>
                                  </div>
                                ))}
                              </div>
                            </div>

                            <Separator />

                            <div>
                              <h4 className="font-medium mb-2">Story Significance</h4>
                              <p className="text-sm text-muted-foreground">{location.significance}</p>
                            </div>

                            <Separator />

                            <div>
                              <h4 className="font-medium mb-2">All Features</h4>
                              <div className="space-y-1">
                                {location.keyFeatures.map((feature, idx) => (
                                  <div key={idx} className="flex items-center gap-2 text-sm">
                                    <Star className="w-3 h-3 text-yellow-500" />
                                    <span>{feature}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </>
                        );
                      })()}
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="p-6 text-center text-muted-foreground">
                      <Globe className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>Select a location to view detailed information</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="timeline" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Story Timeline
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="relative">
                    <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-border"></div>
                    
                    {[
                      { chapter: "Chapter 1-3", event: "Aria's Awakening", status: "complete" },
                      { chapter: "Chapter 4-6", event: "Meeting the Mentor", status: "complete" },
                      { chapter: "Chapter 7-9", event: "First Trials", status: "complete" },
                      { chapter: "Chapter 10-12", event: "Crystal Chamber Discovery", status: "current" },
                      { chapter: "Chapter 13-15", event: "Shadow King's Truth", status: "planned" },
                      { chapter: "Chapter 16-18", event: "Final Confrontation", status: "planned" }
                    ].map((event, idx) => (
                      <div key={idx} className="relative flex items-center gap-4 sm:gap-5 lg:gap-6">
                        <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center ${
                          event.status === 'complete' ? 'bg-green-500 border-green-500' :
                          event.status === 'current' ? 'bg-primary border-primary' :
                          'bg-background border-border'
                        }`}>
                          {event.status === 'complete' && <CheckCircle className="w-4 h-4 text-white" />}
                          {event.status === 'current' && <Clock className="w-4 h-4 text-white" />}
                          {event.status === 'planned' && <Target className="w-4 h-4 text-muted-foreground" />}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium">{event.event}</h4>
                          <p className="text-sm text-muted-foreground">{event.chapter}</p>
                        </div>
                        <Badge variant={
                          event.status === 'complete' ? 'default' :
                          event.status === 'current' ? 'secondary' : 'outline'
                        }>
                          {event.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="threads" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {plotThreads.map((thread) => (
                <Card 
                  key={thread.id}
                  className={`cursor-pointer transition-all duration-300 hover:scale-105 ${
                    selectedThread === thread.id 
                      ? 'border-primary/50 bg-primary/10 shadow-lg' 
                      : 'hover:border-primary/30'
                  }`}
                  onClick={() => setSelectedThread(selectedThread === thread.id ? null : thread.id)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{thread.name}</CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge variant={
                          thread.priority === 'High' ? 'destructive' :
                          thread.priority === 'Medium' ? 'default' : 'secondary'
                        }>
                          {thread.priority}
                        </Badge>
                        <Badge variant={
                          thread.status === 'Active' ? 'default' :
                          thread.status === 'Developing' ? 'secondary' :
                          thread.status === 'Building' ? 'outline' : 'secondary'
                        }>
                          {thread.status}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <p className="text-sm text-muted-foreground">{thread.description}</p>
                    
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Progress</span>
                        <span className="text-sm text-muted-foreground">{thread.progress}%</span>
                      </div>
                      <Progress value={thread.progress} className="h-2" />
                    </div>

                    <div className="text-sm">
                      <p><span className="font-medium">Chapters:</span> {thread.chapters}</p>
                      <p><span className="font-medium">Next Milestone:</span> {thread.nextMilestone}</p>
                      <p><span className="font-medium">Resolution:</span> {thread.resolution}</p>
                    </div>

                    {selectedThread === thread.id && (
                      <div className="mt-4 pt-4 border-t border-border">
                        <h4 className="font-medium mb-2">Key Events</h4>
                        <div className="space-y-1">
                          {thread.keyEvents.map((event, idx) => (
                            <div key={idx} className="flex items-center gap-2 text-sm">
                              <CheckCircle className="w-3 h-3 text-green-500" />
                              <span>{event}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="magic" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  {magicSystem.name} System
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="font-medium mb-3">Magic Types</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
                    {magicSystem.types.map((type, idx) => (
                      <Card key={idx} className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Zap className="w-4 h-4 text-primary" />
                          <h5 className="font-medium">{type.name}</h5>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">{type.description}</p>
                        <div>
                          <p className="text-xs font-medium mb-1">Users:</p>
                          <div className="flex flex-wrap gap-1">
                            {type.users.map((user, userIdx) => (
                              <Badge key={userIdx} variant="outline" className="text-xs">
                                {user}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-3">Magic System Rules</h4>
                  <div className="space-y-2">
                    {magicSystem.rules.map((rule, idx) => (
                      <div key={idx} className="flex items-start gap-2">
                        <Shield className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                        <p className="text-sm">{rule}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-3">Power Source</h4>
                  <div className="flex items-center gap-3 p-4 rounded border">
                    <div className="p-2 rounded-lg bg-primary/20">
                      <Sparkles className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h5 className="font-medium">{magicSystem.source}</h5>
                      <p className="text-sm text-muted-foreground">
                        The central crystal that powers all magic in Aethermoor
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
