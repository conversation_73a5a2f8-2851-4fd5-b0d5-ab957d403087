import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { ServiceBase, ServiceResponse } from './base-service';
import { Database } from '@/lib/db/database.types';

type Notification = Database['public']['Tables']['notifications']['Row'];
type NotificationInsert = Database['public']['Tables']['notifications']['Insert'];

export interface NotificationPreferences {
  marketing?: boolean;
  progress?: boolean;
  achievements?: boolean;
  collaboration?: boolean;
  newsletter?: boolean;
}

export class NotificationService extends ServiceBase {
  constructor() {
    super({
      name: 'notification-service',
      version: '1.0.0',
      endpoints: ['/api/notifications'],
      dependencies: [],
      healthCheck: '/api/services/notification/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, { status: 'healthy' });
  }

  /**
   * Get notifications for a user
   */
  async getNotifications(
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      unreadOnly?: boolean;
    } = {}
  ): Promise<ServiceResponse<{
    notifications: Notification[];
    total: number;
    unread_count: number;
  }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const { limit = 20, offset = 0, unreadOnly = false } = options;

      let query = supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (unreadOnly) {
        query = query.eq('read', false);
      }

      const { data: notifications, error } = await query;

      if (error) {
        logger.error('[NotificationService] Error fetching notifications:', error);
        throw error;
      }

      // Get total count for pagination
      const { count } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .then((res: { count: number | null }) => ({ count: res.count || 0 }));

      // Get unread count
      const { count: unreadCount } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('read', false)
        .then((res: { count: number | null }) => ({ count: res.count || 0 }));

      return {
        notifications: notifications || [],
        total: count,
        unread_count: unreadCount,
      };
    });
  }

  /**
   * Create a new notification
   */
  async createNotification(
    notification: NotificationInsert
  ): Promise<ServiceResponse<Notification>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('notifications')
        .insert(notification)
        .select()
        .single();

      if (error) {
        logger.error('[NotificationService] Error creating notification:', error);
        throw error;
      }

      if (!data) {
        throw new Error('Failed to create notification');
      }

      return data;
    });
  }

  /**
   * Mark notifications as read
   */
  async markNotificationsAsRead(
    userId: string,
    options: {
      notificationIds?: string[];
      markAll?: boolean;
    }
  ): Promise<ServiceResponse<{ updated_count: number }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const { notificationIds, markAll } = options;

      let query = supabase
        .from('notifications')
        .update({ 
          read: true, 
          read_at: new Date().toISOString() 
        })
        .eq('user_id', userId);

      if (markAll) {
        query = query.eq('read', false);
      } else if (notificationIds && notificationIds.length > 0) {
        query = query.in('id', notificationIds);
      } else {
        throw new Error('Either notificationIds or markAll must be provided');
      }

      const { count, error } = await query
        .select('id', { count: 'exact' });

      if (error) {
        logger.error('[NotificationService] Error marking notifications as read:', error);
        throw error;
      }

      return { updated_count: count || 0 };
    });
  }

  /**
   * Delete a notification
   */
  async deleteNotification(
    userId: string,
    notificationId: string
  ): Promise<ServiceResponse<{ success: boolean }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        logger.error('[NotificationService] Error deleting notification:', error);
        throw error;
      }

      return { success: true };
    });
  }

  /**
   * Get notification preferences for a user
   */
  async getNotificationPreferences(userId: string): Promise<ServiceResponse<NotificationPreferences>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('profiles')
        .select('notification_preferences')
        .eq('id', userId)
        .single();

      if (error) {
        logger.error('[NotificationService] Error fetching notification preferences:', error);
        throw error;
      }

      // Return default preferences if none set
      return data?.notification_preferences || {
        marketing: true,
        progress: true,
        achievements: true,
        collaboration: true,
        newsletter: false
      };
    });
  }

  /**
   * Update notification preferences for a user
   */
  async updateNotificationPreferences(
    userId: string,
    preferences: NotificationPreferences
  ): Promise<ServiceResponse<NotificationPreferences>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('profiles')
        .update({
          notification_preferences: preferences,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select('notification_preferences')
        .single();

      if (error) {
        logger.error('[NotificationService] Error updating notification preferences:', error);
        throw error;
      }

      return data?.notification_preferences || preferences;
    });
  }

  /**
   * Send bulk notifications
   */
  async sendBulkNotifications(
    notifications: NotificationInsert[]
  ): Promise<ServiceResponse<{ sent_count: number }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('notifications')
        .insert(notifications)
        .select('id');

      if (error) {
        logger.error('[NotificationService] Error sending bulk notifications:', error);
        throw error;
      }

      return { sent_count: data?.length || 0 };
    });
  }

  /**
   * Clean up old notifications
   */
  async cleanupOldNotifications(daysOld: number = 90): Promise<ServiceResponse<{ deleted_count: number }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const { count, error } = await supabase
        .from('notifications')
        .delete()
        .lt('created_at', cutoffDate.toISOString())
        .select('id', { count: 'exact' });

      if (error) {
        logger.error('[NotificationService] Error cleaning up old notifications:', error);
        throw error;
      }

      return { deleted_count: count || 0 };
    });
  }
}

// Create and export singleton instance
export const notificationService = new NotificationService();