"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Settings } from 'lucide-react'
import { BookOpen } from 'lucide-react'
import { Target } from 'lucide-react'
import { Palette } from 'lucide-react'
import { FileText } from 'lucide-react'
import { Users } from 'lucide-react'
import { Globe } from 'lucide-react'
import { Sparkles } from 'lucide-react'
import { CheckCircle } from 'lucide-react'
import { Clock } from 'lucide-react'
import { Plus } from 'lucide-react'
import { Edit } from 'lucide-react'
import { Save } from 'lucide-react'
import { Wand<PERSON> } from 'lucide-react'
import { <PERSON> } from 'lucide-react'
import { Lightbulb } from 'lucide-react'
import { Star } from 'lucide-react'
import { Zap } from 'lucide-react'
import { Crown } from 'lucide-react'
import { Heart } from 'lucide-react'
import { Sword } from 'lucide-react'
import { Shield } from 'lucide-react'
import { ChevronRight } from 'lucide-react'
import { Building } from 'lucide-react'
import { Check } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const projectTemplates = [
  {
    id: 'fantasy-epic',
    name: 'Epic Fantasy',
    description: 'Multi-book fantasy series with complex world-building',
    features: ['Character Arcs', 'Magic Systems', 'World Building', 'Political Intrigue'],
    icon: Crown,
    color: 'purple'
  },
  {
    id: 'sci-fi-space',
    name: 'Space Opera',
    description: 'Galaxy-spanning adventure with advanced technology',
    features: ['Technology Systems', 'Alien Cultures', 'Space Politics', 'Hero\'s Journey'],
    icon: Star,
    color: 'blue'
  },
  {
    id: 'mystery-thriller',
    name: 'Mystery Thriller',
    description: 'Suspenseful mystery with complex plot twists',
    features: ['Clue Tracking', 'Character Motives', 'Timeline Management', 'Red Herrings'],
    icon: Lightbulb,
    color: 'amber'
  },
  {
    id: 'romance-contemporary',
    name: 'Contemporary Romance',
    description: 'Character-driven romance in modern settings',
    features: ['Relationship Arcs', 'Emotional Beats', 'Character Chemistry', 'Conflict Resolution'],
    icon: Heart,
    color: 'pink'
  },
  {
    id: 'custom',
    name: 'Custom Project',
    description: 'Start from scratch with your own structure',
    features: ['Flexible Setup', 'Custom Templates', 'Personalized Workflow', 'Adaptive Tools'],
    icon: Wand2,
    color: 'green'
  }
];

const wizardSteps = [
  { id: "foundation", title: "Story Foundation", icon: BookOpen },
  { id: "framework", title: "Story Framework", icon: Building },
  { id: "world", title: "World & Characters", icon: Users },
  { id: "elements", title: "Themes & Elements", icon: Palette },
  { id: "review", title: "Review & Create", icon: Sparkles },
];

const genreOptions = [
  'Fantasy', 'Science Fiction', 'Mystery', 'Romance', 'Thriller', 'Horror',
  'Historical Fiction', 'Literary Fiction', 'Young Adult', 'Children\'s',
  'Non-Fiction', 'Biography', 'Self-Help', 'Other'
];

const toneOptions = [
  'Epic', 'Adventurous', 'Dark', 'Mysterious', 'Humorous', 'Lighthearted',
  'Serious', 'Emotional', 'Romantic', 'Action-packed', 'Suspenseful', 'Gritty',
  'Whimsical', 'Melancholic', 'Inspiring', 'Satirical', 'Philosophical', 'Introspective',
  'Fast-paced', 'Contemplative', 'Dramatic', 'Intense', 'Nostalgic', 'Hopeful'
];

const themeOptions = [
  // Universal Themes
  'Love', 'Death', 'Power', 'Freedom', 'Justice', 'Redemption', 'Sacrifice', 'Hope',
  'Fear', 'Identity', 'Family', 'Friendship', 'Betrayal', 'Forgiveness', 'Destiny', 'Fate',
  'Good vs Evil', 'Coming of Age', 'Loss', 'Survival', 'Truth', 'Honor', 'Courage', 'Wisdom',
  
  // Genre-Specific Themes
  'Time', 'Technology', 'Nature', 'Magic', 'War', 'Peace', 'Revolution', 'Discovery',
  'Adventure', 'Mystery', 'Transformation', 'Isolation', 'Community', 'Legacy', 'Memory', 'Dreams',
  'Ambition', 'Corruption', 'Innocence', 'Experience', 'Tradition', 'Progress', 'Faith', 'Doubt',
  'Duty', 'Passion', 'Vengeance', 'Loyalty', 'Deception', 'Reality', 'Illusion', 'Chaos',
  'Order', 'Creation', 'Destruction', 'Knowledge', 'Ignorance', 'Pride', 'Humility', 'Greed'
];

const contentElementOptions = [
  'Action Sequences', 'Romance', 'Mystery Elements', 'Political Intrigue', 'Magic Systems',
  'Technology', 'World-building', 'Character Development', 'Dialogue-heavy', 'Description-rich',
  'Plot Twists', 'Cliffhangers', 'Flashbacks', 'Multiple POVs', 'Unreliable Narrator',
  'Time Jumps', 'Parallel Storylines', 'Symbolism', 'Metaphors', 'Foreshadowing',
  'Comic Relief', 'Social Commentary', 'Philosophical Questions', 'Moral Dilemmas',
  'Cultural Exploration', 'Historical Elements', 'Scientific Concepts', 'Psychological Depth'
];

export function DemoProjectSetupEnhanced() {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>('fantasy-epic');
  const [currentStep, setCurrentStep] = useState("foundation");
  const [activeTab, setActiveTab] = useState("foundation");
  const [projectData, setProjectData] = useState({
    title: 'The Crystal Saga',
    description: 'An epic fantasy series following Aria Moonwhisper as she discovers her crystal magic powers and faces the ancient Shadow King.',
    genre: 'Fantasy',
    subgenre: 'Epic Fantasy',
    targetAudience: 'Young Adult',
    style: 'Third Person Limited',
    structure: 'Three Act Structure',
    pacing: 'Steady Build',
    narrativeVoice: 'third-limited',
    tense: 'past',
    targetLength: '80000',
    chapters: '18',
    tones: ['Epic', 'Adventurous', 'Mysterious'],
    themes: ['Power', 'Destiny', 'Friendship', 'Good vs Evil'],
    contentElements: ['Magic Systems', 'World-building', 'Character Development', 'Plot Twists'],
    protagonist: 'Aria Moonwhisper - A young mage discovering her powers',
    antagonist: 'The Shadow King - Ancient evil seeking to return',
    setting: 'The Crystal Kingdoms - A realm where magic flows through crystals',
    timePeriod: 'Medieval Fantasy',
    series: true,
    seriesBooks: '3'
  });

  const [aiSettings, setAiSettings] = useState({
    writingStyle: 'descriptive',
    assistanceLevel: 'balanced',
    focusAreas: ['character-development', 'world-building', 'dialogue'],
    creativityLevel: 75,
    consistencyChecking: true,
    realTimeAnalysis: true
  });

  const currentTemplate = projectTemplates.find(t => t.id === selectedTemplate);
  const currentStepIndex = wizardSteps.findIndex(s => s.id === currentStep);
  const progressPercentage = ((currentStepIndex + 1) / wizardSteps.length) * 100;

  return (
    <div className="w-full h-full bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <div className="border-b border-border bg-gradient-to-r from-card/60 to-card/40 backdrop-blur-sm p-6 lg:p-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
            <div className="flex items-center gap-3">
              <Sparkles className="w-7 h-7 md:w-8 md:h-8 text-primary animate-pulse" />
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">Create New Project</h2>
              <Badge variant="outline" className="border-primary/50 text-primary text-sm md:text-base px-4 py-2">
                Interactive Demo
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" className="hover:scale-105 transition-transform">
              <Save className="w-4 h-4 mr-2" />
              Save Progress
            </Button>
            <Button variant="outline" size="sm" className="hover:scale-105 transition-transform">
              <Brain className="w-4 h-4 mr-2" />
              AI Suggestions
            </Button>
          </div>
        </div>

        {/* Wizard Progress */}
        <div className="mt-8">
          <Progress value={progressPercentage} className="h-3 shadow-inner" />
          <div className="flex items-center justify-between mt-3 text-sm md:text-base text-muted-foreground font-medium">
            <span>Step {currentStepIndex + 1} of {wizardSteps.length}</span>
            <span>{wizardSteps[currentStepIndex]?.title}</span>
          </div>
        </div>
        
        {/* Icon Navigation */}
        <div className="mt-6 flex items-center justify-center gap-2">
          <TooltipProvider>
            {wizardSteps.map((step, idx) => {
              const StepIcon = step.icon;
              const isActive = step.id === currentStep;
              const isCompleted = idx < currentStepIndex;
              return (
                <Tooltip key={step.id}>
                  <TooltipTrigger asChild>
                    <button
                      onClick={() => {
                        setCurrentStep(step.id);
                        setActiveTab(step.id);
                      }}
                      className={`relative p-3 rounded-lg transition-all ${
                        isActive 
                          ? 'bg-primary text-primary-foreground shadow-lg scale-110' 
                          : isCompleted
                          ? 'bg-primary/20 text-primary hover:bg-primary/30'
                          : 'bg-muted text-muted-foreground hover:bg-muted/80'
                      }`}
                    >
                      <StepIcon className="w-5 h-5" />
                      {isCompleted && !isActive && (
                        <Check className="absolute -top-1 -right-1 w-4 h-4 bg-primary text-primary-foreground rounded-full p-0.5" />
                      )}
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{step.title}</p>
                  </TooltipContent>
                </Tooltip>
              );
            })}
          </TooltipProvider>
        </div>
      </div>

      <div className="p-6 lg:p-8 xl:p-10 2xl:p-12">
        <Tabs value={activeTab} onValueChange={(value) => {
          setActiveTab(value);
          setCurrentStep(value);
        }} className="w-full">

          <TabsContent value="foundation" className="space-y-8 max-w-6xl mx-auto mt-8">
            <div>
              <h3 className="text-2xl md:text-3xl font-bold mb-4">Story Foundation</h3>
              <p className="text-muted-foreground text-base md:text-lg mb-8 max-w-3xl">
                Define the core elements of your story: title, genre, audience, and writing style.
              </p>
            </div>

            {/* Main Form Card */}
            <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow">
              <CardHeader className="bg-gradient-to-r from-card to-card/95 pb-6">
                <CardTitle className="text-xl md:text-2xl">Story Foundation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 p-6 md:p-8">
                {/* Title and Audience Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-base font-semibold mb-3 block">Story Title</label>
                    <Input
                      value={projectData.title}
                      onChange={(e) => setProjectData({...projectData, title: e.target.value})}
                      placeholder="Enter your story title"
                      className="h-12 text-base px-4 border-2 focus:border-primary transition-colors"
                    />
                  </div>
                  <div>
                    <label className="text-base font-semibold mb-3 block">Target Audience</label>
                    <select
                      value={projectData.targetAudience}
                      onChange={(e) => setProjectData({...projectData, targetAudience: e.target.value})}
                      className="w-full h-12 px-4 text-base border-2 border-border rounded-lg bg-background hover:border-primary/50 transition-colors"
                    >
                      <option value="Children">Children (8-12)</option>
                      <option value="Young Adult">Young Adult (13-17)</option>
                      <option value="New Adult">New Adult (18-25)</option>
                      <option value="Adult">Adult (25+)</option>
                      <option value="All Ages">All Ages</option>
                    </select>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="text-base font-semibold mb-3 block">Description</label>
                  <Textarea
                    value={projectData.description}
                    onChange={(e) => setProjectData({...projectData, description: e.target.value})}
                    placeholder="Describe your story..."
                    rows={4}
                    className="text-base p-4 border-2 focus:border-primary transition-colors resize-none"
                  />
                </div>

                {/* Genre and Subgenre Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-base font-semibold mb-3 block">Genre</label>
                    <select
                      value={projectData.genre}
                      onChange={(e) => setProjectData({...projectData, genre: e.target.value})}
                      className="w-full h-12 px-4 text-base border-2 border-border rounded-lg bg-background hover:border-primary/50 transition-colors"
                    >
                      {genreOptions.map(genre => (
                        <option key={genre} value={genre}>{genre}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="text-base font-semibold mb-3 block">Sub-Genre</label>
                    <Input
                      value={projectData.subgenre}
                      onChange={(e) => setProjectData({...projectData, subgenre: e.target.value})}
                      placeholder="e.g., Epic Fantasy, Space Opera"
                      className="h-12 text-base px-4 border-2 focus:border-primary transition-colors"
                    />
                  </div>
                </div>

                {/* Writing Style Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-base font-semibold mb-3 block">Narrative Voice</label>
                    <select
                      value={projectData.narrativeVoice}
                      onChange={(e) => setProjectData({...projectData, narrativeVoice: e.target.value})}
                      className="w-full h-12 px-4 text-base border-2 border-border rounded-lg bg-background hover:border-primary/50 transition-colors"
                    >
                      <option value="first">First Person</option>
                      <option value="third-limited">Third Person Limited</option>
                      <option value="third-omniscient">Third Person Omniscient</option>
                      <option value="second">Second Person</option>
                      <option value="multiple">Multiple POV</option>
                    </select>
                  </div>
                  <div>
                    <label className="text-base font-semibold mb-3 block">Tense</label>
                    <select
                      value={projectData.tense}
                      onChange={(e) => setProjectData({...projectData, tense: e.target.value})}
                      className="w-full h-12 px-4 text-base border-2 border-border rounded-lg bg-background hover:border-primary/50 transition-colors"
                    >
                      <option value="past">Past Tense</option>
                      <option value="present">Present Tense</option>
                      <option value="mixed">Mixed Tense</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>


            {/* Template Selection */}
            <div>
              <h4 className="text-xl md:text-2xl font-semibold mb-6">Choose a Template (Optional)</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                {projectTemplates.map((template) => {
                  const IconComponent = template.icon;
                  return (
                    <Card
                      key={template.id}
                      className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                        selectedTemplate === template.id
                          ? 'border-2 border-primary bg-primary/10'
                          : 'hover:border-primary/50 border-2 border-transparent'
                      }`}
                      onClick={() => setSelectedTemplate(template.id)}
                    >
                      <CardContent className="p-4 text-center">
                        <IconComponent className={`w-8 h-8 mx-auto mb-2 text-${template.color}-500`} />
                        <h5 className="font-semibold text-sm">{template.name}</h5>
                        {selectedTemplate === template.id && (
                          <Check className="w-4 h-4 mx-auto mt-2 text-primary" />
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>

            <div className="flex justify-end pt-8 border-t border-border">
              <Button 
                onClick={() => {
                  setActiveTab('framework');
                  setCurrentStep('framework');
                }}
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-base md:text-lg px-8 py-6 shadow-lg hover:shadow-xl hover:scale-105 transition-all"
              >
                Continue to Story Framework
                <ChevronRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
          </TabsContent>

          {/* Story Framework Tab */}
          <TabsContent value="framework" className="space-y-8 max-w-6xl mx-auto mt-8">
            <div>
              <h3 className="text-2xl md:text-3xl font-bold mb-4">Story Framework</h3>
              <p className="text-muted-foreground text-base md:text-lg mb-8 max-w-3xl">
                Set up your story's structure, pacing, and technical specifications.
              </p>
            </div>

            <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow">
              <CardHeader className="bg-gradient-to-r from-card to-card/95 pb-6">
                <CardTitle className="text-xl md:text-2xl">Structure & Technical Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 p-6 md:p-8">
                {/* Structure and Pacing Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-base font-semibold mb-3 block">Story Structure</label>
                    <select
                      value={projectData.structure}
                      onChange={(e) => setProjectData({...projectData, structure: e.target.value})}
                      className="w-full h-12 px-4 text-base border-2 border-border rounded-lg bg-background hover:border-primary/50 transition-colors"
                    >
                      <option value="Three Act Structure">Three Act Structure</option>
                      <option value="Five Act Structure">Five Act Structure</option>
                      <option value="Hero's Journey">Hero's Journey</option>
                      <option value="Save the Cat">Save the Cat</option>
                      <option value="Fichtean Curve">Fichtean Curve</option>
                      <option value="In Media Res">In Media Res</option>
                      <option value="Nonlinear">Nonlinear</option>
                      <option value="Episodic">Episodic</option>
                    </select>
                  </div>
                  <div>
                    <label className="text-base font-semibold mb-3 block">Pacing</label>
                    <select
                      value={projectData.pacing}
                      onChange={(e) => setProjectData({...projectData, pacing: e.target.value})}
                      className="w-full h-12 px-4 text-base border-2 border-border rounded-lg bg-background hover:border-primary/50 transition-colors"
                    >
                      <option value="Fast-paced">Fast-paced</option>
                      <option value="Steady Build">Steady Build</option>
                      <option value="Slow Burn">Slow Burn</option>
                      <option value="Variable">Variable</option>
                      <option value="Intense Peaks">Intense Peaks</option>
                    </select>
                  </div>
                </div>

                {/* Word Count and Chapters Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-base font-semibold mb-3 block">Target Word Count</label>
                    <Input
                      value={projectData.targetLength}
                      onChange={(e) => setProjectData({...projectData, targetLength: e.target.value})}
                      placeholder="80000"
                      type="number"
                      className="h-12 text-base px-4 border-2 focus:border-primary transition-colors"
                    />
                  </div>
                  <div>
                    <label className="text-base font-semibold mb-3 block">Planned Chapters</label>
                    <Input
                      value={projectData.chapters}
                      onChange={(e) => setProjectData({...projectData, chapters: e.target.value})}
                      placeholder="18"
                      type="number"
                      className="h-12 text-base px-4 border-2 focus:border-primary transition-colors"
                    />
                  </div>
                </div>

                {/* Series Information */}
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="series"
                      checked={projectData.series}
                      onChange={(e) => setProjectData({...projectData, series: e.target.checked})}
                      className="w-5 h-5 rounded border-2 border-border text-primary focus:ring-primary"
                    />
                    <label htmlFor="series" className="text-base font-medium cursor-pointer">
                      This is part of a series
                    </label>
                  </div>

                  {projectData.series && (
                    <div className="ml-8">
                      <label className="text-base font-semibold mb-3 block">Number of Books in Series</label>
                      <Input
                        value={projectData.seriesBooks}
                        onChange={(e) => setProjectData({...projectData, seriesBooks: e.target.value})}
                        placeholder="3"
                        type="number"
                        className="h-12 text-base px-4 border-2 focus:border-primary transition-colors max-w-xs"
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>


            <div className="flex items-center justify-between pt-8 border-t border-border">
              <Button 
                variant="outline" 
                onClick={() => {
                  setActiveTab('foundation');
                  setCurrentStep('foundation');
                }}
                className="text-base px-6 py-6 hover:scale-105 transition-transform"
              >
                Back to Foundation
              </Button>
              <Button 
                onClick={() => {
                  setActiveTab('world');
                  setCurrentStep('world');
                }}
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-base px-8 py-6 shadow-lg hover:shadow-xl hover:scale-105 transition-all"
              >
                Continue to World & Characters
                <ChevronRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
          </TabsContent>

          {/* World & Characters Tab */}
          <TabsContent value="world" className="space-y-8 max-w-6xl mx-auto mt-8">
            <div>
              <h3 className="text-2xl md:text-3xl font-bold mb-4">World & Characters</h3>
              <p className="text-muted-foreground text-base md:text-lg mb-8 max-w-3xl">
                Bring your story to life with compelling characters and immersive world-building.
              </p>
            </div>

            <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow">
              <CardHeader className="bg-gradient-to-r from-card to-card/95 pb-6">
                <CardTitle className="text-xl md:text-2xl">Characters & World Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 p-6 md:p-8">
                {/* Protagonist and Antagonist */}
                <div className="space-y-4">
                  <label className="text-base font-semibold mb-3 block">Protagonist</label>
                  <Textarea
                    value={projectData.protagonist}
                    onChange={(e) => setProjectData({...projectData, protagonist: e.target.value})}
                    placeholder="Describe your main character - their name, background, motivations, and goals"
                    rows={3}
                    className="text-base p-4 border-2 focus:border-primary transition-colors resize-none"
                  />
                </div>

                <div className="space-y-4">
                  <label className="text-base font-semibold mb-3 block">Antagonist</label>
                  <Textarea
                    value={projectData.antagonist}
                    onChange={(e) => setProjectData({...projectData, antagonist: e.target.value})}
                    placeholder="Describe your antagonist - their nature, motivations, and conflict with the protagonist"
                    rows={3}
                    className="text-base p-4 border-2 focus:border-primary transition-colors resize-none"
                  />
                </div>

                {/* Setting and Time Period Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-base font-semibold mb-3 block">Setting</label>
                    <Textarea
                      value={projectData.setting}
                      onChange={(e) => setProjectData({...projectData, setting: e.target.value})}
                      placeholder="Describe your story's world or location"
                      rows={2}
                      className="text-base p-4 border-2 focus:border-primary transition-colors resize-none"
                    />
                  </div>
                  <div>
                    <label className="text-base font-semibold mb-3 block">Time Period</label>
                    <Input
                      value={projectData.timePeriod}
                      onChange={(e) => setProjectData({...projectData, timePeriod: e.target.value})}
                      placeholder="e.g., Medieval, Modern Day, Future"
                      className="h-12 text-base px-4 border-2 focus:border-primary transition-colors"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex items-center justify-between pt-8 border-t border-border">
              <Button 
                variant="outline" 
                onClick={() => {
                  setActiveTab('framework');
                  setCurrentStep('framework');
                }}
                className="text-base px-6 py-6 hover:scale-105 transition-transform"
              >
                Back to Framework
              </Button>
              <Button 
                onClick={() => {
                  setActiveTab('elements');
                  setCurrentStep('elements');
                }}
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-base px-8 py-6 shadow-lg hover:shadow-xl hover:scale-105 transition-all"
              >
                Continue to Themes & Elements
                <ChevronRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
          </TabsContent>

          {/* Themes & Elements Tab */}
          <TabsContent value="elements" className="space-y-8 max-w-6xl mx-auto mt-8">
            <div>
              <h3 className="text-2xl md:text-3xl font-bold mb-4">Themes & Elements</h3>
              <p className="text-muted-foreground text-base md:text-lg mb-8 max-w-3xl">
                Select the themes, tones, and content elements that will shape your story.
              </p>
            </div>

            {/* Tones Selection */}
            <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow">
              <CardHeader className="bg-gradient-to-r from-card to-card/95 pb-6">
                <CardTitle className="text-xl md:text-2xl">Story Tones</CardTitle>
              </CardHeader>
              <CardContent className="p-6 md:p-8">
                <p className="text-sm text-muted-foreground mb-6">Select the tones that best describe your story's atmosphere</p>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                  {toneOptions.map((tone) => {
                    const isSelected = projectData.tones.includes(tone);
                    return (
                      <label
                        key={tone}
                        className={`relative flex items-center justify-center p-3 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${
                          isSelected
                            ? 'border-primary bg-primary/10 shadow-sm'
                            : 'border-border hover:border-primary/50'
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setProjectData({...projectData, tones: [...projectData.tones, tone]});
                            } else {
                              setProjectData({...projectData, tones: projectData.tones.filter(t => t !== tone)});
                            }
                          }}
                          className="sr-only"
                        />
                        <span className="text-sm font-medium">{tone}</span>
                        {isSelected && (
                          <Check className="absolute top-1 right-1 w-4 h-4 text-primary" />
                        )}
                      </label>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Themes Selection */}
            <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow">
              <CardHeader className="bg-gradient-to-r from-card to-card/95 pb-6">
                <CardTitle className="text-xl md:text-2xl">Story Themes</CardTitle>
              </CardHeader>
              <CardContent className="p-6 md:p-8">
                <p className="text-sm text-muted-foreground mb-6">Choose the themes your story will explore</p>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-3">
                  {themeOptions.map((theme) => {
                    const isSelected = projectData.themes.includes(theme);
                    return (
                      <label
                        key={theme}
                        className={`relative flex items-center justify-center p-3 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${
                          isSelected
                            ? 'border-primary bg-primary/10 shadow-sm'
                            : 'border-border hover:border-primary/50'
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setProjectData({...projectData, themes: [...projectData.themes, theme]});
                            } else {
                              setProjectData({...projectData, themes: projectData.themes.filter(t => t !== theme)});
                            }
                          }}
                          className="sr-only"
                        />
                        <span className="text-sm font-medium">{theme}</span>
                        {isSelected && (
                          <Check className="absolute top-1 right-1 w-4 h-4 text-primary" />
                        )}
                      </label>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Content Elements Selection */}
            <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow">
              <CardHeader className="bg-gradient-to-r from-card to-card/95 pb-6">
                <CardTitle className="text-xl md:text-2xl">Content Elements</CardTitle>
              </CardHeader>
              <CardContent className="p-6 md:p-8">
                <p className="text-sm text-muted-foreground mb-6">Select the narrative elements to include in your story</p>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {contentElementOptions.map((element) => {
                    const isSelected = projectData.contentElements.includes(element);
                    return (
                      <label
                        key={element}
                        className={`relative flex items-center p-3 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${
                          isSelected
                            ? 'border-primary bg-primary/10 shadow-sm'
                            : 'border-border hover:border-primary/50'
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setProjectData({...projectData, contentElements: [...projectData.contentElements, element]});
                            } else {
                              setProjectData({...projectData, contentElements: projectData.contentElements.filter(e => e !== element)});
                            }
                          }}
                          className="sr-only"
                        />
                        <span className="text-sm font-medium">{element}</span>
                        {isSelected && (
                          <Check className="absolute top-1 right-1 w-4 h-4 text-primary" />
                        )}
                      </label>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <div className="flex items-center justify-between pt-8 border-t border-border">
              <Button 
                variant="outline" 
                onClick={() => {
                  setActiveTab('world');
                  setCurrentStep('world');
                }}
                className="text-base px-6 py-6 hover:scale-105 transition-transform"
              >
                Back to World & Characters
              </Button>
              <Button 
                onClick={() => {
                  setActiveTab('review');
                  setCurrentStep('review');
                }}
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-base px-8 py-6 shadow-lg hover:shadow-xl hover:scale-105 transition-all"
              >
                Review & Create Project
                <ChevronRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
          </TabsContent>

          {/* Review & Create Tab */}
          <TabsContent value="review" className="space-y-8 max-w-6xl mx-auto mt-8">
            <div>
              <h3 className="text-2xl md:text-3xl font-bold mb-4">Review & Create</h3>
              <p className="text-muted-foreground text-base md:text-lg mb-8 max-w-3xl">
                Review your project details and create your new story.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Project Summary */}
              <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow">
                <CardHeader className="bg-gradient-to-r from-card to-card/95 pb-6">
                  <CardTitle className="text-xl md:text-2xl">Project Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6 p-6 md:p-8">
                  <div>
                    <h4 className="font-semibold mb-2">Story Foundation</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Title:</span>
                        <span className="font-medium">{projectData.title}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Genre:</span>
                        <span className="font-medium">{projectData.genre} - {projectData.subgenre}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Audience:</span>
                        <span className="font-medium">{projectData.targetAudience}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">POV:</span>
                        <span className="font-medium">{projectData.narrativeVoice} / {projectData.tense}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Technical Details</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Word Count:</span>
                        <span className="font-medium">{Number(projectData.targetLength).toLocaleString()} words</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Chapters:</span>
                        <span className="font-medium">{projectData.chapters}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Structure:</span>
                        <span className="font-medium">{projectData.structure}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Pacing:</span>
                        <span className="font-medium">{projectData.pacing}</span>
                      </div>
                      {projectData.series && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Series:</span>
                          <span className="font-medium">{projectData.seriesBooks} books planned</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Selected Themes</h4>
                    <div className="flex flex-wrap gap-2">
                      {projectData.themes.map(theme => (
                        <Badge key={theme} variant="secondary" className="text-xs">
                          {theme}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Tones</h4>
                    <div className="flex flex-wrap gap-2">
                      {projectData.tones.map(tone => (
                        <Badge key={tone} variant="outline" className="text-xs">
                          {tone}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Action Card */}
              <div className="space-y-6">
                <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow bg-gradient-to-br from-primary/5 to-primary/10">
                  <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5 pb-6">
                    <CardTitle className="text-xl md:text-2xl flex items-center gap-3">
                      <Sparkles className="w-6 h-6 text-primary animate-pulse" />
                      Ready to Create!
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 md:p-8 space-y-6">
                    <p className="text-base leading-relaxed">
                      Your project is configured and ready to be created. Once you click
                      "Create Project", BookScribe AI will:
                    </p>
                    <ul className="space-y-3">
                      <li className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                        <span className="text-sm">Generate initial story structure based on your preferences</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                        <span className="text-sm">Create character profiles and world-building elements</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                        <span className="text-sm">Set up your AI writing assistant with your chosen settings</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                        <span className="text-sm">Prepare your workspace for immediate writing</span>
                      </li>
                    </ul>
                    
                    <Button 
                      className="w-full h-14 text-lg bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl transition-all"
                    >
                      <Sparkles className="w-5 h-5 mr-2" />
                      Create Project & Start Writing
                    </Button>
                  </CardContent>
                </Card>

                <Card className="border-2 shadow-xl hover:shadow-2xl transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="w-5 h-5" />
                      AI Assistant Preview
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-4 rounded-lg bg-muted/50">
                      <p className="text-sm italic">
                        "Welcome to {projectData.title}! I'm ready to help you craft your {projectData.genre.toLowerCase()} story. 
                        Based on your preferences, I'll focus on {projectData.themes.slice(0, 2).join(' and ')} themes with 
                        a {projectData.tones[0]?.toLowerCase()} tone. Let's begin with your protagonist, {projectData.protagonist.split('-')[0].trim()}..."
                      </p>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Lightbulb className="w-4 h-4" />
                      <span>Your AI assistant will adapt to your writing style as you work</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="flex items-center justify-between pt-8 border-t border-border">
              <Button 
                variant="outline" 
                onClick={() => {
                  setActiveTab('elements');
                  setCurrentStep('elements');
                }}
                className="text-base px-6 py-6 hover:scale-105 transition-transform"
              >
                Back to Themes & Elements
              </Button>
              <Button 
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-base px-8 py-6 shadow-lg hover:shadow-xl hover:scale-105 transition-all"
              >
                <Sparkles className="w-5 h-5 mr-2" />
                Create Project
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
