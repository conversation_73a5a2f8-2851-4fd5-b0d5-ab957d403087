import * as fs from 'fs';
import * as path from 'path';

// Track all service method calls in API routes
const serviceMethodCalls = new Map<string, Set<string>>();
const serviceImplementations = new Map<string, Set<string>>();

// Find all service method calls in API routes
function findServiceCalls(dir: string) {
  const files = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const file of files) {
    const filePath = path.join(dir, file.name);
    
    if (file.isDirectory()) {
      findServiceCalls(filePath);
    } else if (file.name.endsWith('.ts') && file.name !== 'route.test.ts') {
      const content = fs.readFileSync(filePath, 'utf-8');
      
      // Find service method calls (e.g., projectService.getProject())
      const serviceCallRegex = /(\w+Service)\.(\w+)\(/g;
      let match;
      
      while ((match = serviceCallRegex.exec(content)) !== null) {
        const [, serviceName, methodName] = match;
        if (!serviceMethodCalls.has(serviceName)) {
          serviceMethodCalls.set(serviceName, new Set());
        }
        const methods = serviceMethodCalls.get(serviceName);
        if (methods) {
          methods.add(methodName);
        }
      }
    }
  }
}

// Find all service implementations
function findServiceImplementations(dir: string) {
  const files = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const file of files) {
    const filePath = path.join(dir, file.name);
    
    if (file.name.endsWith('-service.ts') && !file.name.includes('.test')) {
      const content = fs.readFileSync(filePath, 'utf-8');
      const serviceName = file.name.replace('-service.ts', '').split('-').map((word, i) => 
        i === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)
      ).join('') + 'Service';
      
      // Find method implementations
      const methodRegex = /async\s+(\w+)\s*\(/g;
      const methods = new Set<string>();
      let match;
      
      while ((match = methodRegex.exec(content)) !== null) {
        methods.add(match[1]);
      }
      
      serviceImplementations.set(serviceName, methods);
    }
  }
}

// Main verification
console.log('Verifying service method implementations...\n');

findServiceCalls(path.join(__dirname, '../src/app/api'));
findServiceImplementations(path.join(__dirname, '../src/lib/services'));

// Report missing implementations
let missingCount = 0;
const missingMethods: string[] = [];

for (const [serviceName, methods] of serviceMethodCalls) {
  const implementations = serviceImplementations.get(serviceName);
  
  if (!implementations) {
    console.log(`❌ Service not found: ${serviceName}`);
    missingCount += methods.size;
    continue;
  }
  
  for (const method of methods) {
    if (!implementations.has(method)) {
      console.log(`⚠️  Missing: ${serviceName}.${method}()`);
      missingMethods.push(`${serviceName}.${method}`);
      missingCount++;
    }
  }
}

if (missingCount === 0) {
  console.log('✅ All service methods have implementations!');
} else {
  console.log(`\n❌ Found ${missingCount} missing method implementations`);
  
  // Group by service
  const byService = missingMethods.reduce((acc, method) => {
    const [service] = method.split('.');
    if (!acc[service]) acc[service] = [];
    acc[service].push(method);
    return acc;
  }, {} as Record<string, string[]>);
  
  console.log('\nMissing methods by service:');
  for (const [service, methods] of Object.entries(byService)) {
    console.log(`\n${service}:`);
    methods.forEach(m => console.log(`  - ${m}`));
  }
}