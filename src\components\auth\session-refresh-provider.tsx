'use client'

import { useEffect } from 'react'
import { createClient } from '@/lib/supabase'
import { useRouter } from 'next/navigation'

const REFRESH_INTERVAL = 30 * 60 * 1000 // 30 minutes
const SESSION_CHECK_INTERVAL = 60 * 1000 // 1 minute

export function SessionRefreshProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter()

  useEffect(() => {
    const supabase = getBrowserClient() //)
    
    // Initial session check
    const checkSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        // Session expired, redirect to login
        router.push('/login?error=session_expired')
        return
      }
      
      // Check if session needs refresh (less than 5 minutes remaining)
      const expiresAt = new Date(session.expires_at! * 1000)
      const now = new Date()
      const timeUntilExpiry = expiresAt.getTime() - now.getTime()
      
      if (timeUntilExpiry < 5 * 60 * 1000) {
        // Refresh the session
        await supabase.auth.refreshSession()
      }
    }
    
    // Check session initially
    checkSession()
    
    // Set up periodic refresh
    const refreshInterval = setInterval(async () => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session) {
        await supabase.auth.refreshSession()
      }
    }, REFRESH_INTERVAL)
    
    // Set up periodic session check
    const checkInterval = setInterval(checkSession, SESSION_CHECK_INTERVAL)
    
    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_OUT' || (!session && event === 'TOKEN_REFRESHED')) {
        router.push('/login')
      }
    })
    
    return () => {
      clearInterval(refreshInterval)
      clearInterval(checkInterval)
      subscription.unsubscribe()
    }
  }, [router])
  
  return <>{children}</>
}