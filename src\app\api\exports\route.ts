import { NextRequest, NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { TaskService } from '@/lib/services/task-service';
import { ProjectService } from '@/lib/services/project-service';
import { handleAPIError, ValidationError, AuthenticationError, NotFoundError } from '@/lib/api/error-handler';
import { logger } from '@/lib/services/logger';
import { z } from 'zod';

const exportRequestSchema = z.object({
  type: z.enum(['project', 'series', 'chapter']),
  resourceId: z.string().min(1),
  format: z.enum(['docx', 'pdf', 'epub', 'txt', 'html', 'markdown']),
  options: z.object({
    includeChapters: z.boolean().optional().default(true),
    includeCharacters: z.boolean().optional().default(false),
    includeStoryBible: z.boolean().optional().default(false),
    includeCoverPage: z.boolean().optional().default(true),
    fontSize: z.number().min(8).max(18).optional().default(12),
    fontFamily: z.string().optional().default('Times New Roman'),
    pageSize: z.enum(['A4', 'US Letter', 'A5']).optional().default('A4'),
    margins: z.enum(['normal', 'narrow', 'wide']).optional().default('normal'),
    chapterBreaks: z.boolean().optional().default(true),
    tableOfContents: z.boolean().optional().default(true)
  }).optional().default({})
});

export async function POST(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const body = await request.json();
    const validation = exportRequestSchema.safeParse(body);

    if (!validation.success) {
      return handleAPIError(new ValidationError('Invalid export request'));
    }

    const { type, resourceId, format, options } = validation.data;

    // Initialize services
    const taskService = new TaskService();
    const projectService = new ProjectService();
    await taskService.initialize();
    await projectService.initialize();

    // Verify user has access to the resource
    if (type === 'project') {
      const projectResponse = await projectService.getProject(resourceId, user.id);
      if (!projectResponse.success) {
        return handleAPIError(new NotFoundError('Project'));
      }
    }

    // Check if user has any pending export tasks
    const pendingExportsResponse = await taskService.getUserTasks(user.id, {
      taskType: 'export_project',
      status: 'pending',
      limit: 5
    });

    if (pendingExportsResponse.success && pendingExportsResponse.data.length >= 3) {
      return NextResponse.json({
        success: false,
        error: 'Maximum number of pending exports reached. Please wait for existing exports to complete.',
        pendingExports: pendingExportsResponse.data.length
      }, { status: 429 });
    }

    // Create export task
    const taskResponse = await taskService.createTask(user.id, {
      type: 'export_project',
      payload: {
        userId: user.id,
        exportType: type,
        resourceId,
        format,
        options,
        requestedAt: new Date().toISOString()
      },
      priority: 'medium'
    });

    if (!taskResponse.success) {
      logger.error('Failed to create export task:', taskResponse.error);
      return NextResponse.json({
        success: false,
        error: 'Failed to schedule export. Please try again.'
      }, { status: 500 });
    }

    logger.info('Export task created', {
      userId: user.id,
      taskId: taskResponse.data.id,
      type,
      resourceId,
      format
    });

    return NextResponse.json({
      success: true,
      message: 'Export requested successfully',
      exportId: taskResponse.data.id,
      estimatedCompletion: getEstimatedCompletionTime(format, type),
      details: {
        type,
        resourceId,
        format,
        options,
        status: 'pending',
        requestedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Error creating export request:', error);
    return handleAPIError(error);
  }
}

export async function GET(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const searchParams = request.nextUrl.searchParams;
    const exportId = searchParams.get('id');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '10');

    const taskService = new TaskService();
    await taskService.initialize();

    if (exportId) {
      // Get specific export
      const taskResponse = await taskService.getTask(exportId);
      
      if (!taskResponse.success) {
        return handleAPIError(new NotFoundError('Export'));
      }

      const task = taskResponse.data;

      // Verify ownership
      if (task.user_id !== user.id) {
        return NextResponse.json({
          success: false,
          error: 'Access denied'
        }, { status: 403 });
      }

      const exportDetails = {
        id: task.id,
        type: task.payload?.exportType,
        resourceId: task.payload?.resourceId,
        format: task.payload?.format,
        options: task.payload?.options,
        status: task.status,
        progress: getExportProgress(task.status),
        requestedAt: task.created_at,
        completedAt: task.completed_at,
        downloadUrl: task.status === 'completed' ? task.result?.downloadUrl : null,
        fileSize: task.status === 'completed' ? task.result?.fileSize : null,
        expiresAt: task.status === 'completed' ? getExpirationDate(task.completed_at) : null,
        errorMessage: task.error_message
      };

      return NextResponse.json({
        success: true,
        export: exportDetails
      });

    } else {
      // Get user's export history
      const tasksResponse = await taskService.getUserTasks(user.id, {
        taskType: 'export_project',
        status: status || undefined,
        limit
      });

      if (!tasksResponse.success) {
        logger.error('Failed to fetch export tasks:', tasksResponse.error);
        return NextResponse.json({
          success: false,
          error: 'Failed to fetch export history'
        }, { status: 500 });
      }

      const exports = tasksResponse.data.map(task => ({
        id: task.id,
        type: task.payload?.exportType,
        resourceId: task.payload?.resourceId,
        format: task.payload?.format,
        status: task.status,
        progress: getExportProgress(task.status),
        requestedAt: task.created_at,
        completedAt: task.completed_at,
        downloadUrl: task.status === 'completed' ? task.result?.downloadUrl : null,
        fileSize: task.status === 'completed' ? task.result?.fileSize : null,
        expiresAt: task.status === 'completed' ? getExpirationDate(task.completed_at) : null
      }));

      const summary = {
        total: exports.length,
        pending: exports.filter(e => e.status === 'pending').length,
        processing: exports.filter(e => e.status === 'running').length,
        completed: exports.filter(e => e.status === 'completed').length,
        failed: exports.filter(e => e.status === 'failed').length
      };

      return NextResponse.json({
        success: true,
        exports,
        summary,
        totalCount: exports.length
      });
    }

  } catch (error) {
    logger.error('Error fetching export data:', error);
    return handleAPIError(error);
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const searchParams = request.nextUrl.searchParams;
    const exportId = searchParams.get('id');

    if (!exportId) {
      return handleAPIError(new ValidationError('Export ID required'));
    }

    const taskService = new TaskService();
    await taskService.initialize();

    // Cancel the export if it's still pending
    const cancelResponse = await taskService.cancelTask(exportId, user.id);

    if (!cancelResponse.success) {
      return NextResponse.json({
        success: false,
        error: 'Failed to cancel export. It may already be processed or not exist.'
      }, { status: 400 });
    }

    logger.info('Export cancelled', {
      userId: user.id,
      exportId
    });

    return NextResponse.json({
      success: true,
      message: 'Export cancelled successfully'
    });

  } catch (error) {
    logger.error('Error cancelling export:', error);
    return handleAPIError(error);
  }
}

function getEstimatedCompletionTime(format: string, type: string): string {
  const baseTime = {
    'txt': 30,        // 30 seconds
    'markdown': 45,   // 45 seconds
    'html': 60,       // 1 minute
    'docx': 120,      // 2 minutes
    'pdf': 300,       // 5 minutes
    'epub': 240       // 4 minutes
  };

  const typeMultiplier = {
    'chapter': 0.5,
    'project': 1.0,
    'series': 2.0
  };

  const baseSeconds = baseTime[format as keyof typeof baseTime] || 120;
  const multiplier = typeMultiplier[type as keyof typeof typeMultiplier] || 1.0;
  const totalSeconds = Math.round(baseSeconds * multiplier);

  if (totalSeconds < 60) {
    return `${totalSeconds} seconds`;
  } else if (totalSeconds < 3600) {
    const minutes = Math.round(totalSeconds / 60);
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  } else {
    const hours = Math.round(totalSeconds / 3600);
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  }
}

function getExportProgress(status: string): number {
  switch (status) {
    case 'pending': return 0;
    case 'running': return 50;
    case 'completed': return 100;
    case 'failed': return 0;
    default: return 0;
  }
}

function getExpirationDate(completedAt: string | null): string | null {
  if (!completedAt) return null;
  
  // Exports expire after 7 days
  const expirationDate = new Date(completedAt);
  expirationDate.setDate(expirationDate.getDate() + 7);
  
  return expirationDate.toISOString();
}