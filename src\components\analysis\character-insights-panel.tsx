'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { UnifiedErrorBoundary } from '@/components/error/unified-error-boundary'
import { UnifiedLoadingState } from '@/components/ui/unified-loading'
import { UnifiedEmptyState } from '@/components/ui/unified-empty-state'
import { Lightbulb } from 'lucide-react'
import { TrendingUp } from 'lucide-react'
import { AlertTriangle } from 'lucide-react'
import { CheckCircle } from 'lucide-react'
import { Info } from 'lucide-react'
import { User } from 'lucide-react'
import { RefreshCw } from 'lucide-react'
import { <PERSON><PERSON><PERSON> } from 'lucide-react'
import { Target } from 'lucide-react'
import { Heart } from 'lucide-react'
import { Brain } from 'lucide-react'
import { cn } from '@/lib/utils'
import { logger } from '@/lib/services/logger'
import { createClient } from '@/lib/supabase'

interface CharacterInsight {
  id: string
  type: 'strength' | 'weakness' | 'opportunity' | 'threat' | 'recommendation'
  category: 'arc' | 'consistency' | 'relationships' | 'pacing' | 'voice'
  title: string
  description: string
  severity?: 'low' | 'medium' | 'high'
  examples?: string[]
  actionable?: string
}

interface InsightsSummary {
  characterId: string
  characterName: string
  insights: CharacterInsight[]
  overallScore: number
  strengths: number
  improvements: number
  lastAnalyzed: string
}

interface CharacterInsightsPanelProps {
  projectId: string
  className?: string
}

const insightTypeConfig = {
  strength: {
    icon: CheckCircle,
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-100 dark:bg-green-900/20',
    label: 'Strength'
  },
  weakness: {
    icon: AlertTriangle,
    color: 'text-orange-600 dark:text-orange-400',
    bgColor: 'bg-orange-100 dark:bg-orange-900/20',
    label: 'Weakness'
  },
  opportunity: {
    icon: TrendingUp,
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-100 dark:bg-blue-900/20',
    label: 'Opportunity'
  },
  threat: {
    icon: AlertTriangle,
    color: 'text-red-600 dark:text-red-400',
    bgColor: 'bg-red-100 dark:bg-red-900/20',
    label: 'Threat'
  },
  recommendation: {
    icon: Lightbulb,
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-100 dark:bg-purple-900/20',
    label: 'Recommendation'
  }
}

const categoryIcons = {
  arc: Target,
  consistency: CheckCircle,
  relationships: Heart,
  pacing: TrendingUp,
  voice: Brain
}

export function CharacterInsightsPanel({ projectId, className }: CharacterInsightsPanelProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [characters, setCharacters] = useState<any[]>([])
  const [selectedCharacterId, setSelectedCharacterId] = useState<string>('')
  const [insights, setInsights] = useState<InsightsSummary | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  // Load characters
  useEffect(() => {
    const loadCharacters = async () => {
      try {
        const supabase = getBrowserClient() //)
        const { data, error } = await supabase
          .from('characters')
          .select('id, name, role')
          .eq('project_id', projectId)
          .order('name')

        if (error) throw error

        setCharacters(data || [])
        if (data && data.length > 0 && !selectedCharacterId) {
          setSelectedCharacterId(data[0].id)
        }
      } catch (error) {
        logger.error('Failed to load characters:', error)
      }
    }

    loadCharacters()
  }, [projectId, selectedCharacterId])

  // Load insights
  useEffect(() => {
    const loadInsights = async () => {
      if (!selectedCharacterId) return

      setIsLoading(true)
      try {
        const response = await fetch(
          `/api/analysis/character-insights?projectId=${projectId}&characterId=${selectedCharacterId}`
        )
        if (!response.ok) throw new Error('Failed to load insights')
        
        const data = await response.json()
        setInsights(data.data)
      } catch (error) {
        logger.error('Failed to load character insights:', error)
        setInsights(null)
      } finally {
        setIsLoading(false)
      }
    }

    loadInsights()
  }, [projectId, selectedCharacterId])

  const handleAnalyze = async () => {
    if (!selectedCharacterId) return

    setIsAnalyzing(true)
    try {
      const response = await fetch('/api/analysis/character-insights', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId, characterId: selectedCharacterId })
      })

      if (!response.ok) throw new Error('Analysis failed')

      const data = await response.json()
      setInsights(data.data)
    } catch (error) {
      logger.error('Failed to analyze character:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const filteredInsights = insights?.insights.filter(
    insight => selectedCategory === 'all' || insight.category === selectedCategory
  ) || []

  const renderInsightCard = (insight: CharacterInsight) => {
    const config = insightTypeConfig[insight.type]
    const Icon = config.icon
    const CategoryIcon = categoryIcons[insight.category]

    return (
      <Card key={insight.id} className="hover:shadow-lg transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className={cn("p-2 rounded-lg", config.bgColor)}>
                <Icon className={cn("w-4 h-4", config.color)} />
              </div>
              <div className="space-y-1">
                <h4 className="font-medium leading-tight">{insight.title}</h4>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs">
                    <CategoryIcon className="w-3 h-3 mr-1" />
                    {insight.category}
                  </Badge>
                  {insight.severity && (
                    <Badge 
                      variant={
                        insight.severity === 'high' ? 'destructive' :
                        insight.severity === 'medium' ? 'default' :
                        'secondary'
                      }
                      className="text-xs"
                    >
                      {insight.severity}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">
            {insight.description}
          </p>
          
          {insight.examples && insight.examples.length > 0 && (
            <div className="mb-3">
              <p className="text-xs font-medium mb-1">Examples:</p>
              <ul className="text-xs text-muted-foreground space-y-1">
                {insight.examples.map((example, idx) => (
                  <li key={idx} className="flex items-start gap-1">
                    <span className="text-muted-foreground">•</span>
                    <span>{example}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          {insight.actionable && (
            <Alert className="mt-3">
              <Sparkles className="h-4 w-4" />
              <AlertDescription className="text-xs">
                {insight.actionable}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <UnifiedErrorBoundary>
      <div className={cn("space-y-6", className)}>
        {/* Header */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="w-5 h-5" />
                AI Character Insights
              </CardTitle>
              <div className="flex items-center gap-2">
                <Select 
                  value={selectedCharacterId} 
                  onValueChange={setSelectedCharacterId}
                  disabled={characters.length === 0}
                >
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Select character" />
                  </SelectTrigger>
                  <SelectContent>
                    {characters.map(character => (
                      <SelectItem key={character.id} value={character.id}>
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4" />
                          {character.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  onClick={handleAnalyze}
                  disabled={!selectedCharacterId || isAnalyzing}
                  size="sm"
                >
                  <RefreshCw className={cn(
                    "w-4 h-4 mr-2",
                    isAnalyzing && "animate-spin"
                  )} />
                  {isAnalyzing ? 'Analyzing...' : 'Analyze'}
                </Button>
              </div>
            </div>
          </CardHeader>
          {insights && (
            <CardContent>
              <div className="grid grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{insights.overallScore}%</div>
                  <div className="text-xs text-muted-foreground">Overall Score</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{insights.strengths}</div>
                  <div className="text-xs text-muted-foreground">Strengths</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{insights.improvements}</div>
                  <div className="text-xs text-muted-foreground">Areas to Improve</div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-muted-foreground">
                    {new Date(insights.lastAnalyzed).toLocaleDateString()}
                  </div>
                  <div className="text-xs text-muted-foreground">Last Analyzed</div>
                </div>
              </div>
            </CardContent>
          )}
        </Card>

        {/* Category Filter */}
        <div className="flex items-center gap-2">
          <Button
            variant={selectedCategory === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('all')}
          >
            All
          </Button>
          {Object.entries(categoryIcons).map(([category, Icon]) => (
            <Button
              key={category}
              variant={selectedCategory === category ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory(category)}
            >
              <Icon className="w-4 h-4 mr-1" />
              {category.charAt(0).toUpperCase() + category.slice(1)}
            </Button>
          ))}
        </div>

        {/* Insights Grid */}
        {isLoading ? (
          <UnifiedLoadingState message="Loading character insights..." />
        ) : filteredInsights.length > 0 ? (
          <div className="grid md:grid-cols-2 gap-4">
            {filteredInsights.map(insight => renderInsightCard(insight))}
          </div>
        ) : (
          <UnifiedEmptyState
            title="No insights available"
            description="Click 'Analyze' to generate AI-powered insights for this character"
            icon={Lightbulb}
            action={
              <Button onClick={handleAnalyze} disabled={!selectedCharacterId}>
                Generate Insights
              </Button>
            }
          />
        )}
      </div>
    </UnifiedErrorBoundary>
  )
}