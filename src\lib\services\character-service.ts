import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { ServiceBase, ServiceResponse } from './base-service';
import { Database } from '@/lib/db/database.types';

type Character = Database['public']['Tables']['characters']['Row'];
type CharacterInsert = Database['public']['Tables']['characters']['Insert'];
type CharacterUpdate = Database['public']['Tables']['characters']['Update'];

export class CharacterService extends ServiceBase {
  constructor() {
    super({
      name: 'character-service',
      version: '1.0.0',
      endpoints: ['/api/characters'],
      dependencies: [],
      healthCheck: '/api/services/character/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, { status: 'healthy' });
  }

  /**
   * Get all characters for a project
   */
  async getProjectCharacters(projectId: string): Promise<ServiceResponse<Character[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('characters')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: true });
      
      if (error) {
        logger.error('[CharacterService] Error fetching characters:', error);
        throw error;
      }
      
      return data || [];
    });
  }

  /**
   * Get a single character
   */
  async getCharacter(characterId: string): Promise<ServiceResponse<Character>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('characters')
        .select('*')
        .eq('id', characterId)
        .single();
      
      if (error) {
        logger.error('[CharacterService] Error fetching character:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Character not found');
      }
      
      return data;
    });
  }

  /**
   * Create a new character
   */
  async createCharacter(characterData: CharacterInsert): Promise<ServiceResponse<Character>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('characters')
        .insert({
          ...characterData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        logger.error('[CharacterService] Error creating character:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Failed to create character');
      }
      
      return data;
    });
  }

  /**
   * Create multiple characters
   */
  async createCharacters(charactersData: CharacterInsert[]): Promise<ServiceResponse<Character[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const charactersWithTimestamps = charactersData.map(character => ({
        ...character,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));
      
      const { data, error } = await supabase
        .from('characters')
        .insert(charactersWithTimestamps)
        .select();
      
      if (error) {
        logger.error('[CharacterService] Error creating characters:', error);
        throw error;
      }
      
      return data || [];
    });
  }


  /**
   * Delete all characters for a project
   */
  async deleteProjectCharacters(projectId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from('characters')
        .delete()
        .eq('project_id', projectId);
      
      if (error) {
        logger.error('[CharacterService] Error deleting project characters:', error);
        throw error;
      }
      
      return true;
    });
  }

  /**
   * Get character count for a project
   */
  async getCharacterCount(projectId: string): Promise<ServiceResponse<number>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { count, error } = await supabase
        .from('characters')
        .select('id', { count: 'exact', head: true })
        .eq('project_id', projectId);
      
      if (error) {
        logger.error('[CharacterService] Error counting characters:', error);
        throw error;
      }
      
      return count || 0;
    });
  }

  /**
   * Get characters for multiple projects with filters
   */
  async getCharactersForProjects(
    projectIds: string[],
    filters?: {
      project_id?: string;
      role?: string;
      search?: string;
      limit?: number;
      offset?: number;
    }
  ): Promise<ServiceResponse<{ characters: Character[]; total: number }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Build query
      let query = supabase
        .from('characters')
        .select('*', { count: 'exact' })
        .in('project_id', projectIds);
      
      // Apply filters
      if (filters?.project_id && projectIds.includes(filters.project_id)) {
        query = query.eq('project_id', filters.project_id);
      }
      if (filters?.role) {
        query = query.eq('role', filters.role);
      }
      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }
      
      // Apply pagination
      const limit = filters?.limit || 50;
      const offset = filters?.offset || 0;
      
      if (filters?.limit) {
        query = query.limit(limit);
      }
      if (filters?.offset) {
        query = query.range(offset, offset + limit - 1);
      }
      
      // Order by creation date
      query = query.order('created_at', { ascending: false });
      
      const { data, error, count } = await query;
      
      if (error) {
        logger.error('[CharacterService] Error fetching characters for projects:', error);
        throw error;
      }
      
      return { characters: data || [], total: count || 0 };
    });
  }

  /**
   * Check if character_id exists in project
   */
  async checkCharacterIdExists(projectId: string, characterId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('characters')
        .select('id')
        .eq('project_id', projectId)
        .eq('character_id', characterId)
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        logger.error('[CharacterService] Error checking character ID:', error);
        throw error;
      }
      
      return !!data;
    });
  }

  /**
   * Get character by ID
   */
  async getCharacterById(characterId: string): Promise<ServiceResponse<Character>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('characters')
        .select('*')
        .eq('id', characterId)
        .single();
      
      if (error) {
        if (error.code === 'PGRST116') {
          throw new Error('Character not found');
        }
        logger.error('[CharacterService] Error fetching character by ID:', error);
        throw error;
      }
      
      if (!data) {
        throw new Error('Character not found');
      }
      
      return data;
    });
  }

  /**
   * Update character
   */
  async updateCharacter(characterId: string, updates: Partial<CharacterUpdate>): Promise<ServiceResponse<Character>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { data, error } = await supabase
        .from('characters')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', characterId)
        .select()
        .single();
      
      if (error) {
        logger.error('[CharacterService] Error updating character:', error);
        
        if (error.code === '23505') { // Unique constraint violation
          throw new Error('A character with this name already exists');
        }
        
        throw error;
      }
      
      if (!data) {
        throw new Error('Failed to update character');
      }
      
      return data;
    });
  }

  /**
   * Delete character
   */
  async deleteCharacter(characterId: string): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      const { error } = await supabase
        .from('characters')
        .delete()
        .eq('id', characterId);
      
      if (error) {
        logger.error('[CharacterService] Error deleting character:', error);
        
        if (error.code === '23503') { // Foreign key constraint violation
          throw new Error('Cannot delete character: it is referenced by other data');
        }
        
        throw error;
      }
      
      return true;
    });
  }
}