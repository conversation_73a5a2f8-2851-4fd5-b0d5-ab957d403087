import { NextRequest, NextResponse } from 'next/server'
import { TaskService } from '@/lib/services/task-service'
import { logger } from '@/lib/services/logger'

// This endpoint should be called by a cron job (e.g., Vercel Cron or external service)  
// Run daily at 9 AM: 0 9 * * *
export async function GET(request: NextRequest) {
  try {
    // Verify cron secret
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const taskService = new TaskService()
    await taskService.initialize()

    // Check writing goals and create notification tasks
    const notificationCountResponse = await taskService.checkWritingGoals()
    
    if (!notificationCountResponse.success) {
      logger.error('Failed to check writing goals via TaskService:', notificationCountResponse.error)
      return NextResponse.json(
        { error: 'Failed to check writing goals' },
        { status: 500 }
      )
    }

    const notificationCount = notificationCountResponse.data

    logger.info('Writing goals checked via cron', {
      notificationsCreated: notificationCount,
      timestamp: new Date().toISOString()
    })

    return NextResponse.json({ 
      success: true,
      message: 'Writing goals checked successfully',
      notificationsCreated: notificationCount,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    logger.error('Error checking writing goals:', error)
    return NextResponse.json(
      { error: 'Failed to check writing goals' },
      { status: 500 }
    )
  }
}

// Also export POST for manual triggering
export async function POST(request: NextRequest) {
  return GET(request)
}