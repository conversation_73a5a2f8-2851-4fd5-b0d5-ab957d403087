# Service Layer Migration Plan

## Executive Summary

The comprehensive analysis found **446 service layer violations** across **156 API route files**. This plan outlines a systematic approach to refactor these routes to use the proper service layer architecture.

## Current State

### Issue Breakdown
- **Direct Database Queries**: 288 (64.6%)
- **Missing Service Imports**: 151 (33.9%)
- **Complex Data Transformations**: 7 (1.6%)
- **Business Logic in Routes**: 0 (0%)

### Highest Priority Files (Complexity Score > 40)
1. **agents/generate/route.ts** - 64 complexity, 22 issues
2. **story-bible/update/route.ts** - 42 complexity, 14 issues

### Files Requiring Manual Review: 16
These files have complex business logic that requires careful refactoring.

## Migration Strategy

### Phase 1: Critical Path Routes (Week 1)
Focus on the most used and complex routes first:

1. **AI Generation Routes** (`/api/agents/*`)
   - High impact on user experience
   - Complex orchestration logic
   - 22+ direct database queries

2. **Project Management Routes** (`/api/projects/*`)
   - Core functionality
   - Multiple table joins
   - 15+ issues per route

3. **Chapter Management Routes** (`/api/chapters/*`)
   - Frequent usage
   - Content generation integration
   - 10+ issues per route

### Phase 2: Data Management Routes (Week 2)
Refactor routes handling data operations:

1. **Story Bible Routes** (`/api/story-bible/*`)
   - Complex data structures
   - Multiple table updates
   - Context management integration

2. **Character Routes** (`/api/characters/*`)
   - Character development logic
   - Relationship management
   - Voice profile integration

3. **Analytics Routes** (`/api/analytics/*`)
   - Aggregation queries
   - Performance critical
   - Reporting logic

### Phase 3: Supporting Features (Week 3)
Complete remaining routes:

1. **Collaboration Routes** (`/api/collaboration/*`)
2. **Search Routes** (`/api/search/*`)
3. **Series Management** (`/api/series/*`)
4. **User Settings** (`/api/user/*`)

## Implementation Approach

### For Each Route:

1. **Analyze Current Implementation**
   - Identify all database queries
   - Map to appropriate services
   - Document business logic

2. **Update Service Layer**
   - Add missing service methods
   - Ensure proper error handling
   - Implement caching where appropriate

3. **Refactor Route**
   - Import ServiceManager
   - Replace direct queries with service calls
   - Move business logic to services
   - Maintain API contract

4. **Test Thoroughly**
   - Unit tests for new service methods
   - Integration tests for API routes
   - Performance comparison

### Service Mapping Guide

| Database Table | Service | Primary Methods |
|----------------|---------|-----------------|
| projects | ContentGenerator | getUserProjects, createProject, updateProject |
| chapters | ContentGenerator | getChapters, generateChapter, updateChapter |
| characters | ContentGenerator | getCharacters, createCharacter, developCharacter |
| story_bible | ContextManager | getStoryBible, updateStoryBible |
| writing_sessions | AnalyticsEngine | trackWritingSession, getWritingStats |
| collaboration_sessions | CollaborationService | createSession, joinSession |
| content_embeddings | SemanticSearch | indexContent, search |
| agent_logs | AIOrchestrator | logExecution, getAgentPerformance |

## Code Patterns

### Before (Direct Query):
```typescript
const { data: project } = await supabase
  .from('projects')
  .select('*')
  .eq('id', projectId)
  .single()
```

### After (Service Layer):
```typescript
const serviceManager = ServiceManager.getInstance()
const contentGenerator = await serviceManager.getContentGenerator()
const project = await contentGenerator.getProject(projectId)
```

## Success Metrics

1. **Zero Direct Database Queries** in API routes
2. **100% Service Layer Coverage** for business logic
3. **Improved Performance** through service-level caching
4. **Better Testability** with mockable services
5. **Consistent Error Handling** across all routes

## Risk Mitigation

1. **Gradual Migration**: One route at a time
2. **Feature Flags**: Toggle between old/new implementations
3. **Comprehensive Testing**: Before and after each change
4. **Performance Monitoring**: Track response times
5. **Rollback Plan**: Git commits for each route

## Next Steps

1. ✅ Run comprehensive analysis (COMPLETE)
2. ✅ Create migration plan (THIS DOCUMENT)
3. ⏳ Begin Phase 1 migration
4. ⏳ Create/update service methods as needed
5. ⏳ Write integration tests
6. ⏳ Deploy and monitor

## Resources

- Service Layer Guide: `src/lib/api/service-layer-guide.ts`
- Optimized Query Patterns: `src/lib/db/optimized-queries.ts`
- Service Manager: `src/lib/services/service-manager.ts`
- Comprehensive Analysis: `comprehensive-service-layer-report.md`

## Timeline

- **Week 1**: Phase 1 (Critical Routes)
- **Week 2**: Phase 2 (Data Management)
- **Week 3**: Phase 3 (Supporting Features)
- **Week 4**: Testing, optimization, and documentation

This migration will significantly improve code maintainability, testability, and performance while establishing a solid architectural foundation for future development.