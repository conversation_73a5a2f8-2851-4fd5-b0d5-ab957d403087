import { NextRequest, NextResponse } from 'next/server'
import { handleAPIError, ValidationError, AuthenticationError, AuthorizationError, NotFoundError } from '@/lib/api/error-handler';
import { createTypedServerClient } from '@/lib/supabase'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/db/types'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { baseSchemas } from '@/lib/validation/common-schemas'

const inviteCollaboratorSchema = z.object({
  projectId: baseSchemas.uuid,
  email: baseSchemas.email,
  role: z.enum(['viewer', 'commenter', 'editor', 'admin']),
  message: baseSchemas.description.max(500).optional(),
})

const collaboratorQuerySchema = z.object({
  projectId: baseSchemas.uuid.optional(),
  status: z.enum(['pending', 'accepted', 'rejected', 'removed']).optional()
})

export const GET = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const { searchParams } = new URL(request.url);
  const queryParams = {
    projectId: searchParams.get('projectId'),
    status: searchParams.get('status')
  };

  // Validate query parameters
  const parseResult = collaboratorQuerySchema.safeParse(queryParams);
  if (!parseResult.success) {
    return UnifiedResponse.error('Invalid query parameters', 400, parseResult.error.errors);
  }

  const { projectId, status } = parseResult.data;

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'collaboration-list',
    rateLimitCost: 2,
    maxRequestSize: 1024,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      if (projectId) {
        const supabase = await createTypedServerClient();
        const { data: project } = await supabase
          .from('projects')
          .select('user_id')
          .eq('id', projectId)
          .single();

        if (!project) {
          return { valid: false, error: 'Project not found' };
        }

        // Check if user is the owner or a collaborator
        const isOwner = project.user_id === user.id;
        
        if (!isOwner) {
          const { data: collaborator } = await supabase
            .from('project_collaborators')
            .select('role')
            .eq('project_id', projectId)
            .eq('user_id', user.id)
            .eq('status', 'active')
            .single();

          if (!collaborator) {
            return { valid: false, error: 'Access denied to this project' };
          }
        }
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;

  try {
    const supabase = await createTypedServerClient();

    if (!projectId) {
      // Get all collaborations for the current user
      const { data: userEmail } = await supabase
        .from('users')
        .select('email')
        .eq('id', user.id)
        .single();

      if (!userEmail?.email) {
        return UnifiedResponse.error('User email not found', 500);
      }

      let query = supabase
        .from('project_collaborators')
        .select('*, projects(title, description), users!project_collaborators_invited_by_fkey(email)')
        .or(`user_id.eq.${user.id},user_email.eq.${userEmail.email}`)
        .order('created_at', { ascending: false })

      if (status) {
        query = query.eq('status', status)
      }

      const { data: invitations, error } = await query

      if (error) {
        logger.error('Error fetching invitations:', error, {
          userId: user.id,
          clientIP: context.clientIP
        });
        return UnifiedResponse.error('Failed to fetch invitations');
      }

      logger.info('Retrieved user invitations', {
        userId: user.id,
        count: invitations?.length || 0,
        status,
        clientIP: context.clientIP
      });

      return UnifiedResponse.success({ invitations: invitations || [] })
    } else {
      // Get collaborators for a specific project (already validated in customValidator)
      const { data: project } = await supabase
        .from('projects')
        .select('user_id')
        .eq('id', projectId)
        .single();

      const isOwner = project?.user_id === user.id;

      const { data: collaborators, error } = await supabase
        .from('project_collaborators')
        .select('*, users!project_collaborators_user_id_fkey(email, full_name)')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false })

      if (error) {
        logger.error('Error fetching collaborators:', error, {
          userId: user.id,
          projectId,
          clientIP: context.clientIP
        });
        return UnifiedResponse.error('Failed to fetch collaborators');
      }

      logger.info('Retrieved project collaborators', {
        userId: user.id,
        projectId,
        count: collaborators?.length || 0,
        isOwner,
        clientIP: context.clientIP
      });

      return UnifiedResponse.success({ 
        collaborators: collaborators || [],
        isOwner 
      });
    }
  } catch (error) {
    logger.error('Error in project collaborators GET:', error, {
      userId: user.id,
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to fetch collaborators');
  }
});

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: inviteCollaboratorSchema,
    rateLimitKey: 'collaboration-invite',
    rateLimitCost: 5,
    maxBodySize: 2 * 1024, // 2KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      const body = await req.json();
      const { projectId, email } = body;

      // Get user's email
      const supabase = await createTypedServerClient();
      const { data: userData } = await supabase
        .from('users')
        .select('email')
        .eq('id', user.id)
        .single();

      // Check if user is inviting themselves
      if (userData?.email === email) {
        return { valid: false, error: 'Cannot invite yourself to a project' };
      }

      // Verify user owns the project or has admin role
      const { data: project } = await supabase
        .from('projects')
        .select('user_id, status')
        .eq('id', projectId)
        .single();

      if (!project) {
        return { valid: false, error: 'Project not found' };
      }

      if (project.status === 'archived') {
        return { valid: false, error: 'Cannot invite to archived project' };
      }

      const isOwner = project.user_id === user.id;
      
      if (!isOwner) {
        // Check if user has admin permissions
        const { data: collaborator } = await supabase
          .from('project_collaborators')
          .select('role')
          .eq('project_id', projectId)
          .eq('user_id', user.id)
          .eq('status', 'active')
          .single();
        
        if (!collaborator || !['admin', 'editor'].includes(collaborator.role)) {
          return { valid: false, error: 'Only project owner or admins can invite collaborators' };
        }
      }

      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { projectId, email, role, message } = context.body;

  try {
    const supabase = await createTypedServerClient();

    // Get project details (already validated)
    const { data: project } = await supabase
      .from('projects')
      .select('id, title')
      .eq('id', projectId)
      .single();

    if (!project) {
      return UnifiedResponse.error('Project not found', 404);
    }

    // Check if invitation already exists
    const { data: existing } = await supabase
      .from('project_collaborators')
      .select('id, status')
      .eq('project_id', projectId)
      .eq('user_email', email)
      .single();

    if (existing) {
      if (existing.status === 'active') {
        return UnifiedResponse.error('User is already a collaborator on this project', 409);
      } else if (existing.status === 'pending') {
        return UnifiedResponse.error('An invitation is already pending for this user', 409);
      } else if (existing.status === 'removed') {
        // Reactivate the collaboration with new role
        const { error: updateError } = await supabase
          .from('project_collaborators')
          .update({
            role,
            status: 'pending',
            reinvited_at: new Date().toISOString(),
            reinvited_by: user.id,
            invitation_message: message
          })
          .eq('id', existing.id);

        if (updateError) {
          throw updateError;
        }

        logger.info('Collaborator reinvited', {
          projectId,
          email,
          role,
          collaboratorId: existing.id,
          clientIP: context.clientIP
        });

        return UnifiedResponse.success({
          message: 'Collaborator has been reinvited to the project',
          collaboratorId: existing.id
        });
      }
    }

    // Find the invited user's ID if they exist
    const { data: invitedUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single()

    // Create the invitation
    const { data: invitation, error: inviteError } = await supabase
      .from('project_collaborators')
      .insert({
        project_id: projectId,
        user_id: invitedUser?.id,
        user_email: email,
        role,
        status: 'pending',
        invited_by: user.id,
        invitation_message: message,
      })
      .select()
      .single()

    if (inviteError) {
      logger.error('Error creating invitation:', inviteError, {
        userId: user.id,
        projectId,
        email,
        clientIP: context.clientIP
      });
      throw inviteError;
    }

    // Create notification for the invited user if they exist
    if (invitedUser) {
      await supabase
        .from('notifications')
        .insert({
          user_id: invitedUser.id,
          type: 'collaboration_invite',
          title: 'Project Collaboration Invitation',
          message: `You've been invited to collaborate on "${project.title}"`,
          data: { 
            project_id: projectId,
            invitation_id: invitation.id,
            role 
          },
        })
    }

    // Send email invitation if user doesn't exist in the system
    if (!invitedUser && process.env.EMAIL_SERVICE_URL) {
      // Queue email notification for external users
      fetch(`${process.env.EMAIL_SERVICE_URL}/send-invitation`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.EMAIL_SERVICE_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          to: email,
          inviterName: user.email,
          projectTitle: project.title,
          invitationId: invitation.id,
          role: role,
          message: message
        })
      }).catch(err => {
        logger.error('Failed to send email invitation:', err)
      })
    }

    logger.info('Collaboration invitation created', {
      projectId,
      email,
      role,
      inviterId: user.id,
      invitationId: invitation.id,
      hasMessage: !!message,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({
      invitation,
      message: 'Invitation sent successfully'
    });
  } catch (error) {
    logger.error('Error in project collaborators POST:', error, {
      userId: user.id,
      projectId,
      email,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to create invitation');
  }
});

export const DELETE = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  const { searchParams } = new URL(request.url);
  const collaboratorId = searchParams.get('id');

  if (!collaboratorId || !baseSchemas.uuid.safeParse(collaboratorId).success) {
    return UnifiedResponse.error('Valid collaborator ID required', 400);
  }

  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'collaboration-remove',
    rateLimitCost: 3,
    maxRequestSize: 1024,
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };

      const supabase = await createTypedServerClient();

      // Get the collaboration details
      const { data: collaboration, error: fetchError } = await supabase
        .from('project_collaborators')
        .select('project_id, user_id, user_email, projects(user_id)')
        .eq('id', collaboratorId)
        .single();

      if (fetchError || !collaboration) {
        return { valid: false, error: 'Collaboration not found' };
      }

      // Check if user is the project owner or the collaborator themselves
      const projectData = collaboration.projects as { user_id: string };
      const isOwner = projectData?.user_id === user.id;
      const isSelf = collaboration.user_id === user.id;

      if (!isOwner && !isSelf) {
        // Check if user has admin permissions
        const { data: userCollab } = await supabase
          .from('project_collaborators')
          .select('role')
          .eq('project_id', collaboration.project_id)
          .eq('user_id', user.id)
          .eq('status', 'active')
          .single();
        
        if (!userCollab || userCollab.role !== 'admin') {
          return { valid: false, error: 'Only project owner, admins, or the collaborator themselves can remove collaborators' };
        }
      }

      return { valid: true, data: { collaboration, isOwner, isSelf } };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { collaboration, isOwner, isSelf } = context.customData as { collaboration: Collaboration; isOwner: boolean; isSelf: boolean };

  try {
    const supabase = await createTypedServerClient();

    // Instead of deleting, mark as removed for audit trail
    const { error: updateError } = await supabase
      .from('project_collaborators')
      .update({
        status: 'removed',
        removed_at: new Date().toISOString(),
        removed_by: user.id
      })
      .eq('id', collaboratorId);

    if (updateError) {
      logger.error('Error removing collaboration:', updateError, {
        collaboratorId,
        userId: user.id,
        clientIP: context.clientIP
      });
      throw updateError;
    }

    // If collaborator is currently active, broadcast their removal
    if (collaboration.user_id) {
      await supabase
        .from('user_presence')
        .update({
          status: 'offline',
          is_writing: false,
          last_seen: new Date().toISOString()
        })
        .eq('user_id', collaboration.user_id)
        .eq('project_id', collaboration.project_id);
    }

    logger.info('Collaborator removed', {
      collaboratorId,
      projectId: collaboration.project_id,
      removedBy: user.id,
      isSelf,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({ 
      message: isSelf ? 'Left project successfully' : 'Collaborator removed',
      projectId: collaboration.project_id
    });
  } catch (error) {
    logger.error('Error in project collaborators DELETE:', error, {
      collaboratorId,
      userId: user.id,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to remove collaborator');
  }
});