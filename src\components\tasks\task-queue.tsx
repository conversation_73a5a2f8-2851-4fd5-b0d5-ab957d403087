'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { RefreshCw } from 'lucide-react'
import { Clock } from 'lucide-react'
import { CheckCircle } from 'lucide-react'
import { XCircle } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { Loader2 } from 'lucide-react'
import { FileText } from 'lucide-react'
import { Bot } from 'lucide-react'
import { Download } from 'lucide-react'
import { Database } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { TaskStatus, TaskPriority } from '@/lib/services/task-queue-service'

interface Task {
  id: string
  type: string
  status: TaskStatus
  priority: TaskPriority
  attempts: number
  max_attempts: number
  error?: string
  created_at: string
  started_at?: string
  completed_at?: string
  project_id?: string
}

interface TaskQueueProps {
  userId?: string
  projectId?: string
  showAllTasks?: boolean
}

export function TaskQueue({ userId, projectId, showAllTasks = false }: TaskQueueProps) {
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const fetchTasks = async () => {
    try {
      setRefreshing(true)
      const params = new URLSearchParams()
      if (userId && !showAllTasks) params.append('userId', userId)
      if (projectId) params.append('projectId', projectId)
      
      const response = await fetch(`/api/processing-tasks?${params}`)
      if (response.ok) {
        const data = await response.json()
        setTasks(data.tasks || [])
      }
    } catch (error) {
      console.error('Error fetching tasks:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  useEffect(() => {
    fetchTasks()
    
    // Auto-refresh every 5 seconds
    const interval = setInterval(fetchTasks, 5000)
    return () => clearInterval(interval)
  }, [userId, projectId, showAllTasks])

  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'generate_embeddings':
      case 'analyze_content':
        return <Bot className="h-4 w-4" />
      case 'export_docx':
      case 'export_pdf':
      case 'export_epub':
        return <Download className="h-4 w-4" />
      case 'extract_text':
        return <FileText className="h-4 w-4" />
      case 'backup_project':
      case 'compress_memory':
        return <Database className="h-4 w-4" />
      default:
        return <AlertCircle className="h-4 w-4" />
    }
  }

  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.COMPLETED:
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case TaskStatus.FAILED:
        return <XCircle className="h-4 w-4 text-red-500" />
      case TaskStatus.PROCESSING:
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case TaskStatus.CANCELLED:
        return <XCircle className="h-4 w-4 text-gray-500" />
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />
    }
  }

  const getPriorityBadge = (priority: TaskPriority) => {
    const variants = {
      [TaskPriority.LOW]: 'secondary',
      [TaskPriority.NORMAL]: 'default',
      [TaskPriority.HIGH]: 'warning',
      [TaskPriority.CRITICAL]: 'destructive'
    }
    
    const labels = {
      [TaskPriority.LOW]: 'Low',
      [TaskPriority.NORMAL]: 'Normal',
      [TaskPriority.HIGH]: 'High',
      [TaskPriority.CRITICAL]: 'Critical'
    }
    
    return (
      <Badge variant={variants[priority] as any}>
        {labels[priority]}
      </Badge>
    )
  }

  const formatTaskType = (type: string) => {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Task Queue</CardTitle>
            <CardDescription>
              Background tasks and processing jobs
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchTasks}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {tasks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No tasks in queue
          </div>
        ) : (
          <ScrollArea className="h-[400px]">
            <div className="space-y-4">
              {tasks.map((task) => (
                <div
                  key={task.id}
                  className="flex items-start space-x-4 p-4 border rounded-lg"
                >
                  <div className="flex-shrink-0 mt-1">
                    {getTaskIcon(task.type)}
                  </div>
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">
                        {formatTaskType(task.type)}
                      </h4>
                      <div className="flex items-center space-x-2">
                        {getPriorityBadge(task.priority)}
                        {getStatusIcon(task.status)}
                      </div>
                    </div>
                    
                    {task.status === TaskStatus.PROCESSING && (
                      <Progress 
                        value={((task.attempts - 1) / task.max_attempts) * 100} 
                        className="h-2"
                      />
                    )}
                    
                    {task.error && (
                      <p className="text-xs text-red-500">
                        Error: {task.error}
                      </p>
                    )}
                    
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                      <span>
                        Created {formatDistanceToNow(new Date(task.created_at))} ago
                      </span>
                      {task.attempts > 0 && (
                        <span>
                          Attempt {task.attempts}/{task.max_attempts}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  )
}