import { NextRequest, NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware';
import { GDPRService, userConsentSchema } from '@/lib/privacy/gdpr-service';
import { logger } from '@/lib/services/logger';

export const GET = UnifiedAuthService.withAuth(async (request) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'authenticated',
    rateLimitCost: 1,
    maxRequestSize: 1024
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;

  try {
    const user = request.user!;
    
    // Get current consent status
    const consent = await GDPRService.getConsentStatus(user.id);

    logger.info('Consent status retrieved', {
      userId: user.id,
      hasConsent: !!consent,
      clientIP: context.clientIP
    });

    return NextResponse.json({
      success: true,
      consent: consent || {
        userId: user.id,
        marketing: false,
        analytics: false,
        thirdPartySharing: false,
        aiDataProcessing: true, // Default to true for core functionality
        performanceTracking: false,
        consentedAt: null
      },
      requiresUpdate: !consent || isConsentOutdated(consent)
    });

  } catch (error) {
    logger.error('Failed to retrieve consent status', {
      error,
      userId: request.user?.id,
      clientIP: context.clientIP
    });

    return NextResponse.json(
      { error: 'Failed to retrieve consent status' },
      { status: 500 }
    );
  }
});

export const POST = UnifiedAuthService.withAuth(async (request) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: userConsentSchema,
    rateLimitKey: 'authenticated',
    rateLimitCost: 3,
    maxBodySize: 2 * 1024, // 2KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;

  try {
    const user = request.user!;
    const consentData = context.body;

    // Update consent
    const updatedConsent = await GDPRService.updateConsent(
      user.id,
      consentData,
      context.clientIP,
      context.userAgent
    );

    // Log significant consent changes
    if (!consentData.analytics || !consentData.marketing) {
      logger.info('User opted out of data collection', {
        userId: user.id,
        analytics: consentData.analytics,
        marketing: consentData.marketing,
        clientIP: context.clientIP
      });
    }

    return NextResponse.json({
      success: true,
      consent: updatedConsent,
      message: 'Your privacy preferences have been updated successfully.'
    });

  } catch (error) {
    logger.error('Failed to update consent', {
      error,
      userId: request.user?.id,
      clientIP: context.clientIP,
      requestBody: context.body
    });

    return NextResponse.json(
      { error: 'Failed to update consent preferences' },
      { status: 500 }
    );
  }
});

export const DELETE = UnifiedAuthService.withAuth(async (request) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    rateLimitKey: 'authenticated',
    rateLimitCost: 5,
    maxRequestSize: 1024,
    validateCSRF: true
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;

  try {
    const user = request.user!;

    // Withdraw all consent (equivalent to opting out of everything)
    const withdrawnConsent = await GDPRService.updateConsent(
      user.id,
      {
        marketing: false,
        analytics: false,
        thirdPartySharing: false,
        aiDataProcessing: false,
        performanceTracking: false
      },
      context.clientIP,
      context.userAgent
    );

    logger.info('All consent withdrawn', {
      userId: user.id,
      clientIP: context.clientIP
    });

    return NextResponse.json({
      success: true,
      consent: withdrawnConsent,
      message: 'All data processing consent has been withdrawn. Note that some features may be limited.',
      warning: 'AI-powered features will be disabled without data processing consent.'
    });

  } catch (error) {
    logger.error('Failed to withdraw consent', {
      error,
      userId: request.user?.id,
      clientIP: context.clientIP
    });

    return NextResponse.json(
      { error: 'Failed to withdraw consent' },
      { status: 500 }
    );
  }
});

// Helper function to check if consent needs renewal
function isConsentOutdated(consent: { consentedAt: string | null }): boolean {
  if (!consent.consentedAt) return true;
  
  const consentDate = new Date(consent.consentedAt);
  const yearAgo = new Date();
  yearAgo.setFullYear(yearAgo.getFullYear() - 1);
  
  // Consent older than 1 year should be renewed
  return consentDate < yearAgo;
}