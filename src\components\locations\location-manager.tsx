'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import { MapPin } from 'lucide-react'
import { Plus } from 'lucide-react'
import { Search } from 'lucide-react'
import { Edit } from 'lucide-react'
import { Trash2 } from 'lucide-react'
import { ChevronRight } from 'lucide-react'
import { ChevronDown } from 'lucide-react'
import { Building } from 'lucide-react'
import { Globe } from 'lucide-react'
import { Home } from 'lucide-react'
import { Mountain } from 'lucide-react'
import { Trees } from 'lucide-react'
import { Loader2 } from 'lucide-react'
import { Filter } from 'lucide-react'
import { Eye } from 'lucide-react'
import { EyeOff } from 'lucide-react'
import { LocationTreeView } from './location-tree-view'
import { LocationMapView } from './location-map-view'
import { CreateLocationDialog } from './create-location-dialog'
import { LocationDetailsPanel } from './location-details-panel'
import { ComponentErrorBoundary } from '@/components/error/unified-error-boundary'
import type { Location, LocationWithHierarchy, LocationNode } from './types'

// Location interface moved to ./types.ts to prevent circular dependencies

interface LocationManagerProps {
  projectId: string
  seriesId?: string
  universeId?: string
}

const LOCATION_TYPE_ICONS = {
  world: Globe,
  continent: Mountain,
  country: Trees,
  region: Mountain,
  city: Building,
  building: Home,
  room: Home,
  other: MapPin
}

const LOCATION_TYPE_COLORS = {
  world: 'text-blue-600 bg-blue-50',
  continent: 'text-green-600 bg-green-50',
  country: 'text-purple-600 bg-purple-50',
  region: 'text-orange-600 bg-orange-50',
  city: 'text-red-600 bg-red-50',
  building: 'text-yellow-600 bg-yellow-50',
  room: 'text-gray-600 bg-gray-50',
  other: 'text-indigo-600 bg-indigo-50'
}

export function LocationManager({ projectId, seriesId, universeId }: LocationManagerProps) {
  const [locations, setLocations] = useState<Location[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<'all' | Location['location_type']>('all')
  const [showShareableOnly, setShowShareableOnly] = useState(false)
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [parentLocationForCreate, setParentLocationForCreate] = useState<Location | null>(null)
  const [viewMode, setViewMode] = useState<'tree' | 'list' | 'map'>('tree')
  const { toast } = useToast()

  const loadLocations = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/projects/${projectId}/locations`)
      if (!response.ok) {
        throw new Error('Failed to load locations')
      }
      
      const data = await response.json()
      setLocations(data.locations || [])
    } catch (error) {
      logger.error('Error loading locations:', error)
      toast({
        title: 'Error',
        description: 'Failed to load locations',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [projectId, toast])

  useEffect(() => {
    loadLocations()
  }, [loadLocations])

  const filteredLocations = locations.filter(location => {
    // Search filter
    if (searchQuery && !location.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !location.description?.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false
    }
    
    // Type filter
    if (filterType !== 'all' && location.location_type !== filterType) {
      return false
    }
    
    // Shareable filter
    if (showShareableOnly && !location.is_shareable) {
      return false
    }
    
    return true
  })

  const handleCreateLocation = async (locationData: any) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/locations`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...locationData,
          parentLocationId: parentLocationForCreate?.id,
          seriesId,
          universeId
        })
      })
      
      if (!response.ok) {
        throw new Error('Failed to create location')
      }
      
      const result = await response.json()
      setLocations(prev => [...prev, result.location])
      setShowCreateDialog(false)
      setParentLocationForCreate(null)
      
      toast({
        title: 'Success',
        description: 'Location created successfully'
      })
    } catch (error) {
      logger.error('Error creating location:', error)
      toast({
        title: 'Error',
        description: 'Failed to create location',
        variant: 'destructive'
      })
    }
  }

  const handleUpdateLocation = async (locationId: string, updates: Partial<Location>) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/locations/${locationId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      })
      
      if (!response.ok) {
        throw new Error('Failed to update location')
      }
      
      const result = await response.json()
      setLocations(prev => prev.map(loc => loc.id === locationId ? result.location : loc))
      
      if (selectedLocation?.id === locationId) {
        setSelectedLocation(result.location)
      }
      
      toast({
        title: 'Success',
        description: 'Location updated successfully'
      })
    } catch (error) {
      logger.error('Error updating location:', error)
      toast({
        title: 'Error',
        description: 'Failed to update location',
        variant: 'destructive'
      })
    }
  }

  const handleDeleteLocation = async (locationId: string) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/locations/${locationId}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        throw new Error('Failed to delete location')
      }
      
      setLocations(prev => prev.filter(loc => loc.id !== locationId))
      
      if (selectedLocation?.id === locationId) {
        setSelectedLocation(null)
      }
      
      toast({
        title: 'Success',
        description: 'Location deleted successfully'
      })
    } catch (error) {
      logger.error('Error deleting location:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete location',
        variant: 'destructive'
      })
    }
  }

  const handleAddChild = (parentLocation: Location) => {
    setParentLocationForCreate(parentLocation)
    setShowCreateDialog(true)
  }

  const getLocationIcon = (type: Location['location_type']) => {
    const IconComponent = LOCATION_TYPE_ICONS[type]
    return <IconComponent className="w-4 h-4" />
  }

  const getLocationStats = () => {
    const stats = locations.reduce((acc, location) => {
      acc.byType[location.location_type] = (acc.byType[location.location_type] || 0) + 1
      if (location.is_shareable) acc.shareable++
      return acc
    }, {
      byType: {} as Record<string, number>,
      shareable: 0
    })
    
    return {
      total: locations.length,
      ...stats
    }
  }

  const stats = getLocationStats()

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Location Manager</h2>
          <p className="text-muted-foreground">
            Organize and manage your story locations ({stats.total} total)
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add Location
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{stats.total}</p>
                <p className="text-xs text-muted-foreground">Total Locations</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Globe className="w-4 h-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{stats.shareable}</p>
                <p className="text-xs text-muted-foreground">Shareable</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Building className="w-4 h-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{stats.byType.city || 0}</p>
                <p className="text-xs text-muted-foreground">Cities</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Home className="w-4 h-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{stats.byType.building || 0}</p>
                <p className="text-xs text-muted-foreground">Buildings</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search locations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as any)}
            className="border rounded px-3 py-2 text-sm"
          >
            <option value="all">All Types</option>
            <option value="world">World</option>
            <option value="continent">Continent</option>
            <option value="country">Country</option>
            <option value="region">Region</option>
            <option value="city">City</option>
            <option value="building">Building</option>
            <option value="room">Room</option>
            <option value="other">Other</option>
          </select>
          
          <Button
            variant={showShareableOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowShareableOnly(!showShareableOnly)}
          >
            {showShareableOnly ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
            Shareable Only
          </Button>
          
          <div className="flex border rounded">
            <Button
              variant={viewMode === 'tree' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('tree')}
              className="rounded-r-none"
            >
              Tree
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-none"
            >
              List
            </Button>
            <Button
              variant={viewMode === 'map' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('map')}
              className="rounded-l-none"
            >
              Map
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className={`grid gap-6 ${viewMode === 'map' ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-3'}`}>
        {/* Location Tree/List/Map */}
        <div className={viewMode === 'map' ? '' : 'lg:col-span-2'}>
          {viewMode === 'map' ? (
            <ComponentErrorBoundary componentName="LocationMapView" isolate>
              <LocationMapView
                locations={filteredLocations}
                projectId={projectId}
                onLocationSelect={setSelectedLocation}
                onLocationEdit={(location) => setSelectedLocation(location)}
                onLocationDelete={handleDeleteLocation}
                onAddChild={handleAddChild}
                selectedLocationId={selectedLocation?.id}
                loading={loading}
              />
            </ComponentErrorBoundary>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Locations</CardTitle>
                <CardDescription>
                  {viewMode === 'tree' 
                    ? 'Hierarchical view of your locations' 
                    : 'List view of all locations'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                {filteredLocations.length === 0 ? (
                  <div className="text-center py-12">
                    <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">
                      {searchQuery || filterType !== 'all' || showShareableOnly
                        ? 'No locations found'
                        : 'No locations created yet'
                      }
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      {searchQuery || filterType !== 'all' || showShareableOnly
                        ? 'Try adjusting your search or filters'
                        : 'Create your first location to start building your world'
                      }
                    </p>
                    <Button onClick={() => setShowCreateDialog(true)}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Location
                    </Button>
                  </div>
                ) : (
                  <ScrollArea className="h-[600px]">
                    {viewMode === 'tree' ? (
                      <ComponentErrorBoundary componentName="LocationTreeView" isolate>
                        <LocationTreeView
                          locations={filteredLocations}
                          onLocationSelect={setSelectedLocation}
                          onLocationEdit={(location) => setSelectedLocation(location)}
                          onLocationDelete={handleDeleteLocation}
                          onAddChild={handleAddChild}
                          selectedLocationId={selectedLocation?.id}
                        />
                      </ComponentErrorBoundary>
                    ) : (
                      <div className="space-y-2">
                        {filteredLocations.map(location => (
                          <div
                            key={location.id}
                            className={`p-3 border rounded-lg cursor-pointer transition-colors hover:bg-muted/50 ${
                              selectedLocation?.id === location.id ? 'bg-accent' : ''
                            }`}
                            onClick={() => setSelectedLocation(location)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <div className={`p-1 rounded ${LOCATION_TYPE_COLORS[location.location_type]}`}>
                                  {getLocationIcon(location.location_type)}
                                </div>
                                <div>
                                  <h4 className="font-medium">{location.name}</h4>
                                  <p className="text-sm text-muted-foreground">
                                    {location.parent_location?.name && `${location.parent_location.name} • `}
                                    {location.location_type}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                {location.is_shareable && (
                                  <Badge variant="secondary" className="text-xs">
                                    Shareable
                                  </Badge>
                                )}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleAddChild(location)
                                  }}
                                >
                                  <Plus className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Location Details */}
        {viewMode !== 'map' && (
          <div>
            <ComponentErrorBoundary componentName="LocationDetailsPanel" isolate>
              <LocationDetailsPanel
                location={selectedLocation}
                onUpdate={handleUpdateLocation}
                onDelete={handleDeleteLocation}
                onAddChild={handleAddChild}
              />
            </ComponentErrorBoundary>
          </div>
        )}
      </div>

      {/* Create Location Dialog */}
      <CreateLocationDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onLocationCreate={handleCreateLocation}
        parentLocation={parentLocationForCreate}
        existingLocations={locations}
      />
    </div>
  )
}