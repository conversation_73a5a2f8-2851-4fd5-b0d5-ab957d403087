#!/usr/bin/env node
import { readdir, readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface FileChange {
  file: string;
  changes: number;
  details: string[];
}

async function findSourceFiles(dir: string): Promise<string[]> {
  const files: string[] = [];
  
  async function walk(currentDir: string) {
    const entries = await readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        if (!['node_modules', '.next', 'dist', '.git'].includes(entry.name)) {
          await walk(fullPath);
        }
      } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
        files.push(fullPath);
      }
    }
  }
  
  await walk(dir);
  return files;
}

async function fixFile(filePath: string): Promise<FileChange> {
  let content = await readFile(filePath, 'utf-8');
  const originalContent = content;
  const details: string[] = [];
  
  // Check context
  const isAPIRoute = filePath.includes('/api/') && filePath.endsWith('route.ts');
  const isServerComponent = filePath.includes('/app/') && !filePath.includes('/api/') && !content.includes("'use client'");
  const isClientComponent = content.includes("'use client'");
  
  // Fix 1: Replace old import paths
  const oldImportPatterns = [
    { 
      pattern: /from\s+['"]@\/lib\/supabase\/client['"]/g,
      replacement: "from '@/lib/supabase'"
    },
    { 
      pattern: /from\s+['"]@\/lib\/supabase\/server['"]/g,
      replacement: "from '@/lib/supabase'"
    },
    { 
      pattern: /from\s+['"]@\/lib\/supabase\/supabase-client['"]/g,
      replacement: "from '@/lib/supabase'"
    }
  ];
  
  for (const { pattern, replacement } of oldImportPatterns) {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement);
      details.push("Fixed old import path");
    }
  }
  
  // Fix 2: Replace direct @supabase/supabase-js imports
  if (content.includes("from '@supabase/supabase-js'")) {
    content = content.replace(
      /import\s*{\s*createClient\s*}\s*from\s*['"]@supabase\/supabase-js['"]/g,
      "import { createTypedServerClient, createTypedBrowserClient } from '@/lib/supabase'"
    );
    details.push("Replaced direct @supabase/supabase-js import");
  }
  
  // Fix 3: Replace createClient() calls based on context
  if (isAPIRoute || isServerComponent) {
    // Server context - use createTypedServerClient
    content = content.replace(
      /const\s+(\w+)\s*=\s*createClient\(/g,
      'const $1 = await createTypedServerClient('
    );
    content = content.replace(
      /const\s+(\w+)\s*=\s*await\s+createClient\(/g,
      'const $1 = await createTypedServerClient('
    );
    
    // Also fix createBrowserClient in server context
    content = content.replace(
      /createBrowserClient\(/g,
      'await createTypedServerClient('
    );
    content = content.replace(
      /createTypedBrowserClient\(/g,
      'await createTypedServerClient('
    );
    
    if (content !== originalContent) {
      details.push("Replaced client creation with createTypedServerClient");
    }
  } else if (isClientComponent) {
    // Client context - use getBrowserClient for singleton
    content = content.replace(
      /const\s+(\w+)\s*=\s*createClient\(/g,
      'const $1 = getBrowserClient() //'
    );
    content = content.replace(
      /const\s+(\w+)\s*=\s*createTypedBrowserClient\(\)/g,
      'const $1 = getBrowserClient()'
    );
    content = content.replace(
      /createServerClient\(/g,
      'getBrowserClient() //'
    );
    
    if (content !== originalContent) {
      details.push("Replaced client creation with getBrowserClient");
    }
  }
  
  // Fix 4: Ensure correct imports after changes
  if (details.length > 0) {
    // Check if we need to add imports
    const hasServerImport = content.includes('createTypedServerClient');
    const hasBrowserImport = content.includes('getBrowserClient');
    const hasSupabaseImport = content.includes("from '@/lib/supabase'");
    
    if ((hasServerImport || hasBrowserImport) && !hasSupabaseImport) {
      // Find the last import
      const importMatch = content.match(/import[^;]+from\s+['"][^'"]+['"]\s*\n/g);
      if (importMatch) {
        const lastImport = importMatch[importMatch.length - 1];
        const lastImportIndex = content.lastIndexOf(lastImport);
        const insertIndex = lastImportIndex + lastImport.length;
        
        const imports = [];
        if (hasServerImport) imports.push('createTypedServerClient');
        if (hasBrowserImport) imports.push('getBrowserClient');
        
        content = content.slice(0, insertIndex) + 
          `import { ${imports.join(', ')} } from '@/lib/supabase'\n` +
          content.slice(insertIndex);
        details.push("Added missing Supabase imports");
      }
    }
  }
  
  // Fix 5: Remove unnecessary async from variable declarations (cleanup)
  content = content.replace(
    /const\s+(\w+)\s*=\s*await\s+await\s+createTypedServerClient\(/g,
    'const $1 = await createTypedServerClient('
  );
  
  const changes = content !== originalContent ? 1 : 0;
  
  if (changes > 0) {
    await writeFile(filePath, content);
  }
  
  return {
    file: filePath,
    changes,
    details
  };
}

async function main() {
  console.log('🔧 Auto-fixing Supabase client patterns...\n');
  
  const srcDir = join(__dirname, '..', 'src');
  const files = await findSourceFiles(srcDir);
  
  // Filter to only process files with potential issues
  const targetFiles = files.filter(file => 
    file.includes('\\api\\') || file.includes('/api/') ||
    file.includes('\\app\\') || file.includes('/app/') ||
    file.includes('\\components\\') || file.includes('/components/') ||
    file.includes('\\lib\\') || file.includes('/lib/')
  );
  
  console.log(`Processing ${targetFiles.length} relevant files\n`);
  
  const results: FileChange[] = [];
  let totalFixed = 0;
  
  for (const file of targetFiles) {
    const result = await fixFile(file);
    results.push(result);
    
    if (result.changes > 0) {
      console.log(`✅ Fixed: ${file.replace(/\\/g, '/')}`);
      result.details.forEach(detail => console.log(`   - ${detail}`));
      totalFixed++;
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`- Total files processed: ${targetFiles.length}`);
  console.log(`- Files fixed: ${totalFixed}`);
  console.log(`- Files already correct: ${targetFiles.length - totalFixed}`);
  
  // Generate report
  const reportPath = join(__dirname, '..', 'supabase-client-fixes-applied.md');
  let report = '# Supabase Client Fixes Applied\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  report += `Total files fixed: ${totalFixed}\n\n`;
  
  const fixedFiles = results.filter(r => r.changes > 0);
  if (fixedFiles.length > 0) {
    report += '## Files Fixed\n\n';
    for (const file of fixedFiles) {
      report += `### ${file.file.replace(/\\/g, '/')}\n\n`;
      file.details.forEach(detail => {
        report += `- ${detail}\n`;
      });
      report += '\n';
    }
  }
  
  await writeFile(reportPath, report);
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
}

// Run the script
main().catch(console.error);