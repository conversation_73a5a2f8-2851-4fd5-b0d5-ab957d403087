import { NextRequest, NextResponse } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { createTypedServerClient } from '@/lib/supabase'

export const GET = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Get today's date range
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    // Get week date range
    const weekStart = new Date(today)
    weekStart.setDate(weekStart.getDate() - weekStart.getDay()) // Start of week (Sunday)
    
    // Get month date range
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)

    // Get today's word count
    const { data: todayWords } = await supabase
      .from('writing_sessions')
      .select('word_count')
      .eq('user_id', user.id)
      .gte('started_at', today.toISOString())
      .lt('started_at', tomorrow.toISOString())

    const totalWordsToday = todayWords?.reduce((sum, session) => sum + (session.word_count || 0), 0) || 0

    // Get this week's word count
    const { data: weekWords } = await supabase
      .from('writing_sessions')
      .select('word_count')
      .eq('user_id', user.id)
      .gte('started_at', weekStart.toISOString())

    const totalWordsWeek = weekWords?.reduce((sum, session) => sum + (session.word_count || 0), 0) || 0

    // Get this month's word count
    const { data: monthWords } = await supabase
      .from('writing_sessions')
      .select('word_count')
      .eq('user_id', user.id)
      .gte('started_at', monthStart.toISOString())

    const totalWordsMonth = monthWords?.reduce((sum, session) => sum + (session.word_count || 0), 0) || 0

    // Get current streak
    let currentStreak = 0
    let checkDate = new Date(today)
    
    while (true) {
      const dayStart = new Date(checkDate)
      const dayEnd = new Date(checkDate)
      dayEnd.setDate(dayEnd.getDate() + 1)

      const { data: dayWords } = await supabase
        .from('writing_sessions')
        .select('word_count')
        .eq('user_id', user.id)
        .gte('started_at', dayStart.toISOString())
        .lt('started_at', dayEnd.toISOString())

      const dayTotal = dayWords?.reduce((sum, session) => sum + (session.word_count || 0), 0) || 0
      
      if (dayTotal > 0) {
        currentStreak++
        checkDate.setDate(checkDate.getDate() - 1)
      } else {
        break
      }
    }

    // Get longest streak (simplified - just use current for now)
    const longestStreak = currentStreak

    // Get completed goals count
    const { count: goalsCompleted } = await supabase
      .from('writing_goals')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('is_active', false)
      .not('completed_at', 'is', null)

    // Get active goals count
    const { count: goalsActive } = await supabase
      .from('writing_goals')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('is_active', true)

    const stats = {
      total_words_today: totalWordsToday,
      total_words_week: totalWordsWeek,
      total_words_month: totalWordsMonth,
      current_streak: currentStreak,
      longest_streak: longestStreak,
      goals_completed: goalsCompleted || 0,
      goals_active: goalsActive || 0
    }

    return NextResponse.json({ stats })
  } catch (error) {
    logger.error('Error fetching goal stats', error as Error)
    return NextResponse.json(
      { error: 'Failed to fetch goal statistics' },
      { status: 500 }
    )
  }
})