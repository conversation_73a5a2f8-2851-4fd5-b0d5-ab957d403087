'use client'

import { useState, useEffect } from 'react'
import { TimelineCalendarView, type TimelineEvent } from '@/components/timeline/timeline-calendar-view'
import { CreateTimelineEventDialog } from '@/components/timeline/create-timeline-event-dialog'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Plus } from 'lucide-react'
import { Calendar } from 'lucide-react'
import { Clock } from 'lucide-react'
import { Filter } from 'lucide-react'
import { Download } from 'lucide-react'
import { Upload } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { Skeleton } from '@/components/ui/skeleton'
import Link from 'next/link'

interface Project {
  id: string
  title: string
  description?: string
}

export default function TimelinePage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [selectedProjectId, setSelectedProjectId] = useState<string>('')
  const [events, setEvents] = useState<TimelineEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [selectedEvent, setSelectedEvent] = useState<TimelineEvent | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchProjects()
  }, [])

  useEffect(() => {
    if (selectedProjectId) {
      fetchEvents(selectedProjectId)
    }
  }, [selectedProjectId])

  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects')
      if (!response.ok) throw new Error('Failed to fetch projects')
      
      const data = await response.json()
      setProjects(data.projects || [])
      
      // Auto-select first project if available
      if (data.projects?.length > 0 && !selectedProjectId) {
        setSelectedProjectId(data.projects[0].id)
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load projects',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchEvents = async (projectId: string) => {
    try {
      const response = await fetch(`/api/timeline/events?projectId=${projectId}`)
      if (!response.ok) throw new Error('Failed to fetch timeline events')
      
      const data = await response.json()
      setEvents(data.events || [])
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load timeline events',
        variant: 'destructive'
      })
    }
  }

  const handleEventCreate = (date: Date) => {
    setSelectedDate(date)
    setIsCreateDialogOpen(true)
  }

  const handleEventSelect = (event: TimelineEvent) => {
    setSelectedEvent(event)
    setIsEditDialogOpen(true)
  }

  const handleEventUpdate = async (event: TimelineEvent) => {
    try {
      const response = await fetch(`/api/timeline/events/${event.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(event)
      })
      
      if (!response.ok) throw new Error('Failed to update event')
      
      const { event: updatedEvent } = await response.json()
      setEvents(prev => prev.map(e => e.id === updatedEvent.id ? updatedEvent : e))
      
      toast({
        title: 'Success',
        description: 'Timeline event updated'
      })
      
      setIsEditDialogOpen(false)
      setSelectedEvent(null)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update event',
        variant: 'destructive'
      })
    }
  }

  const handleEventDelete = async (eventId: string) => {
    try {
      const response = await fetch(`/api/timeline/events/${eventId}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) throw new Error('Failed to delete event')
      
      setEvents(prev => prev.filter(e => e.id !== eventId))
      toast({
        title: 'Success',
        description: 'Timeline event deleted'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete event',
        variant: 'destructive'
      })
    }
  }

  const handleExport = () => {
    // Implement export functionality
    toast({
      title: 'Export',
      description: 'Timeline export functionality coming soon'
    })
  }

  const handleImport = () => {
    // Implement import functionality
    toast({
      title: 'Import',
      description: 'Timeline import functionality coming soon'
    })
  }

  return (
    <div className="container max-w-7xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-4xl font-literary-display text-foreground mb-2">
              Story Timeline
            </h1>
            <p className="text-lg text-muted-foreground">
              Visualize and manage your story events in chronological order
            </p>
          </div>
          <div className="flex items-center gap-4">
            {loading ? (
              <Skeleton className="h-10 w-[200px]" />
            ) : (
              <Select value={selectedProjectId} onValueChange={setSelectedProjectId}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Event
            </Button>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-primary" />
              <div>
                <p className="text-2xl font-semibold">{events.length}</p>
                <p className="text-sm text-muted-foreground">Total Events</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-5 h-5 rounded bg-red-500" />
              <div>
                <p className="text-2xl font-semibold">
                  {events.filter(e => e.importance === 'critical').length}
                </p>
                <p className="text-sm text-muted-foreground">Critical Events</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="w-5 h-5 rounded bg-blue-500" />
              <div>
                <p className="text-2xl font-semibold">
                  {events.filter(e => e.type === 'plot').length}
                </p>
                <p className="text-sm text-muted-foreground">Plot Events</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-primary" />
              <div>
                <p className="text-2xl font-semibold">
                  {events.filter(e => e.verified).length}
                </p>
                <p className="text-sm text-muted-foreground">Verified Events</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Bar */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={handleImport}>
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
        </div>
      </div>

      {/* Content */}
      {loading ? (
        <div className="space-y-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      ) : !selectedProjectId ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Project Selected</h3>
            <p className="text-muted-foreground mb-4">
              Select a project from the dropdown above to view its timeline
            </p>
            <Button asChild>
              <Link href="/projects/new">
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Project
              </Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <TimelineCalendarView
          events={events}
          onEventSelect={handleEventSelect}
          onEventCreate={handleEventCreate}
          onEventUpdate={handleEventUpdate}
          onEventDelete={handleEventDelete}
        />
      )}

      {/* Create Event Dialog */}
      <CreateTimelineEventDialog
        open={isCreateDialogOpen}
        onOpenChange={(open) => {
          setIsCreateDialogOpen(open)
          if (!open) setSelectedDate(null)
        }}
        initialDate={selectedDate || undefined}
        onEventCreate={async (eventData) => {
          try {
            const response = await fetch(`/api/timeline/events`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                ...eventData,
                projectId: selectedProjectId
              })
            })
            
            if (!response.ok) throw new Error('Failed to create event')
            
            const { event } = await response.json()
            setEvents(prev => [...prev, event])
            
            toast({
              title: 'Success',
              description: 'Timeline event created'
            })
          } catch (error) {
            toast({
              title: 'Error',
              description: 'Failed to create event',
              variant: 'destructive'
            })
          }
        }}
      />
    </div>
  )
}