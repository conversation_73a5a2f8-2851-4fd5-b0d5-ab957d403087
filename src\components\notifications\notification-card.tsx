'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { MessageSquare } from 'lucide-react'
import { Trophy } from 'lucide-react'
import { Target } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { Bot } from 'lucide-react'
import { FileText } from 'lucide-react'
import { Users } from 'lucide-react'
import { X } from 'lucide-react'
import { ExternalLink } from 'lucide-react'
import { ChevronRight } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface NotificationProps {
  notification: {
    id: string
    type: string
    title: string
    message: string
    data?: any
    read: boolean
    priority: 'low' | 'medium' | 'high' | 'critical'
    action_url?: string
    action_type?: 'navigate' | 'modal' | 'external'
    created_at: string
  }
  onClick?: () => void
  onDelete?: () => void
}

export function NotificationCard({ notification, onClick, onDelete }: NotificationProps) {
  const getIcon = () => {
    switch (notification.type) {
      case 'collaboration':
        return <Users className="h-5 w-5" />
      case 'achievement':
        return <Trophy className="h-5 w-5" />
      case 'goal':
        return <Target className="h-5 w-5" />
      case 'ai_agent':
        return <Bot className="h-5 w-5" />
      case 'project':
        return <FileText className="h-5 w-5" />
      case 'comment':
        return <MessageSquare className="h-5 w-5" />
      case 'system':
      default:
        return <AlertCircle className="h-5 w-5" />
    }
  }

  const getPriorityColor = () => {
    switch (notification.priority) {
      case 'critical':
        return 'text-red-500'
      case 'high':
        return 'text-orange-500'
      case 'medium':
        return 'text-blue-500'
      case 'low':
      default:
        return 'text-muted-foreground'
    }
  }

  const getActionIcon = () => {
    if (!notification.action_url) return null
    
    if (notification.action_type === 'external') {
      return <ExternalLink className="h-4 w-4" />
    }
    return <ChevronRight className="h-4 w-4" />
  }

  return (
    <Card 
      className={cn(
        "relative cursor-pointer transition-colors hover:bg-accent/50",
        !notification.read && "border-primary/20 bg-primary/5"
      )}
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <div className={cn("mt-0.5", getPriorityColor())}>
            {getIcon()}
          </div>
          
          <div className="flex-1 space-y-1">
            <div className="flex items-start justify-between">
              <h4 className="text-sm font-medium leading-none">
                {notification.title}
              </h4>
              <div className="flex items-center space-x-1">
                {!notification.read && (
                  <Badge variant="secondary" className="h-2 w-2 p-0 rounded-full" />
                )}
                {onDelete && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={(e) => {
                      e.stopPropagation()
                      onDelete()
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>
            
            <p className="text-sm text-muted-foreground line-clamp-2">
              {notification.message}
            </p>
            
            {notification.data?.comment_preview && (
              <div className="mt-2 p-2 bg-muted/50 rounded text-xs text-muted-foreground italic">
                "{notification.data.comment_preview}"
              </div>
            )}
            
            <div className="flex items-center justify-between pt-1">
              <span className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
              </span>
              
              {notification.action_url && (
                <div className="flex items-center text-xs text-primary">
                  View
                  {getActionIcon()}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}