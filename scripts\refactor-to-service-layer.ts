#!/usr/bin/env node
import { readdir, readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface RefactorContext {
  file: string;
  content: string;
  hasServiceImports: boolean;
  hasSupabaseImports: boolean;
  tables: Set<string>;
  changes: number;
}

// Mapping of table names to service methods
const TABLE_TO_SERVICE_MAP = {
  'projects': {
    service: 'ServiceManager',
    methods: {
      'select': 'getContentGenerator().getProjects',
      'insert': 'getContentGenerator().createProject',
      'update': 'getContentGenerator().updateProject',
      'delete': 'getContentGenerator().deleteProject'
    }
  },
  'chapters': {
    service: 'ServiceManager',
    methods: {
      'select': 'getContentGenerator().getChapters',
      'insert': 'getContentGenerator().generateChapter',
      'update': 'getContentGenerator().updateChapter',
      'delete': 'getContentGenerator().deleteChapter'
    }
  },
  'characters': {
    service: 'ServiceManager',
    methods: {
      'select': 'getContentGenerator().getCharacters',
      'insert': 'getContentGenerator().createCharacter',
      'update': 'getContentGenerator().updateCharacter',
      'delete': 'getContentGenerator().deleteCharacter'
    }
  },
  'writing_sessions': {
    service: 'ServiceManager',
    methods: {
      'select': 'getAnalyticsEngine().getWritingSessions',
      'insert': 'getAnalyticsEngine().trackWritingSession',
      'update': 'getAnalyticsEngine().updateWritingSession'
    }
  },
  'usage_tracking': {
    service: 'ServiceManager',
    methods: {
      'select': 'getAnalyticsEngine().getUsageStats',
      'insert': 'getAnalyticsEngine().trackUsage'
    }
  },
  'collaboration_sessions': {
    service: 'ServiceManager',
    methods: {
      'select': 'getCollaborationHub().getSessions',
      'insert': 'getCollaborationHub().createSession',
      'update': 'getCollaborationHub().updateSession'
    }
  },
  'content_embeddings': {
    service: 'ServiceManager',
    methods: {
      'select': 'getSemanticSearch().search',
      'insert': 'getSemanticSearch().indexContent'
    }
  },
  'email_queue': {
    service: 'MailerooEmailService',
    methods: {
      'select': 'getQueuedEmails',
      'insert': 'sendEmail',
      'update': 'updateEmailStatus'
    }
  }
};

async function findAPIRoutes(dir: string): Promise<string[]> {
  const files: string[] = [];
  
  async function walk(currentDir: string) {
    const entries = await readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        if (!['node_modules', '.next', 'dist', '.git'].includes(entry.name)) {
          await walk(fullPath);
        }
      } else if (entry.isFile() && entry.name === 'route.ts' && (fullPath.includes('\\api\\') || fullPath.includes('/api/'))) {
        files.push(fullPath);
      }
    }
  }
  
  await walk(dir);
  return files;
}

function analyzeFile(content: string): RefactorContext {
  const hasServiceImports = content.includes('ServiceManager') || 
                          content.includes('AIOrchestrator') ||
                          content.includes('ContentGenerator') ||
                          content.includes('AnalyticsEngine') ||
                          content.includes('CollaborationService');
                          
  const hasSupabaseImports = content.includes('supabase') || 
                           content.includes('createTypedServerClient');
  
  // Find all tables being accessed
  const tables = new Set<string>();
  const tableMatches = content.matchAll(/\.from\(['"](\w+)['"]\)/g);
  for (const match of tableMatches) {
    tables.add(match[1]);
  }
  
  return {
    file: '',
    content,
    hasServiceImports,
    hasSupabaseImports,
    tables,
    changes: 0
  };
}

function refactorSupabaseQueries(context: RefactorContext): string {
  let { content } = context;
  
  // Track if we need to add imports
  let needsServiceManager = false;
  let needsMailerooService = false;
  
  // Replace direct database queries
  for (const [table, mapping] of Object.entries(TABLE_TO_SERVICE_MAP)) {
    // Match patterns like: supabase.from('table').select()
    const selectPattern = new RegExp(
      `(const\\s+{[^}]*}\\s*=\\s*await\\s+)?supabase\\.from\\(['"]${table}['"]\\)\\.select\\([^)]*\\)([^;]*;)`,
      'g'
    );
    
    if (content.includes(`.from('${table}')`)) {
      if (mapping.service === 'ServiceManager') {
        needsServiceManager = true;
      } else if (mapping.service === 'MailerooEmailService') {
        needsMailerooService = true;
      }
      
      // Replace select queries
      content = content.replace(selectPattern, (match, assignment, rest) => {
        context.changes++;
        if (mapping.service === 'ServiceManager' && mapping.methods.select) {
          const method = mapping.methods.select;
          return `${assignment || ''}await serviceManager.${method}(userId)${rest}`;
        }
        return match;
      });
      
      // Replace insert queries
      const insertPattern = new RegExp(
        `supabase\\.from\\(['"]${table}['"]\\)\\.insert\\(([^)]+)\\)`,
        'g'
      );
      content = content.replace(insertPattern, (match, data) => {
        context.changes++;
        if (mapping.service === 'ServiceManager' && mapping.methods.insert) {
          const method = mapping.methods.insert;
          return `serviceManager.${method}(${data})`;
        }
        return match;
      });
      
      // Replace update queries
      const updatePattern = new RegExp(
        `supabase\\.from\\(['"]${table}['"]\\)\\.update\\(([^)]+)\\)\\.eq\\(['"]\\w+['"],\\s*([^)]+)\\)`,
        'g'
      );
      content = content.replace(updatePattern, (match, data, id) => {
        context.changes++;
        if (mapping.service === 'ServiceManager' && mapping.methods.update) {
          const method = mapping.methods.update;
          return `serviceManager.${method}(${id}, ${data})`;
        }
        return match;
      });
    }
  }
  
  // Add necessary imports
  if (needsServiceManager && !context.hasServiceImports) {
    const importStatement = "import { ServiceManager } from '@/lib/services/service-manager'\n";
    
    // Find where to insert the import
    const lastImportIndex = content.lastIndexOf('import ');
    if (lastImportIndex !== -1) {
      const endOfLineIndex = content.indexOf('\n', lastImportIndex);
      content = content.slice(0, endOfLineIndex + 1) + importStatement + content.slice(endOfLineIndex + 1);
    } else {
      // No imports found, add at the beginning
      content = importStatement + '\n' + content;
    }
    
    // Add service manager initialization
    const functionMatch = content.match(/(export\s+async\s+function\s+\w+\s*\([^)]*\)\s*{)/);
    if (functionMatch) {
      const insertPoint = functionMatch.index! + functionMatch[0].length;
      const initialization = '\n  const serviceManager = ServiceManager.getInstance();\n';
      content = content.slice(0, insertPoint) + initialization + content.slice(insertPoint);
    }
  }
  
  if (needsMailerooService && !content.includes('mailerooEmailService')) {
    const importStatement = "import { mailerooEmailService } from '@/lib/services/maileroo-email-service'\n";
    
    const lastImportIndex = content.lastIndexOf('import ');
    if (lastImportIndex !== -1) {
      const endOfLineIndex = content.indexOf('\n', lastImportIndex);
      content = content.slice(0, endOfLineIndex + 1) + importStatement + content.slice(endOfLineIndex + 1);
    } else {
      content = importStatement + '\n' + content;
    }
  }
  
  // Remove unused supabase imports if no longer needed
  if (context.hasSupabaseImports && !content.includes('supabase.from(') && !content.includes('.from(')) {
    content = content.replace(/import.*createTypedServerClient.*\n/g, '');
    content = content.replace(/const\s+supabase\s*=.*\n/g, '');
  }
  
  return content;
}

async function processFile(filePath: string, dryRun: boolean = false): Promise<boolean> {
  try {
    const content = await readFile(filePath, 'utf-8');
    const context = analyzeFile(content);
    context.file = filePath;
    
    // Skip if no tables are accessed
    if (context.tables.size === 0) {
      return false;
    }
    
    // Skip if already using services properly
    if (context.hasServiceImports && !context.hasSupabaseImports) {
      return false;
    }
    
    // Refactor the file
    const refactoredContent = refactorSupabaseQueries(context);
    
    if (context.changes > 0) {
      if (!dryRun) {
        await writeFile(filePath, refactoredContent);
      }
      console.log(`✅ Refactored ${filePath.replace(/\\/g, '/')}: ${context.changes} changes`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error);
    return false;
  }
}

async function generateRefactorReport(results: Map<string, boolean>): Promise<void> {
  const reportPath = join(__dirname, '..', 'service-layer-refactor-report.md');
  
  let report = '# Service Layer Refactoring Report\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  
  const successful = Array.from(results.entries()).filter(([_, success]) => success);
  const failed = Array.from(results.entries()).filter(([_, success]) => !success);
  
  report += `## Summary\n\n`;
  report += `- Total files processed: ${results.size}\n`;
  report += `- Successfully refactored: ${successful.length}\n`;
  report += `- No changes needed: ${failed.length}\n\n`;
  
  if (successful.length > 0) {
    report += `## Successfully Refactored Files\n\n`;
    successful.forEach(([file]) => {
      report += `- ${file.replace(/\\/g, '/')}\n`;
    });
    report += '\n';
  }
  
  report += `## Next Steps\n\n`;
  report += `1. Run tests to ensure functionality is preserved\n`;
  report += `2. Review refactored files for any edge cases\n`;
  report += `3. Update service layer methods if needed\n`;
  report += `4. Consider adding integration tests for service layer\n\n`;
  
  report += `## Service Layer Benefits\n\n`;
  report += `- **Centralized business logic**: All logic in one place\n`;
  report += `- **Better testability**: Mock services for unit tests\n`;
  report += `- **Consistent error handling**: Services handle errors uniformly\n`;
  report += `- **Performance optimization**: Services can implement caching\n`;
  report += `- **Easier maintenance**: Changes in one service affect all consumers\n`;
  
  await writeFile(reportPath, report);
  console.log(`\nReport saved to: ${reportPath}`);
}

async function main() {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  
  console.log('🔧 Refactoring API routes to use service layer...\n');
  if (dryRun) {
    console.log('Running in DRY RUN mode - no files will be modified\n');
  }
  
  const srcDir = join(__dirname, '..', 'src');
  const apiRoutes = await findAPIRoutes(srcDir);
  
  console.log(`Found ${apiRoutes.length} API route files\n`);
  
  const results = new Map<string, boolean>();
  
  // Process each file
  for (const route of apiRoutes) {
    const success = await processFile(route, dryRun);
    results.set(route, success);
  }
  
  // Generate report
  await generateRefactorReport(results);
  
  console.log('\n✅ Service layer refactoring complete!');
  
  if (dryRun) {
    console.log('\nThis was a dry run. Run without --dry-run to apply changes.');
  }
}

// Run the script
main().catch(console.error);