'use client'

import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react'
import { VariableSizeList as List } from 'react-window'
import AutoSizer from 'react-virtualized-auto-sizer'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ChevronRight } from 'lucide-react'
import { ChevronDown } from 'lucide-react'
import { MapPin } from 'lucide-react'
import { Plus } from 'lucide-react'
import { Edit } from 'lucide-react'
import { Trash2 } from 'lucide-react'
import { Globe } from 'lucide-react'
import { Mountain } from 'lucide-react'
import { Trees } from 'lucide-react'
import { Building } from 'lucide-react'
import { Home } from 'lucide-react'
import { Search } from 'lucide-react'
import { Input } from '@/components/ui/input'
import type { Location } from './types'

// Constants moved outside component for performance
const LOCATION_TYPE_ICONS = {
  world: Globe,
  continent: Mountain,
  country: Trees,
  region: Mountain,
  city: Building,
  building: Home,
  room: Home,
  other: MapPin
} as const

const LOCATION_TYPE_COLORS = {
  world: 'text-blue-600 bg-blue-50 border-blue-200',
  continent: 'text-green-600 bg-green-50 border-green-200',
  country: 'text-purple-600 bg-purple-50 border-purple-200',
  region: 'text-orange-600 bg-orange-50 border-orange-200',
  city: 'text-red-600 bg-red-50 border-red-200',
  building: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  room: 'text-gray-600 bg-gray-50 border-gray-200',
  other: 'text-indigo-600 bg-indigo-50 border-indigo-200'
} as const

const ROW_HEIGHT = 56 // Base height for each tree node
const INDENT_SIZE = 20 // Pixels per level of indentation

interface LocationTreeViewProps {
  locations: Location[]
  onLocationSelect: (location: Location) => void
  onLocationEdit: (location: Location) => void
  onLocationDelete: (locationId: string) => void
  onAddChild: (parentLocation: Location) => void
  selectedLocationId?: string
}

interface LocationNode extends Location {
  children: LocationNode[]
  level: number
}

interface FlattenedNode {
  node: LocationNode
  visible: boolean
  index: number
}

// Memoized tree node component for better performance
const TreeNode = React.memo(({ 
  node, 
  isSelected, 
  isExpanded,
  hasChildren,
  onToggleExpanded,
  onSelect,
  onAddChild,
  onEdit,
  onDelete
}: {
  node: LocationNode
  isSelected: boolean
  isExpanded: boolean
  hasChildren: boolean
  onToggleExpanded: (nodeId: string) => void
  onSelect: (location: Location) => void
  onAddChild: (location: Location) => void
  onEdit: (location: Location) => void
  onDelete: (locationId: string) => void
}) => {
  const IconComponent = LOCATION_TYPE_ICONS[node.location_type]
  const indent = node.level * INDENT_SIZE
  
  return (
    <div
      className={`flex items-center gap-2 p-2 rounded-lg cursor-pointer transition-colors hover:bg-muted/50 group ${
        isSelected ? 'bg-accent border-l-4 border-l-primary' : ''
      }`}
      style={{ paddingLeft: `${indent + 8}px` }}
      onClick={() => onSelect(node)}
    >
      {/* Expand/Collapse Button */}
      <div className="w-4 h-4 flex items-center justify-center">
        {hasChildren ? (
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0 hover:bg-transparent"
            onClick={(e) => {
              e.stopPropagation()
              onToggleExpanded(node.id)
            }}
          >
            {isExpanded ? (
              <ChevronDown className="w-3 h-3" />
            ) : (
              <ChevronRight className="w-3 h-3" />
            )}
          </Button>
        ) : (
          <div className="w-3 h-3" />
        )}
      </div>

      {/* Icon */}
      <div className={`p-1 rounded ${LOCATION_TYPE_COLORS[node.location_type]}`}>
        <IconComponent className="w-4 h-4" />
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <h4 className="font-medium truncate">{node.name}</h4>
          {node.is_shareable && (
            <Badge variant="secondary" className="text-xs">
              Shareable
            </Badge>
          )}
        </div>
        {node.description && (
          <p className="text-xs text-muted-foreground truncate">
            {node.description}
          </p>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={(e) => {
            e.stopPropagation()
            onAddChild(node)
          }}
          title="Add child location"
        >
          <Plus className="w-3 h-3" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={(e) => {
            e.stopPropagation()
            onEdit(node)
          }}
          title="Edit location"
        >
          <Edit className="w-3 h-3" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={(e) => {
            e.stopPropagation()
            onDelete(node.id)
          }}
          title="Delete location"
        >
          <Trash2 className="w-3 h-3" />
        </Button>
      </div>
    </div>
  )
})

TreeNode.displayName = 'TreeNode'

export function LocationTreeView({
  locations,
  onLocationSelect,
  onLocationEdit,
  onLocationDelete,
  onAddChild,
  selectedLocationId
}: LocationTreeViewProps) {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())
  const [searchQuery, setSearchQuery] = useState('')
  const listRef = useRef<List>(null)
  const itemSizeMap = useRef<{ [index: number]: number }>({})

  // Build tree structure
  const locationTree = useMemo(() => {
    const locationMap = new Map<string, LocationNode>()
    const rootNodes: LocationNode[] = []
    
    // First pass: create all nodes
    locations.forEach(location => {
      locationMap.set(location.id, {
        ...location,
        children: [],
        level: 0
      })
    })
    
    // Second pass: build parent-child relationships and calculate levels
    const calculateLevel = (node: LocationNode, visited = new Set<string>()): number => {
      if (visited.has(node.id)) {
        return 0
      }
      visited.add(node.id)
      
      if (!node.parent_location_id) {
        return 0
      }
      
      const parent = locationMap.get(node.parent_location_id)
      if (!parent) {
        return 0
      }
      
      return calculateLevel(parent, visited) + 1
    }
    
    locations.forEach(location => {
      const node = locationMap.get(location.id)!
      node.level = calculateLevel(node)
      
      if (location.parent_location_id) {
        const parent = locationMap.get(location.parent_location_id)
        if (parent) {
          parent.children.push(node)
        } else {
          rootNodes.push(node)
        }
      } else {
        rootNodes.push(node)
      }
    })
    
    // Sort children by name
    const sortChildren = (node: LocationNode) => {
      node.children.sort((a, b) => a.name.localeCompare(b.name))
      node.children.forEach(sortChildren)
    }
    
    rootNodes.sort((a, b) => a.name.localeCompare(b.name))
    rootNodes.forEach(sortChildren)
    
    return rootNodes
  }, [locations])

  // Flatten tree for virtualization with search filtering
  const flattenedTree = useMemo(() => {
    const flattened: FlattenedNode[] = []
    let index = 0
    
    const matchesSearch = (node: LocationNode): boolean => {
      if (!searchQuery) return true
      const query = searchQuery.toLowerCase()
      return (
        node.name.toLowerCase().includes(query) ||
        (node.description?.toLowerCase().includes(query) ?? false)
      )
    }
    
    const flattenNode = (node: LocationNode, forceVisible = false): boolean => {
      const matches = matchesSearch(node)
      const isExpanded = expandedNodes.has(node.id)
      
      // Check if any children match
      let hasVisibleChildren = false
      if (node.children.length > 0) {
        node.children.forEach(child => {
          const childVisible = flattenNode(child, matches || forceVisible)
          if (childVisible) {
            hasVisibleChildren = true
          }
        })
      }
      
      // Node is visible if it matches search, has visible children, or parent matches
      const visible = matches || hasVisibleChildren || forceVisible
      
      if (visible) {
        flattened.push({
          node,
          visible: true,
          index: index++
        })
        
        // If searching and has visible children, auto-expand
        if (searchQuery && hasVisibleChildren && !isExpanded) {
          setExpandedNodes(prev => new Set([...prev, node.id]))
        }
      }
      
      return visible
    }
    
    locationTree.forEach(rootNode => flattenNode(rootNode))
    
    return flattened
  }, [locationTree, expandedNodes, searchQuery])

  // Get visible nodes (expanded and not filtered out)
  const visibleNodes = useMemo(() => {
    const visible: FlattenedNode[] = []
    
    const addVisibleNodes = (nodes: LocationNode[], parentExpanded = true) => {
      nodes.forEach(node => {
        const flatNode = flattenedTree.find(f => f.node.id === node.id)
        if (flatNode && parentExpanded) {
          visible.push(flatNode)
          const isExpanded = expandedNodes.has(node.id)
          if (isExpanded && node.children.length > 0) {
            addVisibleNodes(node.children, true)
          }
        }
      })
    }
    
    // Start with filtered root nodes
    const rootNodes = flattenedTree
      .filter(f => !f.node.parent_location_id)
      .map(f => f.node)
    
    addVisibleNodes(rootNodes)
    
    return visible
  }, [flattenedTree, expandedNodes])

  const toggleExpanded = useCallback((nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev)
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId)
      } else {
        newSet.add(nodeId)
      }
      return newSet
    })
    
    // Clear cached sizes when expanding/collapsing
    itemSizeMap.current = {}
    listRef.current?.resetAfterIndex(0)
  }, [])

  // Get item size
  const getItemSize = useCallback((index: number) => {
    return itemSizeMap.current[index] || ROW_HEIGHT
  }, [])

  // Set item size
  const setItemSize = useCallback((index: number, size: number) => {
    if (itemSizeMap.current[index] !== size) {
      itemSizeMap.current[index] = size
      if (listRef.current) {
        listRef.current.resetAfterIndex(index)
      }
    }
  }, [])

  // Row renderer
  const Row = useCallback(({ index, style }: { index: number; style: React.CSSProperties }) => {
    const item = visibleNodes[index]
    if (!item) return null
    
    const { node } = item
    const isSelected = selectedLocationId === node.id
    const isExpanded = expandedNodes.has(node.id)
    const hasChildren = node.children.length > 0
    
    return (
      <div style={style}>
        <TreeNode
          node={node}
          isSelected={isSelected}
          isExpanded={isExpanded}
          hasChildren={hasChildren}
          onToggleExpanded={toggleExpanded}
          onSelect={onLocationSelect}
          onAddChild={onAddChild}
          onEdit={onLocationEdit}
          onDelete={onLocationDelete}
        />
      </div>
    )
  }, [visibleNodes, selectedLocationId, expandedNodes, toggleExpanded, onLocationSelect, onAddChild, onLocationEdit, onLocationDelete])

  // Auto-expand to show selected item
  useEffect(() => {
    if (!selectedLocationId) return
    
    const findPath = (nodeId: string): string[] => {
      const path: string[] = []
      let current = locations.find(l => l.id === nodeId)
      
      while (current?.parent_location_id) {
        path.unshift(current.parent_location_id)
        current = locations.find(l => l.id === current!.parent_location_id)
      }
      
      return path
    }
    
    const path = findPath(selectedLocationId)
    if (path.length > 0) {
      setExpandedNodes(prev => new Set([...prev, ...path]))
    }
  }, [selectedLocationId, locations])

  // Scroll to selected item
  useEffect(() => {
    if (!selectedLocationId || !listRef.current) return
    
    const index = visibleNodes.findIndex(item => item.node.id === selectedLocationId)
    if (index >= 0) {
      listRef.current.scrollToItem(index, 'center')
    }
  }, [selectedLocationId, visibleNodes])

  if (locations.length === 0) {
    return (
      <div className="text-center py-8">
        <MapPin className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">No locations to display</p>
      </div>
    )
  }

  const showPerformanceMode = locations.length > 100

  return (
    <div className="flex flex-col h-full">
      {/* Search bar */}
      <div className="p-2 border-b">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search locations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8 h-9"
          />
        </div>
        {showPerformanceMode && (
          <div className="mt-1 text-xs text-muted-foreground">
            Virtualization enabled • {visibleNodes.length} of {locations.length} visible
          </div>
        )}
      </div>

      {/* Tree view with virtualization */}
      <div className="flex-1 min-h-0">
        {visibleNodes.length === 0 ? (
          <div className="text-center py-8">
            <Search className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">No locations match your search</p>
          </div>
        ) : (
          <AutoSizer>
            {({ height, width }) => (
              <List
                ref={listRef}
                height={height}
                itemCount={visibleNodes.length}
                itemSize={getItemSize}
                width={width}
                overscanCount={5}
                estimatedItemSize={ROW_HEIGHT}
              >
                {Row}
              </List>
            )}
          </AutoSizer>
        )}
      </div>
    </div>
  )
}