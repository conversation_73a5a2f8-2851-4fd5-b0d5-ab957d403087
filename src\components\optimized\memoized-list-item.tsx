/**
 * Optimized List Item Component Pattern
 * Use this as a template for creating memoized list items
 */

import React from 'react'

// Example: Generic list item props
interface ListItemProps<T> {
  item: T
  onClick?: (item: T) => void
  selected?: boolean
  className?: string
}

/**
 * Generic memoized list item component
 * 
 * Key optimization strategies:
 * 1. Use React.memo with custom comparison function
 * 2. Avoid inline functions in render
 * 3. Minimize prop changes
 * 4. Use stable references for callbacks
 */
export const MemoizedListItem = React.memo(
  function ListItem<T extends { id: string | number }>({ 
    item, 
    onClick, 
    selected = false,
    className 
  }: ListItemProps<T>) {
    // Use useCallback for stable function reference
    const handleClick = React.useCallback(() => {
      onClick?.(item)
    }, [item, onClick])

    return (
      <div 
        className={className}
        onClick={handleClick}
        role="button"
        tabIndex={0}
        aria-selected={selected}
      >
        {/* Render item content */}
        {JSON.stringify(item)}
      </div>
    )
  },
  // Custom comparison function
  (prevProps, nextProps) => {
    // Check if the item hasn't changed
    if (prevProps.item !== nextProps.item) return false
    
    // Check if selection state hasn't changed
    if (prevProps.selected !== nextProps.selected) return false
    
    // Check if className hasn't changed
    if (prevProps.className !== nextProps.className) return false
    
    // Check if onClick reference is stable
    if (prevProps.onClick !== nextProps.onClick) return false
    
    // Props are equal, skip re-render
    return true
  }
)

/**
 * Example: Specialized memoized components
 */

// Project list item with deep comparison
export const MemoizedProjectItem = React.memo(
  function ProjectItem({ 
    project, 
    onClick, 
    selected 
  }: {
    project: { id: string; title: string; updated_at: string }
    onClick?: (project: any) => void
    selected?: boolean
  }) {
    const handleClick = React.useCallback(() => {
      onClick?.(project)
    }, [project, onClick])

    return (
      <div 
        className={`project-item ${selected ? 'selected' : ''}`}
        onClick={handleClick}
      >
        <h3>{project.title}</h3>
        <span>{project.updated_at}</span>
      </div>
    )
  },
  (prevProps, nextProps) => {
    // Deep comparison for project object
    return (
      prevProps.project.id === nextProps.project.id &&
      prevProps.project.title === nextProps.project.title &&
      prevProps.project.updated_at === nextProps.project.updated_at &&
      prevProps.selected === nextProps.selected &&
      prevProps.onClick === nextProps.onClick
    )
  }
)

/**
 * Best Practices for Memoized List Components:
 * 
 * 1. Parent Component:
 *    - Use useCallback for event handlers passed to list items
 *    - Keep item references stable (don't recreate objects in render)
 *    - Consider using useMemo for filtered/sorted lists
 * 
 * 2. List Item Component:
 *    - Implement custom comparison function for complex props
 *    - Avoid expensive computations in render
 *    - Keep props minimal and primitive when possible
 * 
 * 3. Performance Tips:
 *    - Virtualize long lists (react-window or react-virtualized)
 *    - Use keys properly for list rendering
 *    - Profile with React DevTools to verify optimization impact
 * 
 * Example parent component:
 * ```tsx
 * function ProjectList({ projects }) {
 *   const [selectedId, setSelectedId] = useState(null)
 *   
 *   // Stable callback reference
 *   const handleProjectClick = useCallback((project) => {
 *     setSelectedId(project.id)
 *   }, [])
 *   
 *   return (
 *     <div>
 *       {projects.map(project => (
 *         <MemoizedProjectItem
 *           key={project.id}
 *           project={project}
 *           onClick={handleProjectClick}
 *           selected={project.id === selectedId}
 *         />
 *       ))}
 *     </div>
 *   )
 * }
 * ```
 */