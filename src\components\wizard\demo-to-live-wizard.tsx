'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { logger } from '@/lib/services/logger'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { BookOpen } from 'lucide-react'
import { Sparkles } from 'lucide-react'
import { Users } from 'lucide-react'
import { Palette } from 'lucide-react'
import { CheckCircle } from 'lucide-react'
import { ArrowRight } from 'lucide-react'
import { ArrowLeft } from 'lucide-react'
import { Save } from 'lucide-react'
import { Loader2 } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { Wand2 } from 'lucide-react'
import { User } from 'lucide-react'
import { Lock } from 'lucide-react'
import { Mail } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/contexts/auth-context'
import { genreOptions, toneOptions, themeOptions } from './wizard-data'

interface DemoWizardProps {
  onComplete?: (data: any) => void
}

interface FormData {
  title: string
  description: string
  genre: string
  targetAudience: string
  themes: string[]
  tones: string[]
  protagonist: string
  setting: string
}

const wizardSteps = [
  { id: 'basics', title: 'Story Basics', icon: BookOpen, description: 'Title and core concept' },
  { id: 'style', title: 'Style & Tone', icon: Palette, description: 'Genre, themes, and mood' },
  { id: 'world', title: 'World & Characters', icon: Users, description: 'Setting and protagonist' },
  { id: 'generate', title: 'AI Generation', icon: Sparkles, description: 'Watch your story come to life' },
  { id: 'save', title: 'Save Your Story', icon: Save, description: 'Create account to continue' },
]

export function DemoToLiveWizard({ onComplete }: DemoWizardProps) {
  const router = useRouter()
  const { toast } = useToast()
  const { user } = useAuth()
  const [currentStep, setCurrentStep] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState<any>(null)
  const [showAuthForm, setShowAuthForm] = useState(false)
  const [authMode, setAuthMode] = useState<'login' | 'signup'>('signup')
  
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    genre: '',
    targetAudience: 'young-adult',
    themes: [],
    tones: [],
    protagonist: '',
    setting: '',
  })

  // Save to localStorage on each update
  useEffect(() => {
    if (currentStep < 3) { // Don't save auth steps
      localStorage.setItem('bookscribe_demo_progress', JSON.stringify({
        step: currentStep,
        data: formData,
        timestamp: Date.now()
      }))
    }
  }, [currentStep, formData])

  // Load from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('bookscribe_demo_progress')
    if (saved) {
      try {
        const { step, data, timestamp } = JSON.parse(saved)
        // Only restore if less than 30 minutes old
        if (Date.now() - timestamp < 30 * 60 * 1000) {
          setFormData(data)
          setCurrentStep(step)
          toast({
            title: 'Welcome back!',
            description: 'We saved your progress. Continue where you left off.',
          })
        }
      } catch (e) {
        logger.error('Failed to restore progress', e as Error)
      }
    }
  }, [])

  const validateCurrentStep = () => {
    switch (currentStep) {
      case 0: // Basics
        return formData.title.length > 0 && formData.description.length > 10
      case 1: // Style
        return formData.genre && formData.themes.length > 0 && formData.tones.length > 0
      case 2: // World
        return formData.protagonist.length > 0 && formData.setting.length > 0
      default:
        return true
    }
  }

  const handleNext = async () => {
    if (currentStep === 2) {
      // Generate story structure
      await generateStoryStructure()
    } else if (currentStep === 3 && generatedContent) {
      // Show auth form
      if (user) {
        // Already logged in, save and redirect
        await saveGeneratedStory()
      } else {
        setShowAuthForm(true)
      }
    } else {
      setCurrentStep(prev => prev + 1)
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(0, prev - 1))
  }

  const generateStoryStructure = async () => {
    setIsGenerating(true)
    setCurrentStep(3) // Move to generation step

    try {
      const response = await fetch('/api/demo/generate-story', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Generation failed')
      }

      setGeneratedContent(result.data)
      
      // Show success animation
      toast({
        title: '✨ Story Generated!',
        description: 'Your unique story structure is ready.',
      })
    } catch (error) {
      logger.error('Generation error', error as Error)
      toast({
        title: 'Generation Failed',
        description: 'Please try again or check your connection.',
        variant: 'destructive',
      })
      setCurrentStep(2) // Go back to previous step
    } finally {
      setIsGenerating(false)
    }
  }

  const saveGeneratedStory = async () => {
    // This would save to the user's account
    localStorage.removeItem('bookscribe_demo_progress')
    router.push('/projects/new?from=demo&data=' + encodeURIComponent(JSON.stringify(generatedContent)))
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Basics
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <div>
              <Label htmlFor="title" className="text-mono-base font-mono">
                What's your story called?
              </Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="The Crystal Kingdoms"
                className="mt-2 font-mono"
                maxLength={100}
              />
              <p className="text-mono-xs text-muted-foreground mt-1 font-mono">
                {formData.title.length}/100 characters
              </p>
            </div>

            <div>
              <Label htmlFor="description" className="text-mono-base font-mono">
                What's it about? (One paragraph)
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="A young mage discovers ancient crystal magic and must master it to save her kingdom from an awakening evil..."
                className="mt-2 min-h-[120px] font-mono"
                maxLength={500}
              />
              <p className="text-mono-xs text-muted-foreground mt-1 font-mono">
                {formData.description.length}/500 characters
              </p>
            </div>

            {formData.title && formData.description && (
              <Alert className="border-primary/20 bg-primary/5">
                <Sparkles className="h-4 w-4 text-primary" />
                <AlertDescription className="font-mono text-mono-sm">
                  Great start! Our AI will expand this into a full story structure.
                </AlertDescription>
              </Alert>
            )}
          </motion.div>
        )

      case 1: // Style & Tone
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <div>
              <Label className="text-mono-base font-mono">Genre</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                {genreOptions.slice(0, 6).map((genre) => (
                  <Button
                    key={genre}
                    variant={formData.genre === genre ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFormData({ ...formData, genre })}
                    className="font-mono"
                  >
                    {genre}
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <Label className="text-mono-base font-mono">
                Themes (Choose 2-3)
              </Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {themeOptions.slice(0, 12).map((theme) => (
                  <Badge
                    key={theme}
                    variant={formData.themes.includes(theme) ? 'default' : 'outline'}
                    className="cursor-pointer font-mono"
                    onClick={() => {
                      const newThemes = formData.themes.includes(theme)
                        ? formData.themes.filter(t => t !== theme)
                        : [...formData.themes, theme].slice(0, 3)
                      setFormData({ ...formData, themes: newThemes })
                    }}
                  >
                    {theme}
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <Label className="text-mono-base font-mono">
                Tone & Mood (Choose 2-3)
              </Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {toneOptions.slice(0, 12).map((tone) => (
                  <Badge
                    key={tone}
                    variant={formData.tones.includes(tone) ? 'default' : 'outline'}
                    className="cursor-pointer font-mono"
                    onClick={() => {
                      const newTones = formData.tones.includes(tone)
                        ? formData.tones.filter(t => t !== tone)
                        : [...formData.tones, tone].slice(0, 3)
                      setFormData({ ...formData, tones: newTones })
                    }}
                  >
                    {tone}
                  </Badge>
                ))}
              </div>
            </div>
          </motion.div>
        )

      case 2: // World & Characters
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <div>
              <Label htmlFor="protagonist" className="text-mono-base font-mono">
                Who's your main character?
              </Label>
              <Input
                id="protagonist"
                value={formData.protagonist}
                onChange={(e) => setFormData({ ...formData, protagonist: e.target.value })}
                placeholder="Aria, a young mage with hidden powers"
                className="mt-2 font-mono"
              />
            </div>

            <div>
              <Label htmlFor="setting" className="text-mono-base font-mono">
                Where does it take place?
              </Label>
              <Textarea
                id="setting"
                value={formData.setting}
                onChange={(e) => setFormData({ ...formData, setting: e.target.value })}
                placeholder="The Crystal Kingdoms - a realm where magic flows through ancient crystals buried deep in the mountains..."
                className="mt-2 min-h-[100px] font-mono"
              />
            </div>

            <Alert className="border-primary/20 bg-primary/5">
              <Wand2 className="h-4 w-4 text-primary" />
              <AlertDescription className="font-mono text-mono-sm">
                Ready to generate! Our AI will create a complete story structure with chapters, plot points, and character arcs.
              </AlertDescription>
            </Alert>
          </motion.div>
        )

      case 3: // Generation
        return (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-12"
          >
            {isGenerating ? (
              <div className="space-y-6">
                <Loader2 className="w-16 h-16 mx-auto text-primary animate-spin" />
                <h3 className="text-display-md font-literary-display">
                  Creating Your Story Universe...
                </h3>
                <p className="text-mono-base text-muted-foreground font-mono">
                  Our AI is crafting plot points, character arcs, and world details
                </p>
                <Progress value={66} className="max-w-xs mx-auto" />
              </div>
            ) : generatedContent ? (
              <div className="space-y-6">
                <CheckCircle className="w-16 h-16 mx-auto text-green-500" />
                <h3 className="text-display-md font-literary-display">
                  Your Story is Ready!
                </h3>
                <Card className="max-w-2xl mx-auto text-left">
                  <CardHeader>
                    <CardTitle className="text-display-sm font-literary-display">
                      {generatedContent.title}
                    </CardTitle>
                    <CardDescription className="font-mono text-mono-sm">
                      {formData.genre} • {generatedContent.estimatedWordCount.toLocaleString()} words
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="prose prose-sm max-w-none">
                      <pre className="whitespace-pre-wrap font-mono text-mono-sm bg-muted p-4 rounded-lg">
                        {generatedContent.generatedStructure}
                      </pre>
                    </div>
                    <Alert className="border-amber-500/20 bg-amber-500/10">
                      <AlertCircle className="h-4 w-4 text-amber-500" />
                      <AlertDescription className="font-mono text-mono-sm">
                        This preview expires in {generatedContent.expiresIn}. Sign up to save your work!
                      </AlertDescription>
                    </Alert>
                  </CardContent>
                </Card>
              </div>
            ) : null}
          </motion.div>
        )

      case 4: // Save/Auth
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-md mx-auto"
          >
            {!showAuthForm ? (
              <div className="text-center space-y-6">
                <Save className="w-16 h-16 mx-auto text-primary" />
                <h3 className="text-display-md font-literary-display">
                  Save Your Story
                </h3>
                <p className="text-mono-base text-muted-foreground font-mono">
                  Create a free account to save your generated story and start writing
                </p>
                <div className="space-y-3">
                  <Button
                    size="lg"
                    className="w-full font-mono"
                    onClick={() => {
                      setAuthMode('signup')
                      setShowAuthForm(true)
                    }}
                  >
                    <User className="mr-2" />
                    Create Account
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="w-full font-mono"
                    onClick={() => {
                      setAuthMode('login')
                      setShowAuthForm(true)
                    }}
                  >
                    <Lock className="mr-2" />
                    Sign In
                  </Button>
                </div>
              </div>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="font-literary-display text-display-sm">
                    {authMode === 'signup' ? 'Create Your Account' : 'Welcome Back'}
                  </CardTitle>
                  <CardDescription className="font-mono text-mono-sm">
                    {authMode === 'signup' 
                      ? 'Save your story and continue writing'
                      : 'Sign in to save your generated story'
                    }
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Simple auth form - would integrate with your auth system */}
                  <form onSubmit={(e) => {
                    e.preventDefault()
                    // Handle auth then save story
                    saveGeneratedStory()
                  }} className="space-y-4">
                    <div>
                      <Label htmlFor="email" className="font-mono text-mono-sm">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        className="font-mono"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="password" className="font-mono text-mono-sm">Password</Label>
                      <Input
                        id="password"
                        type="password"
                        className="font-mono"
                        required
                      />
                    </div>
                    <Button type="submit" className="w-full font-mono">
                      {authMode === 'signup' ? 'Create Account & Save' : 'Sign In & Save'}
                    </Button>
                    <Button
                      type="button"
                      variant="link"
                      className="w-full font-mono text-mono-sm"
                      onClick={() => setAuthMode(authMode === 'signup' ? 'login' : 'signup')}
                    >
                      {authMode === 'signup' 
                        ? 'Already have an account? Sign in'
                        : "Don't have an account? Sign up"
                      }
                    </Button>
                  </form>
                </CardContent>
              </Card>
            )}
          </motion.div>
        )

      default:
        return null
    }
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress bar */}
      <div className="mb-8">
        <Progress value={(currentStep + 1) / wizardSteps.length * 100} className="mb-4" />
        <div className="flex justify-between">
          {wizardSteps.map((step, index) => {
            const Icon = step.icon
            return (
              <div
                key={step.id}
                className={`flex flex-col items-center ${
                  index <= currentStep ? 'text-primary' : 'text-muted-foreground'
                }`}
              >
                <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
                  index < currentStep 
                    ? 'bg-primary border-primary text-primary-foreground'
                    : index === currentStep
                    ? 'border-primary'
                    : 'border-muted'
                }`}>
                  {index < currentStep ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    <Icon className="w-5 h-5" />
                  )}
                </div>
                <span className="text-mono-xs mt-2 hidden md:block font-mono">
                  {step.title}
                </span>
              </div>
            )
          })}
        </div>
      </div>

      {/* Step content */}
      <Card className="min-h-[400px]">
        <CardHeader>
          <CardTitle className="text-display-md font-literary-display">
            {wizardSteps[currentStep].title}
          </CardTitle>
          <CardDescription className="font-mono text-mono-base">
            {wizardSteps[currentStep].description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AnimatePresence mode="wait">
            {renderStepContent()}
          </AnimatePresence>
        </CardContent>
      </Card>

      {/* Navigation buttons */}
      <div className="flex justify-between mt-6">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0 || isGenerating}
          className="font-mono"
        >
          <ArrowLeft className="mr-2 w-4 h-4" />
          Previous
        </Button>
        
        <Button
          onClick={handleNext}
          disabled={!validateCurrentStep() || isGenerating || (currentStep === 4 && showAuthForm)}
          className="font-mono"
        >
          {currentStep === wizardSteps.length - 1 ? (
            <>
              Save Story
              <Save className="ml-2 w-4 h-4" />
            </>
          ) : currentStep === 2 ? (
            <>
              Generate Story
              <Sparkles className="ml-2 w-4 h-4" />
            </>
          ) : (
            <>
              Next
              <ArrowRight className="ml-2 w-4 h-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  )
}