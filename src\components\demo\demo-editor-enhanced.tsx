"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { PenTool } from 'lucide-react'
import { Sparkles } from 'lucide-react'
import { BookOpen } from 'lucide-react'
import { Users } from 'lucide-react'
import { Target } from 'lucide-react'
import { Play } from 'lucide-react'
import { Pause } from 'lucide-react'
import { RotateCcw } from 'lucide-react'
import { Eye } from 'lucide-react'
import { EyeOff } from 'lucide-react'
import { Lightbulb } from 'lucide-react'
import { MessageSquare } from 'lucide-react'
import { FileText } from 'lucide-react'
import { Zap } from 'lucide-react'
import { Brain } from 'lucide-react'
import { Clock } from 'lucide-react'
import { CheckCircle } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { TrendingUp } from 'lucide-react'
import { Save } from 'lucide-react'
import { Download } from 'lucide-react'
import { Share } from 'lucide-react'
import { Search } from 'lucide-react'
import { Type } from 'lucide-react'
import { Volume2 } from 'lucide-react'
import { Maximize2 } from 'lucide-react'
import { ChevronDown } from 'lucide-react'
import { ChevronRight } from 'lucide-react';

const sampleContent = `Chapter 12: The Crystal's Whisper

The ancient chamber hummed with ethereal energy as Aria stepped across the threshold. Crystalline formations jutted from every surface, their faceted edges catching and refracting the pale blue light that seemed to emanate from within the stones themselves.

"The Heart of Aethermoor," Marcus whispered behind her, his voice barely audible above the crystal's resonant song. "I never thought I'd see it with my own eyes."

Aria approached the central pedestal where the largest crystal pulsed with an inner fire. As her fingers neared its surface, visions flooded her mind—glimpses of the realm's forgotten history, the rise and fall of the Crystal Mages, and something else... something darker lurking in the shadows of memory.

"Aria!" Zara's urgent call snapped her back to the present. "The entrance is sealing!"

Behind them, the stone archway was slowly grinding shut, ancient mechanisms finally succumbing to the weight of centuries. They had perhaps minutes before they would be trapped in this sacred chamber forever.

But the crystal's song grew stronger, more insistent. It was trying to tell her something—something crucial about the Shadow King's true weakness. If she could just listen a little longer...

The chamber shuddered as another section of the entrance collapsed. Dust and debris rained down from the ceiling, and the crystal's light flickered ominously.

"We need to go, now!" Marcus grabbed Aria's arm, but she pulled away.

"Wait," she breathed, pressing her palm against the crystal's surface. "I can see it... the Shadow King's origin. He was once like us, a Crystal Mage who—"

The vision shattered as Zara's magic blast cleared a path through the falling stones. "Story time later, survival now!"

Aria clutched the fragment of knowledge she'd gained as they raced toward the rapidly shrinking exit. The crystal's final whisper followed them: "The heart remembers what the mind forgets..."`;

const aiSuggestions = [
  {
    id: 1,
    type: 'enhancement',
    text: 'Consider adding more sensory details about the crystal\'s song - what does it sound like?',
    position: 156,
    confidence: 0.92,
    category: 'Sensory Details'
  },
  {
    id: 2,
    type: 'character',
    text: 'Marcus\'s reaction seems understated for seeing something legendary. Perhaps show more awe?',
    position: 298,
    confidence: 0.87,
    category: 'Character Development'
  },
  {
    id: 3,
    type: 'pacing',
    text: 'The transition between the vision and Zara\'s call could be smoother.',
    position: 654,
    confidence: 0.79,
    category: 'Pacing & Flow'
  },
  {
    id: 4,
    type: 'dialogue',
    text: 'Zara\'s final line could be more characteristic of her personality.',
    position: 1456,
    confidence: 0.85,
    category: 'Dialogue'
  },
  {
    id: 5,
    type: 'world-building',
    text: 'The crystal\'s whisper could tie into established lore from Chapter 3.',
    position: 1523,
    confidence: 0.91,
    category: 'Consistency'
  }
];

const chapterOutline = [
  { id: 1, title: "The Approach", status: "complete", wordCount: 1247, progress: 100 },
  { id: 2, title: "Guardian's Challenge", status: "complete", wordCount: 892, progress: 100 },
  { id: 3, title: "The Crystal Chamber", status: "current", wordCount: 1634, progress: 75 },
  { id: 4, title: "Visions of the Past", status: "planned", wordCount: 0, progress: 0 },
  { id: 5, title: "The Shadow's Truth", status: "planned", wordCount: 0, progress: 0 },
  { id: 6, title: "The Escape", status: "planned", wordCount: 0, progress: 0 },
];

const storyBibleData = {
  characters: [
    { name: "Aria Moonwhisper", role: "Protagonist", status: "Active", chapters: "1-12" },
    { name: "Marcus Stormwind", role: "Mentor", status: "Active", chapters: "2-12" },
    { name: "Zara Nightblade", role: "Ally", status: "Active", chapters: "4-12" },
    { name: "Shadow King", role: "Antagonist", status: "Mentioned", chapters: "1,5,8,12" }
  ],
  locations: [
    { name: "Crystal Chamber", type: "Sacred Site", status: "Current", description: "Ancient chamber housing the Heart of Aethermoor" },
    { name: "Whispering Woods", type: "Forest", status: "Previous", description: "Mystical forest surrounding the chamber" }
  ],
  plotThreads: [
    { name: "Crystal Quest", status: "Active", progress: 75 },
    { name: "Shadow King's Origin", status: "Developing", progress: 25 },
    { name: "Aria's Powers", status: "Ongoing", progress: 60 }
  ]
};

export function DemoEditorEnhanced() {
  const [isTyping, setIsTyping] = useState(false);
  const [currentContent, setCurrentContent] = useState("");
  const [wordCount, setWordCount] = useState(1634);
  const [selectedSuggestion, setSelectedSuggestion] = useState<number | null>(null);
  const [isOutlineCollapsed, setIsOutlineCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState("editor");
  const [isAiPanelOpen, setIsAiPanelOpen] = useState(true);
  const [writingGoal, setWritingGoal] = useState(2000);
  const [sessionWords, setSessionWords] = useState(634);

  useEffect(() => {
    // Simulate typing effect
    if (isTyping) {
      let index = 0;
      const interval = setInterval(() => {
        if (index < sampleContent.length) {
          setCurrentContent(sampleContent.slice(0, index + 1));
          setWordCount(Math.floor(sampleContent.slice(0, index + 1).split(' ').length * 1.2));
          setSessionWords(Math.floor(sampleContent.slice(0, index + 1).split(' ').length * 0.4));
          index += 3;
        } else {
          setIsTyping(false);
          clearInterval(interval);
        }
      }, 50);
      return () => clearInterval(interval);
    }
    return undefined;
  }, [isTyping]);

  const startTyping = () => {
    setCurrentContent("");
    setWordCount(1000);
    setSessionWords(0);
    setIsTyping(true);
  };

  const progressPercentage = (wordCount / writingGoal) * 100;

  return (
    <div className="w-full h-full bg-background">
      {/* Header Toolbar */}
      <div className="border-b border-border bg-card/50 backdrop-blur-sm p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
            <div className="flex items-center gap-2">
              <PenTool className="w-5 h-5 text-primary" />
              <h2 className="text-lg font-semibold">The Crystal Saga - Chapter 12</h2>
              <Badge variant="outline" className="border-primary/50 text-primary">
                Demo Mode
              </Badge>
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="w-4 h-4" />
              <span>Last saved: 2 minutes ago</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm">
              <Search className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Save className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Download className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Share className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Maximize2 className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Writing Progress */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4 sm:gap-5 lg:gap-6">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Daily Goal</span>
              <span className="font-medium">{wordCount}/{writingGoal} words</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
          <div className="space-y-1">
            <div className="text-sm text-muted-foreground">Session</div>
            <div className="text-lg font-semibold text-primary">{sessionWords} words</div>
          </div>
          <div className="space-y-1">
            <div className="text-sm text-muted-foreground">Chapter</div>
            <div className="text-lg font-semibold">{wordCount} words</div>
          </div>
          <div className="space-y-1">
            <div className="text-sm text-muted-foreground">Reading Time</div>
            <div className="text-lg font-semibold">{Math.ceil(wordCount / 250)} min</div>
          </div>
        </div>
      </div>

      <div className="flex h-[calc(100vh-200px)]">
        {/* Left Sidebar - Chapter Outline */}
        <div className={`border-r border-border bg-card/30 transition-all duration-300 ${isOutlineCollapsed ? 'w-12' : 'w-80'}`}>
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              {!isOutlineCollapsed && (
                <h3 className="font-semibold flex items-center gap-2">
                  <BookOpen className="w-4 h-4" />
                  Chapter Outline
                </h3>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOutlineCollapsed(!isOutlineCollapsed)}
              >
                {isOutlineCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
              </Button>
            </div>
            
            {!isOutlineCollapsed && (
              <ScrollArea className="h-[400px]">
                <div className="space-y-2">
                  {chapterOutline.map((chapter) => (
                    <Card key={chapter.id} className={`p-3 cursor-pointer transition-colors ${
                      chapter.status === 'current' ? 'border-primary/50 bg-primary/10' : 'hover:bg-accent'
                    }`}>
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-sm">{chapter.title}</span>
                        <Badge variant={chapter.status === 'complete' ? 'default' : chapter.status === 'current' ? 'secondary' : 'outline'} className="text-xs">
                          {chapter.status}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{chapter.wordCount} words</span>
                        <div className="flex items-center gap-1">
                          <Progress value={chapter.progress} className="w-12 h-1" />
                          <span>{chapter.progress}%</span>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            )}
          </div>
        </div>

        {/* Main Editor Area */}
        <div className="flex-1 flex flex-col">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <div className="border-b border-border px-4">
              <TabsList className="grid w-full max-w-md grid-cols-3">
                <TabsTrigger value="editor">Editor</TabsTrigger>
                <TabsTrigger value="preview">Preview</TabsTrigger>
                <TabsTrigger value="outline">Outline</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="editor" className="flex-1 p-4">
              <div className="h-full">
                <Textarea
                  value={currentContent || sampleContent}
                  onChange={(e) => setCurrentContent(e.target.value)}
                  placeholder="Start writing your story..."
                  className="w-full h-full resize-none border-0 focus:ring-0 text-base leading-relaxed font-mono"
                  style={{ minHeight: '500px' }}
                />
              </div>
            </TabsContent>

            <TabsContent value="preview" className="flex-1 p-4">
              <ScrollArea className="h-full">
                <div className="prose prose-lg max-w-none">
                  <div className="whitespace-pre-wrap text-base leading-relaxed">
                    {currentContent || sampleContent}
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="outline" className="flex-1 p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Users className="w-5 h-5" />
                      Active Characters
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {storyBibleData.characters.map((char, idx) => (
                        <div key={idx} className="flex items-center justify-between p-2 rounded border">
                          <div>
                            <div className="font-medium">{char.name}</div>
                            <div className="text-sm text-muted-foreground">{char.role}</div>
                          </div>
                          <Badge variant={char.status === 'Active' ? 'default' : 'secondary'}>
                            {char.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Target className="w-5 h-5" />
                      Plot Threads
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {storyBibleData.plotThreads.map((thread, idx) => (
                        <div key={idx} className="p-2 rounded border">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium">{thread.name}</span>
                            <Badge variant="outline">{thread.status}</Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            <Progress value={thread.progress} className="flex-1 h-2" />
                            <span className="text-sm text-muted-foreground">{thread.progress}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Sidebar - AI Assistant */}
        {isAiPanelOpen && (
          <div className="w-96 border-l border-border bg-card/30">
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <Sparkles className="w-4 h-4 text-primary" />
                  AI Assistant
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsAiPanelOpen(false)}
                >
                  <Eye className="w-4 h-4" />
                </Button>
              </div>

              <Tabs defaultValue="suggestions" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
                  <TabsTrigger value="chat">Chat</TabsTrigger>
                  <TabsTrigger value="bible">Bible</TabsTrigger>
                </TabsList>

                <TabsContent value="suggestions" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Writing Suggestions</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={startTyping}
                      disabled={isTyping}
                    >
                      {isTyping ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                      {isTyping ? 'Pause' : 'Demo'}
                    </Button>
                  </div>
                  
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-3">
                      {aiSuggestions.map((suggestion) => (
                        <Card 
                          key={suggestion.id} 
                          className={`p-3 cursor-pointer transition-colors ${
                            selectedSuggestion === suggestion.id ? 'border-primary/50 bg-primary/10' : 'hover:bg-accent'
                          }`}
                          onClick={() => setSelectedSuggestion(suggestion.id)}
                        >
                          <div className="flex items-start gap-2">
                            <div className="flex-shrink-0 mt-1">
                              {suggestion.type === 'enhancement' && <Lightbulb className="w-4 h-4 text-yellow-500" />}
                              {suggestion.type === 'character' && <Users className="w-4 h-4 text-blue-500" />}
                              {suggestion.type === 'pacing' && <TrendingUp className="w-4 h-4 text-green-500" />}
                              {suggestion.type === 'dialogue' && <MessageSquare className="w-4 h-4 text-purple-500" />}
                              {suggestion.type === 'world-building' && <BookOpen className="w-4 h-4 text-orange-500" />}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge variant="outline" className="text-xs">
                                  {suggestion.category}
                                </Badge>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                  <span className="text-xs text-muted-foreground">{Math.round(suggestion.confidence * 100)}%</span>
                                </div>
                              </div>
                              <p className="text-sm">{suggestion.text}</p>
                              <p className="text-xs text-muted-foreground mt-1">Position: {suggestion.position}</p>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="chat" className="space-y-4">
                  <div className="space-y-3">
                    <div className="p-3 rounded bg-primary/10 border border-primary/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Brain className="w-4 h-4 text-primary" />
                        <span className="text-sm font-medium">AI Assistant</span>
                      </div>
                      <p className="text-sm">I notice you're writing a pivotal scene with the crystal. Would you like me to help enhance the mystical atmosphere or develop the character dynamics?</p>
                    </div>
                    
                    <div className="p-3 rounded bg-accent">
                      <p className="text-sm">How can I make the crystal's power feel more mysterious and ancient?</p>
                    </div>
                    
                    <div className="p-3 rounded bg-primary/10 border border-primary/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Brain className="w-4 h-4 text-primary" />
                        <span className="text-sm font-medium">AI Assistant</span>
                      </div>
                      <p className="text-sm">Consider adding sensory details beyond sight - perhaps the crystal hums with a frequency that resonates in their bones, or its light casts shadows that move independently. You could also reference how it connects to the ancient magic system you established in Chapter 3.</p>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Textarea 
                      placeholder="Ask the AI assistant anything..."
                      className="flex-1 min-h-[60px]"
                    />
                    <Button size="sm" className="self-end">
                      <MessageSquare className="w-4 h-4" />
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="bible" className="space-y-4">
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2 flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          Characters in Scene
                        </h4>
                        <div className="space-y-2">
                          {storyBibleData.characters.filter(c => c.status === 'Active').map((char, idx) => (
                            <div key={idx} className="p-2 rounded border text-sm">
                              <div className="font-medium">{char.name}</div>
                              <div className="text-muted-foreground">{char.role} • Chapters {char.chapters}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <div>
                        <h4 className="font-medium mb-2 flex items-center gap-2">
                          <BookOpen className="w-4 h-4" />
                          Current Location
                        </h4>
                        <div className="p-2 rounded border text-sm">
                          <div className="font-medium">{storyBibleData.locations[0].name}</div>
                          <div className="text-muted-foreground">{storyBibleData.locations[0].description}</div>
                        </div>
                      </div>
                    </div>
                  </ScrollArea>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        )}
      </div>

      {/* Bottom Status Bar */}
      <div className="border-t border-border bg-card/50 backdrop-blur-sm p-2">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-4 sm:gap-5 lg:gap-6">
            <span>Line 23, Column 45</span>
            <span>Words: {wordCount}</span>
            <span>Characters: {(currentContent || sampleContent).length}</span>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" className="h-6 px-2">
              <Type className="w-3 h-3" />
            </Button>
            <Button variant="ghost" size="sm" className="h-6 px-2">
              <Volume2 className="w-3 h-3" />
            </Button>
            {!isAiPanelOpen && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2"
                onClick={() => setIsAiPanelOpen(true)}
              >
                <EyeOff className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
