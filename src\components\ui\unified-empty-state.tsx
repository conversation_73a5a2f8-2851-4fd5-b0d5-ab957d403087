'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { FileText } from 'lucide-react'
import { FolderOpen } from 'lucide-react'
import { Search } from 'lucide-react'
import { Plus } from 'lucide-react'
import { Users } from 'lucide-react'
import { BookOpen } from 'lucide-react'
import { PenTool } from 'lucide-react'
import { Sparkles } from 'lucide-react'
import { Database } from 'lucide-react'
import { BarChart3 } from 'lucide-react'
import { MessageSquare } from 'lucide-react'
import { Image as ImageIcon } from 'lucide-react'
import { Calendar } from 'lucide-react'
import { Settings } from 'lucide-react'
import { type LucideIcon } from 'lucide-react'

// ============================================================================
// UNIFIED EMPTY STATE SYSTEM - Consolidates all empty state components
// ============================================================================

// Empty state types
type EmptyStateType = 
  | 'no-data'
  | 'no-results'
  | 'no-projects'
  | 'no-chapters'
  | 'no-characters'
  | 'no-analytics'
  | 'no-comments'
  | 'no-images'
  | 'no-events'
  | 'error'
  | 'custom'

interface EmptyStateConfig {
  icon: LucideIcon
  title: string
  description: string
  actionLabel?: string
  actionIcon?: LucideIcon
}

// Preset configurations
const emptyStateConfigs: Record<EmptyStateType, EmptyStateConfig> = {
  'no-data': {
    icon: Database,
    title: 'No data yet',
    description: 'Start creating to see content here.',
    actionLabel: 'Get Started',
    actionIcon: Plus
  },
  'no-results': {
    icon: Search,
    title: 'No results found',
    description: 'Try adjusting your search or filters.',
    actionLabel: 'Clear Filters',
    actionIcon: X
  },
  'no-projects': {
    icon: BookOpen,
    title: 'No projects yet',
    description: 'Create your first project to begin your writing journey.',
    actionLabel: 'Create Project',
    actionIcon: Plus
  },
  'no-chapters': {
    icon: FileText,
    title: 'No chapters written',
    description: 'Start writing your first chapter to bring your story to life.',
    actionLabel: 'Write Chapter',
    actionIcon: PenTool
  },
  'no-characters': {
    icon: Users,
    title: 'No characters created',
    description: 'Create compelling characters to populate your story.',
    actionLabel: 'Create Character',
    actionIcon: Plus
  },
  'no-analytics': {
    icon: BarChart3,
    title: 'No analytics data',
    description: 'Start writing to see your progress and insights.',
    actionLabel: 'Start Writing',
    actionIcon: PenTool
  },
  'no-comments': {
    icon: MessageSquare,
    title: 'No comments yet',
    description: 'Share your work to receive feedback.',
    actionLabel: 'Share Project',
    actionIcon: Users
  },
  'no-images': {
    icon: ImageIcon,
    title: 'No images uploaded',
    description: 'Add images to enhance your story.',
    actionLabel: 'Upload Image',
    actionIcon: Plus
  },
  'no-events': {
    icon: Calendar,
    title: 'No events scheduled',
    description: 'Plan your story timeline with events.',
    actionLabel: 'Add Event',
    actionIcon: Plus
  },
  'error': {
    icon: X,
    title: 'Something went wrong',
    description: 'Unable to load content. Please try again.',
    actionLabel: 'Retry',
    actionIcon: RefreshCw
  },
  'custom': {
    icon: FolderOpen,
    title: 'Nothing here',
    description: 'No content to display.',
  }
}

// Import missing icons
import { X } from 'lucide-react'
import { RefreshCw } from 'lucide-react'

// ============================================================================
// MAIN EMPTY STATE COMPONENT
// ============================================================================

interface EmptyStateProps {
  type?: EmptyStateType
  icon?: LucideIcon
  title?: string
  description?: string
  action?: () => void
  actionLabel?: string
  actionIcon?: LucideIcon
  size?: 'sm' | 'md' | 'lg'
  className?: string
  children?: React.ReactNode
}

export function EmptyState({
  type = 'no-data',
  icon: CustomIcon,
  title: customTitle,
  description: customDescription,
  action,
  actionLabel: customActionLabel,
  actionIcon: CustomActionIcon,
  size = 'md',
  className,
  children
}: EmptyStateProps) {
  // Get config
  const config = emptyStateConfigs[type]
  
  // Use custom values or fallback to config
  const Icon = CustomIcon || config.icon
  const title = customTitle || config.title
  const description = customDescription || config.description
  const actionLabel = customActionLabel || config.actionLabel
  const ActionIcon = CustomActionIcon || config.actionIcon

  // Size classes
  const sizeClasses = {
    sm: {
      container: 'py-8 px-4',
      icon: 'h-8 w-8',
      iconWrapper: 'h-12 w-12',
      title: 'text-display-xs',
      description: 'text-mono-sm',
      spacing: 'space-y-2'
    },
    md: {
      container: 'py-12 px-6',
      icon: 'h-10 w-10',
      iconWrapper: 'h-16 w-16',
      title: 'text-display-sm',
      description: 'text-mono-base',
      spacing: 'space-y-3'
    },
    lg: {
      container: 'py-16 px-8',
      icon: 'h-12 w-12',
      iconWrapper: 'h-20 w-20',
      title: 'text-display-md',
      description: 'text-mono-lg',
      spacing: 'space-y-4'
    }
  }

  const sizes = sizeClasses[size]

  return (
    <div 
      className={cn(
        'flex flex-col items-center justify-center text-center',
        sizes.container,
        className
      )}
    >
      <div className={cn(sizes.spacing, 'max-w-md')}>
        {/* Icon */}
        <div className={cn(
          'mx-auto rounded-full bg-muted/50 flex items-center justify-center',
          sizes.iconWrapper
        )}>
          <Icon className={cn('text-muted-foreground', sizes.icon)} />
        </div>

        {/* Title */}
        <h3 className={cn('font-literary-display font-semibold', sizes.title)}>
          {title}
        </h3>

        {/* Description */}
        <p className={cn('font-mono text-muted-foreground', sizes.description)}>
          {description}
        </p>

        {/* Action button */}
        {action && actionLabel && (
          <Button
            onClick={action}
            className="font-literary-display mt-2"
            size={size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'default'}
          >
            {ActionIcon && <ActionIcon className="mr-2 h-4 w-4" />}
            {actionLabel}
          </Button>
        )}

        {/* Custom children */}
        {children}
      </div>
    </div>
  )
}

// ============================================================================
// SPECIALIZED EMPTY STATES
// ============================================================================

export function NoDataState({ action, className }: { action?: () => void; className?: string }) {
  return <EmptyState type="no-data" action={action} className={className} />
}

export function NoResultsState({ onClear, className }: { onClear?: () => void; className?: string }) {
  return <EmptyState type="no-results" action={onClear} className={className} />
}

export function NoProjectsState({ onCreate, className }: { onCreate?: () => void; className?: string }) {
  return <EmptyState type="no-projects" action={onCreate} className={className} />
}

export function NoChaptersState({ onWrite, className }: { onWrite?: () => void; className?: string }) {
  return <EmptyState type="no-chapters" action={onWrite} className={className} />
}

export function NoCharactersState({ onCreate, className }: { onCreate?: () => void; className?: string }) {
  return <EmptyState type="no-characters" action={onCreate} className={className} />
}

// ============================================================================
// LIST EMPTY STATE
// ============================================================================

interface ListEmptyStateProps {
  icon?: LucideIcon
  title: string
  description?: string
  actionLabel?: string
  onAction?: () => void
  className?: string
}

export function ListEmptyState({
  icon: Icon = FolderOpen,
  title,
  description = 'No items to display',
  actionLabel,
  onAction,
  className
}: ListEmptyStateProps) {
  return (
    <div className={cn(
      'flex flex-col items-center justify-center py-8 px-4 border-2 border-dashed border-muted rounded-lg',
      className
    )}>
      <Icon className="h-8 w-8 text-muted-foreground mb-3" />
      <p className="font-mono font-bold text-mono-base mb-1">{title}</p>
      <p className="font-mono text-mono-sm text-muted-foreground">{description}</p>
      {actionLabel && onAction && (
        <Button
          onClick={onAction}
          variant="outline"
          size="sm"
          className="mt-3 font-literary-display"
        >
          <Plus className="mr-2 h-3 w-3" />
          {actionLabel}
        </Button>
      )}
    </div>
  )
}

// ============================================================================
// INLINE EMPTY STATE
// ============================================================================

interface InlineEmptyStateProps {
  text: string
  icon?: LucideIcon
  className?: string
}

export function InlineEmptyState({ 
  text, 
  icon: Icon = Sparkles,
  className 
}: InlineEmptyStateProps) {
  return (
    <div className={cn(
      'flex items-center gap-2 text-muted-foreground text-mono-sm font-mono',
      className
    )}>
      <Icon className="h-4 w-4" />
      <span>{text}</span>
    </div>
  )
}