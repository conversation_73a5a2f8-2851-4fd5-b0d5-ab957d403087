'use client'

import { MemoryDashboard } from '@/components/memory/memory-dashboard'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'
import { PageLoading } from '@/components/ui/unified-loading'
import { useAsync } from '@/hooks/use-async'
import { useToast } from '@/hooks/use-toast'
import { UnifiedErrorBoundary } from '@/components/error/unified-error-boundary'

interface MemoryPageProps {
  params: {
    id: string
  }
}

export default function MemoryPage({ params }: MemoryPageProps) {
  const { toast } = useToast()
  const { loading, error } = useAsync(async () => {
    // Check if project exists and user has access
    const response = await fetch(`/api/projects/${params.id}`)
    if (!response.ok) {
      throw new Error('Project not found or access denied')
    }
  }, [params.id])

  if (loading) {
    return <PageLoading text="Loading AI memory management..." />
  }

  if (error) {
    return (
      <div className="container-wide mx-auto p-6">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-2">Unable to access project</h2>
          <p className="text-muted-foreground mb-4">{error.message}</p>
          <Link href="/dashboard">
            <Button>Back to Dashboard</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <UnifiedErrorBoundary>
      <div className="container-wide mx-auto p-6">
        <div className="mb-6">
          <Link href={`/projects/${params.id}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Project
            </Button>
          </Link>
        </div>

        <div className="mb-8">
          <h1 className="text-3xl font-bold">AI Memory Management</h1>
          <p className="text-muted-foreground mt-2">
            Optimize your AI's context usage for better performance and cost efficiency
          </p>
        </div>

        <MemoryDashboard 
          projectId={params.id}
          maxContextSize={150000}
          onOptimize={async (strategy) => {
            try {
              const response = await fetch(`/api/projects/${params.id}/memory/optimize`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ strategy })
              })
              
              if (!response.ok) throw new Error('Failed to optimize memory')
              
              const result = await response.json()
              
              toast({
                title: 'Memory Optimized',
                description: `Reduced tokens by ${result.tokensSaved || 0}. ${result.message || ''}`
              })
              
              // Refresh the dashboard will be handled by MemoryDashboard component
            } catch (error) {
              toast({
                title: 'Optimization Failed',
                description: 'Unable to optimize memory at this time',
                variant: 'destructive'
              })
            }
          }}
        />
      </div>
    </UnifiedErrorBoundary>
  )
}