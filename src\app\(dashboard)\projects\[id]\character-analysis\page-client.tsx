'use client'

import { useState } from 'react'
import { useParams } from 'next/navigation'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CharacterArcTimeline } from '@/components/analysis/character-arc-timeline'
import { CharacterDevelopmentGrid } from '@/components/analysis/character-development-grid'
import { CharacterInsightsPanel } from '@/components/analysis/character-insights-panel'
import { UnifiedErrorBoundary } from '@/components/error/unified-error-boundary'
import { PageHeader } from '@/components/layout/page-header'
import { User } from 'lucide-react'
import { Activity } from 'lucide-react'
import { Grid3x3 } from 'lucide-react'
import { Lightbulb } from 'lucide-react'
import { Download } from 'lucide-react'
import { Share2 } from 'lucide-react'
import { FileText } from 'lucide-react'

export function CharacterAnalysisPage() {
  const params = useParams()
  const projectId = params.id as string
  const [activeTab, setActiveTab] = useState('timeline')

  return (
    <UnifiedErrorBoundary>
      <div className="container mx-auto p-6 space-y-6">
        <PageHeader
          title="Character Analysis"
          description="Deep insights into character development and story arcs"
          icon={User}
          actions={
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-2" />
                Share Analysis
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Report
              </Button>
            </div>
          }
        />

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 lg:w-[400px]">
            <TabsTrigger value="timeline" className="flex items-center gap-2">
              <Activity className="w-4 h-4" />
              Arc Timeline
            </TabsTrigger>
            <TabsTrigger value="grid" className="flex items-center gap-2">
              <Grid3x3 className="w-4 h-4" />
              Development Grid
            </TabsTrigger>
            <TabsTrigger value="insights" className="flex items-center gap-2">
              <Lightbulb className="w-4 h-4" />
              AI Insights
            </TabsTrigger>
          </TabsList>

          <TabsContent value="timeline" className="space-y-6">
            <CharacterArcTimeline projectId={projectId} />
          </TabsContent>

          <TabsContent value="grid" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Character Development Analysis</CardTitle>
                <CardDescription>
                  Track character growth across multiple dimensions throughout your story
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  The development grid visualizes how your characters evolve across different aspects
                  like goals, relationships, beliefs, and skills. Use this to ensure consistent and
                  meaningful character progression.
                </p>
                <CharacterDevelopmentGrid 
                  gridData={{
                    characterName: "Select a character",
                    chapters: [],
                    overallProgress: 0
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="insights" className="space-y-6">
            <CharacterInsightsPanel projectId={projectId} />
          </TabsContent>
        </Tabs>

        {/* Additional Analysis Tools */}
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Character Consistency Report
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                AI-powered analysis of character voice, behavior, and trait consistency across chapters.
              </p>
              <Button variant="outline" className="w-full">
                Generate Consistency Report
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5" />
                Relationship Dynamics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Visualize how character relationships evolve and interconnect throughout your story.
              </p>
              <Button variant="outline" className="w-full">
                View Relationship Map
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </UnifiedErrorBoundary>
  )
}