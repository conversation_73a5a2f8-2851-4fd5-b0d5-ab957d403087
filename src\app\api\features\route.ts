import { NextRequest, NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { UserService } from '@/lib/services/user-service';
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { logger } from '@/lib/services/logger';

interface FeatureFlag {
  key: string;
  name: string;
  description: string;
  enabled: boolean;
  rolloutPercentage: number;
  requiredTier?: string;
  beta?: boolean;
  experimental?: boolean;
  category: string;
}

// Feature flags configuration
const FEATURE_FLAGS: Record<string, FeatureFlag> = {
  'ai_story_architect': {
    key: 'ai_story_architect',
    name: 'AI Story Architect',
    description: 'Advanced AI-powered story structure planning',
    enabled: true,
    rolloutPercentage: 100,
    requiredTier: 'pro',
    category: 'ai'
  },
  'collaborative_editing': {
    key: 'collaborative_editing',
    name: 'Collaborative Editing',
    description: 'Real-time collaborative editing with team members',
    enabled: true,
    rolloutPercentage: 100,
    requiredTier: 'team',
    category: 'collaboration'
  },
  'advanced_analytics': {
    key: 'advanced_analytics',
    name: 'Advanced Analytics',
    description: 'Detailed writing analytics and insights',
    enabled: true,
    rolloutPercentage: 100,
    requiredTier: 'pro',
    category: 'analytics'
  },
  'voice_consistency_check': {
    key: 'voice_consistency_check',
    name: 'Voice Consistency Check',
    description: 'AI-powered voice consistency analysis across chapters',
    enabled: true,
    rolloutPercentage: 95,
    requiredTier: 'pro',
    beta: true,
    category: 'ai'
  },
  'series_management': {
    key: 'series_management',
    name: 'Series Management',
    description: 'Manage multi-book series with continuity tracking',
    enabled: true,
    rolloutPercentage: 100,
    requiredTier: 'pro',
    category: 'projects'
  },
  'export_formats': {
    key: 'export_formats',
    name: 'Advanced Export Formats',
    description: 'Export to EPUB, PDF, and other professional formats',
    enabled: true,
    rolloutPercentage: 100,
    requiredTier: 'free',
    category: 'export'
  },
  'ai_character_generator': {
    key: 'ai_character_generator',
    name: 'AI Character Generator',
    description: 'Generate detailed character profiles with AI',
    enabled: true,
    rolloutPercentage: 90,
    requiredTier: 'pro',
    beta: true,
    category: 'ai'
  },
  'writing_goals_v2': {
    key: 'writing_goals_v2',
    name: 'Enhanced Writing Goals',
    description: 'Advanced goal tracking with smart recommendations',
    enabled: true,
    rolloutPercentage: 75,
    requiredTier: 'free',
    beta: true,
    category: 'productivity'
  },
  'timeline_visualization': {
    key: 'timeline_visualization',
    name: 'Timeline Visualization',
    description: 'Interactive timeline view of story events',
    enabled: true,
    rolloutPercentage: 80,
    requiredTier: 'pro',
    beta: true,
    category: 'visualization'
  },
  'ai_editing_assistant': {
    key: 'ai_editing_assistant',
    name: 'AI Editing Assistant',
    description: 'Advanced AI-powered prose editing and suggestions',
    enabled: false,
    rolloutPercentage: 10,
    requiredTier: 'pro',
    experimental: true,
    category: 'ai'
  },
  'mood_boarding': {
    key: 'mood_boarding',
    name: 'Visual Mood Boards',
    description: 'Create visual mood boards for chapters and characters',
    enabled: false,
    rolloutPercentage: 5,
    requiredTier: 'pro',
    experimental: true,
    category: 'visualization'
  }
};

export async function GET(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get('category');
    const includeDisabled = searchParams.get('includeDisabled') === 'true';
    const betaOnly = searchParams.get('beta') === 'true';
    const experimentalOnly = searchParams.get('experimental') === 'true';

    // Get user's subscription tier
    const userService = new UserService();
    await userService.initialize();
    
    const tierResponse = await userService.getUserSubscriptionTier(user.id);
    const userTier = tierResponse.success ? tierResponse.data : 'free';

    // Filter features based on user's access level and rollout
    let availableFeatures = Object.values(FEATURE_FLAGS);

    // Filter by category if specified
    if (category) {
      availableFeatures = availableFeatures.filter(f => f.category === category);
    }

    // Filter by beta/experimental status
    if (betaOnly) {
      availableFeatures = availableFeatures.filter(f => f.beta === true);
    }

    if (experimentalOnly) {
      availableFeatures = availableFeatures.filter(f => f.experimental === true);
    }

    // Filter by enabled status
    if (!includeDisabled) {
      availableFeatures = availableFeatures.filter(f => f.enabled);
    }

    // Check user access for each feature
    const userFeatures = availableFeatures.map(feature => {
      const hasAccess = checkFeatureAccess(feature, userTier, user.id);
      
      return {
        ...feature,
        hasAccess,
        reason: !hasAccess ? getAccessDenialReason(feature, userTier) : null
      };
    });

    // Get feature categories
    const categories = [...new Set(availableFeatures.map(f => f.category))];

    return NextResponse.json({
      success: true,
      features: userFeatures,
      categories,
      userTier,
      totalFeatures: userFeatures.length,
      accessibleFeatures: userFeatures.filter(f => f.hasAccess).length
    });

  } catch (error) {
    logger.error('Error fetching features:', error);
    return handleAPIError(error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const body = await request.json();
    const { featureKey, enabled } = body;

    if (!featureKey || typeof enabled !== 'boolean') {
      return handleAPIError(new ValidationError('Feature key and enabled status required'));
    }

    const feature = FEATURE_FLAGS[featureKey];
    if (!feature) {
      return NextResponse.json({
        success: false,
        error: 'Feature not found'
      }, { status: 404 });
    }

    // Check if user has access to this feature
    const userService = new UserService();
    await userService.initialize();
    
    const tierResponse = await userService.getUserSubscriptionTier(user.id);
    const userTier = tierResponse.success ? tierResponse.data : 'free';

    const hasAccess = checkFeatureAccess(feature, userTier, user.id);
    
    if (!hasAccess) {
      return NextResponse.json({
        success: false,
        error: 'Access denied to this feature',
        reason: getAccessDenialReason(feature, userTier)
      }, { status: 403 });
    }

    // In a real implementation, you would save user feature preferences to database
    // For now, we'll just return success
    
    logger.info('Feature preference updated', {
      userId: user.id,
      featureKey,
      enabled,
      userTier
    });

    return NextResponse.json({
      success: true,
      message: `Feature ${featureKey} ${enabled ? 'enabled' : 'disabled'}`,
      feature: {
        ...feature,
        enabled,
        hasAccess: true
      }
    });

  } catch (error) {
    logger.error('Error updating feature preference:', error);
    return handleAPIError(error);
  }
}

// Check if user has access to a feature
function checkFeatureAccess(feature: FeatureFlag, userTier: string, userId: string): boolean {
  // Check if feature is globally enabled
  if (!feature.enabled) {
    return false;
  }

  // Check rollout percentage (simple hash-based approach)
  const userHash = hashString(userId) % 100;
  if (userHash >= feature.rolloutPercentage) {
    return false;
  }

  // Check tier requirements
  if (feature.requiredTier) {
    const tierHierarchy = { 'free': 0, 'pro': 1, 'team': 2 };
    const requiredLevel = tierHierarchy[feature.requiredTier as keyof typeof tierHierarchy] || 0;
    const userLevel = tierHierarchy[userTier as keyof typeof tierHierarchy] || 0;
    
    if (userLevel < requiredLevel) {
      return false;
    }
  }

  return true;
}

// Get reason for access denial
function getAccessDenialReason(feature: FeatureFlag, userTier: string): string {
  if (!feature.enabled) {
    return 'Feature is currently disabled';
  }

  if (feature.requiredTier) {
    const tierHierarchy = { 'free': 0, 'pro': 1, 'team': 2 };
    const requiredLevel = tierHierarchy[feature.requiredTier as keyof typeof tierHierarchy] || 0;
    const userLevel = tierHierarchy[userTier as keyof typeof tierHierarchy] || 0;
    
    if (userLevel < requiredLevel) {
      return `Requires ${feature.requiredTier} subscription`;
    }
  }

  return 'Not available for your account';
}

// Simple hash function for consistent user rollout
function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}