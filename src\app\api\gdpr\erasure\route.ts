import { NextRequest, NextResponse } from 'next/server';
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service';
import { UserService } from '@/lib/services/user-service';
import { TaskService } from '@/lib/services/task-service';
import { handleAPIError, ValidationError, AuthenticationError } from '@/lib/api/error-handler';
import { logger } from '@/lib/services/logger';
import { z } from 'zod';

const erasureRequestSchema = z.object({
  confirmation: z.literal(true, {
    errorMap: () => ({ message: 'You must confirm this action to proceed' })
  }),
  reason: z.string().min(10, 'Please provide a reason for account deletion'),
  emailVerification: z.string().email('Valid email required for verification'),
  password: z.string().min(1, 'Password required for verification')
});

export async function POST(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const body = await request.json();
    const validation = erasureRequestSchema.safeParse(body);

    if (!validation.success) {
      return handleAPIError(new ValidationError('Invalid erasure request data'));
    }

    const { confirmation, reason, emailVerification, password } = validation.data;

    // Verify email matches user account
    if (emailVerification !== user.email) {
      return NextResponse.json({
        success: false,
        error: 'Email verification failed. Please enter your account email.'
      }, { status: 400 });
    }

    // Initialize services
    const userService = new UserService();
    const taskService = new TaskService();
    await userService.initialize();
    await taskService.initialize();

    // Check for active subscriptions
    const hasActiveSubResponse = await userService.hasActiveSubscription(user.id);
    if (hasActiveSubResponse.success && hasActiveSubResponse.data) {
      return NextResponse.json({
        success: false,
        error: 'Please cancel your active subscription before requesting account deletion.',
        code: 'ACTIVE_SUBSCRIPTION'
      }, { status: 400 });
    }

    // Record the erasure request for audit purposes
    await userService.recordConsent(user.id, 'data_erasure', true, {
      reason,
      requestedAt: new Date().toISOString(),
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    });

    // Create a background task to handle the erasure
    const taskResponse = await taskService.createTask(user.id, {
      type: 'user_data_erasure',
      payload: {
        userId: user.id,
        userEmail: user.email,
        reason,
        requestedAt: new Date().toISOString()
      },
      priority: 'high'
    });

    if (!taskResponse.success) {
      logger.error('Failed to create erasure task:', taskResponse.error);
      return NextResponse.json({
        success: false,
        error: 'Failed to schedule account deletion. Please try again.'
      }, { status: 500 });
    }

    // Log the erasure request
    logger.info('User data erasure requested', {
      userId: user.id,
      userEmail: user.email,
      reason,
      taskId: taskResponse.data.id
    });

    // NOTE: In a real implementation, you might want to:
    // 1. Send a final confirmation email
    // 2. Give the user a grace period to cancel the request
    // 3. Notify them about backup retention policies
    // 4. Handle the actual deletion in a secure background process

    return NextResponse.json({
      success: true,
      message: 'Account deletion request submitted successfully',
      taskId: taskResponse.data.id,
      estimatedCompletion: '30 days',
      details: {
        requestedAt: new Date().toISOString(),
        status: 'pending',
        taskId: taskResponse.data.id
      }
    });

  } catch (error) {
    logger.error('Error processing erasure request:', error);
    return handleAPIError(error);
  }
}

export async function GET(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const taskService = new TaskService();
    await taskService.initialize();

    // Get any existing erasure requests
    const tasksResponse = await taskService.getUserTasks(user.id, {
      taskType: 'user_data_erasure',
      limit: 10
    });

    if (!tasksResponse.success) {
      logger.error('Failed to fetch erasure tasks:', tasksResponse.error);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch erasure status'
      }, { status: 500 });
    }

    const erasureTasks = tasksResponse.data.map(task => ({
      id: task.id,
      status: task.status,
      requestedAt: task.created_at,
      scheduledFor: task.scheduled_for,
      completedAt: task.completed_at,
      reason: task.payload?.reason,
      estimatedCompletion: task.status === 'pending' ? '30 days' : null
    }));

    return NextResponse.json({
      success: true,
      erasureRequests: erasureTasks,
      hasActiveDeletion: erasureTasks.some(t => t.status === 'pending' || t.status === 'running')
    });

  } catch (error) {
    logger.error('Error fetching erasure status:', error);
    return handleAPIError(error);
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }

    const searchParams = request.nextUrl.searchParams;
    const taskId = searchParams.get('taskId');

    if (!taskId) {
      return handleAPIError(new ValidationError('Task ID required'));
    }

    const taskService = new TaskService();
    await taskService.initialize();

    // Cancel the erasure task if it's still pending
    const cancelResponse = await taskService.cancelTask(taskId, user.id);

    if (!cancelResponse.success) {
      return NextResponse.json({
        success: false,
        error: 'Failed to cancel erasure request. It may already be processed or not exist.'
      }, { status: 400 });
    }

    logger.info('User cancelled erasure request', {
      userId: user.id,
      taskId
    });

    return NextResponse.json({
      success: true,
      message: 'Erasure request cancelled successfully'
    });

  } catch (error) {
    logger.error('Error cancelling erasure request:', error);
    return handleAPIError(error);
  }
}