// Common UI component exports to reduce import duplication
// This file helps with tree-shaking and reduces bundle size

export { But<PERSON> } from '@/components/ui/button'
export { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
export { Badge } from '@/components/ui/badge'
export { 
  Ta<PERSON>, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs'
export { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
export { Input } from '@/components/ui/input'
export { Label } from '@/components/ui/label'
export { Textarea } from '@/components/ui/textarea'
export { ScrollArea } from '@/components/ui/scroll-area'
export { Separator } from '@/components/ui/separator'
export { useToast } from '@/hooks/use-toast'

// Re-export commonly used icons
export {
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  ChevronUp,
  Plus,
  Minus,
  X,
  Check,
  Search,
  Menu,
  Settings,
  User,
  Loader2,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react'
