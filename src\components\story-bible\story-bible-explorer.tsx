'use client'

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import { Search } from 'lucide-react'
import { Users } from 'lucide-react'
import { Map } from 'lucide-react'
import { Clock } from 'lucide-react'
import { Lightbulb } from 'lucide-react'
import { Book } from 'lucide-react'
import { Settings } from 'lucide-react'
import { Filter } from 'lucide-react'
import { Eye } from 'lucide-react'
import { EyeOff } from 'lucide-react'
import { Star } from 'lucide-react'
import { Calendar } from 'lucide-react'
import { ChevronRight } from 'lucide-react'
import { ChevronDown } from 'lucide-react'
import { Plus } from 'lucide-react'
import { Edit } from 'lucide-react'
import { Trash2 } from 'lucide-react'
import { Globe } from 'lucide-react'
import { MapPin } from 'lucide-react'
import { Crown } from 'lucide-react'
import { Shield } from 'lucide-react'
import { Heart } from 'lucide-react'
import { Zap } from 'lucide-react'
import { User } from 'lucide-react'
import { ComponentErrorBoundary } from '@/components/error/unified-error-boundary'

interface Character {
  id: string
  name: string
  role: string
  description: string
  backstory: string
  personality: Record<string, unknown>
  relationships: Array<Record<string, unknown>>
  created_at: string
  updated_at: string
}

interface TimelineEvent {
  id: string
  event: string
  chapter: number
  importance: 'high' | 'medium' | 'low'
  characters_involved?: string[]
}

interface PlotThread {
  id: string
  description: string
  status: 'active' | 'resolved' | 'paused'
  priority: 'high' | 'medium' | 'low'
  related_characters?: string[]
}

interface WorldRule {
  key: string
  value: string
  category: 'magic' | 'technology' | 'society' | 'geography' | 'other'
}

interface StoryBibleData {
  characters: Character[]
  worldRules: Record<string, string>
  timeline: TimelineEvent[]
  plotThreads: PlotThread[]
}

interface StoryBibleExplorerProps {
  projectId: string
  onEdit?: (type: string, item: any) => void
  onAdd?: (type: string) => void
  readOnly?: boolean
}

const CHARACTER_ROLE_ICONS = {
  protagonist: Crown,
  antagonist: Shield,
  supporting: Users,
  minor: User,
  love_interest: Heart
}

const PLOT_STATUS_COLORS = {
  active: 'bg-green-100 text-green-800 border-green-200',
  resolved: 'bg-blue-100 text-blue-800 border-blue-200',
  paused: 'bg-yellow-100 text-yellow-800 border-yellow-200'
}

const PRIORITY_COLORS = {
  high: 'bg-red-100 text-red-800 border-red-200',
  medium: 'bg-orange-100 text-orange-800 border-orange-200',
  low: 'bg-gray-100 text-gray-800 border-gray-200'
}

function StoryBibleExplorerComponent({ 
  projectId, 
  onEdit, 
  onAdd, 
  readOnly = false 
}: StoryBibleExplorerProps) {
  const [storyBible, setStoryBible] = useState<StoryBibleData>({
    characters: [],
    worldRules: {},
    timeline: [],
    plotThreads: []
  })
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [activeCategory, setActiveCategory] = useState<'all' | 'characters' | 'world' | 'timeline' | 'plot'>('all')
  const [showFavorites, setShowFavorites] = useState(false)
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['characters', 'world', 'timeline', 'plot']))
  const { toast } = useToast()

  useEffect(() => {
    loadStoryBible()
  }, [projectId])

  const loadStoryBible = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/story-bible?projectId=${projectId}`)
      if (!response.ok) {
        throw new Error('Failed to load story bible')
      }
      
      const data = await response.json()
      setStoryBible(data)
    } catch (error) {
      logger.error('Error loading story bible:', error)
      toast({
        title: 'Error',
        description: 'Failed to load story bible',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredContent = useMemo(() => {
    const query = searchQuery.toLowerCase()
    
    const filteredCharacters = storyBible.characters.filter(char =>
      char.name.toLowerCase().includes(query) ||
      char.role.toLowerCase().includes(query) ||
      char.description.toLowerCase().includes(query)
    )

    const filteredWorldRules = Object.entries(storyBible.worldRules).filter(([key, value]) =>
      key.toLowerCase().includes(query) ||
      value.toLowerCase().includes(query)
    )

    const filteredTimeline = storyBible.timeline.filter(event =>
      event.event.toLowerCase().includes(query) ||
      event.chapter.toString().includes(query)
    )

    const filteredPlotThreads = storyBible.plotThreads.filter(thread =>
      thread.description.toLowerCase().includes(query) ||
      thread.status.toLowerCase().includes(query)
    )

    return {
      characters: filteredCharacters,
      worldRules: Object.fromEntries(filteredWorldRules),
      timeline: filteredTimeline,
      plotThreads: filteredPlotThreads
    }
  }, [storyBible, searchQuery])

  const toggleSection = (section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(section)) {
        newSet.delete(section)
      } else {
        newSet.add(section)
      }
      return newSet
    })
  }

  const getCharacterIcon = (role: string) => {
    const IconComponent = CHARACTER_ROLE_ICONS[role as keyof typeof CHARACTER_ROLE_ICONS] || Users
    return IconComponent
  }

  const getStats = () => {
    return {
      characters: storyBible.characters.length,
      worldRules: Object.keys(storyBible.worldRules).length,
      timelineEvents: storyBible.timeline.length,
      plotThreads: storyBible.plotThreads.length,
      activePlots: storyBible.plotThreads.filter(p => p.status === 'active').length
    }
  }

  const stats = getStats()

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Book className="h-12 w-12 text-muted-foreground mx-auto mb-4 animate-pulse" />
          <p className="text-muted-foreground">Loading story bible...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Book className="h-6 w-6" />
            Story Bible Explorer
          </h2>
          <p className="text-muted-foreground">
            Comprehensive overview of your story elements
          </p>
        </div>
        {!readOnly && (
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => onAdd?.('quick')}>
              <Plus className="w-4 h-4 mr-2" />
              Quick Add
            </Button>
            <Button onClick={() => onEdit?.('bible', null)}>
              <Edit className="w-4 h-4 mr-2" />
              Edit Bible
            </Button>
          </div>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{stats.characters}</p>
                <p className="text-xs text-muted-foreground">Characters</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Map className="w-4 h-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{stats.worldRules}</p>
                <p className="text-xs text-muted-foreground">World Rules</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{stats.timelineEvents}</p>
                <p className="text-xs text-muted-foreground">Timeline Events</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Lightbulb className="w-4 h-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{stats.plotThreads}</p>
                <p className="text-xs text-muted-foreground">Plot Threads</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Zap className="w-4 h-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{stats.activePlots}</p>
                <p className="text-xs text-muted-foreground">Active Plots</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search characters, events, rules..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <Tabs value={activeCategory} onValueChange={(value) => setActiveCategory(value as any)}>
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="characters">Characters</TabsTrigger>
              <TabsTrigger value="world">World</TabsTrigger>
              <TabsTrigger value="timeline">Timeline</TabsTrigger>
              <TabsTrigger value="plot">Plot</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <Button
            variant={showFavorites ? "default" : "outline"}
            size="sm"
            onClick={() => setShowFavorites(!showFavorites)}
          >
            {showFavorites ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
            Favorites
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Characters Section */}
          {(activeCategory === 'all' || activeCategory === 'characters') && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle 
                    className="flex items-center gap-2 cursor-pointer"
                    onClick={() => toggleSection('characters')}
                  >
                    {expandedSections.has('characters') ? 
                      <ChevronDown className="h-4 w-4" /> : 
                      <ChevronRight className="h-4 w-4" />
                    }
                    <Users className="h-5 w-5" />
                    Characters ({filteredContent.characters.length})
                  </CardTitle>
                  {!readOnly && (
                    <Button size="sm" onClick={() => onAdd?.('character')}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardHeader>
              {expandedSections.has('characters') && (
                <CardContent>
                  <div className="grid gap-3">
                    {filteredContent.characters.length === 0 ? (
                      <p className="text-center text-muted-foreground py-4">
                        No characters found
                      </p>
                    ) : (
                      filteredContent.characters.map((character) => {
                        const IconComponent = getCharacterIcon(character.role)
                        return (
                          <Card key={character.id} className="p-3 hover:shadow-sm transition-shadow">
                            <div className="flex items-start justify-between">
                              <div className="flex items-start gap-3 flex-1">
                                <div className="p-2 bg-muted rounded-lg">
                                  <IconComponent className="h-4 w-4" />
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-1">
                                    <h4 className="font-medium">{character.name}</h4>
                                    <Badge variant="outline" className="capitalize">
                                      {character.role}
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-muted-foreground line-clamp-2">
                                    {character.description}
                                  </p>
                                  {character.personality?.traits && Array.isArray(character.personality.traits) && (
                                    <div className="flex flex-wrap gap-1 mt-2">
                                      {(character.personality.traits as string[]).slice(0, 3).map((trait, index) => (
                                        <Badge key={index} variant="secondary" className="text-xs">
                                          {trait}
                                        </Badge>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              </div>
                              {!readOnly && (
                                <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                  <Button 
                                    size="sm" 
                                    variant="ghost"
                                    onClick={() => onEdit?.('character', character)}
                                  >
                                    <Edit className="h-3 w-3" />
                                  </Button>
                                </div>
                              )}
                            </div>
                          </Card>
                        )
                      })
                    )}
                  </div>
                </CardContent>
              )}
            </Card>
          )}

          {/* World Rules Section */}
          {(activeCategory === 'all' || activeCategory === 'world') && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle 
                    className="flex items-center gap-2 cursor-pointer"
                    onClick={() => toggleSection('world')}
                  >
                    {expandedSections.has('world') ? 
                      <ChevronDown className="h-4 w-4" /> : 
                      <ChevronRight className="h-4 w-4" />
                    }
                    <Map className="h-5 w-5" />
                    World Rules ({Object.keys(filteredContent.worldRules).length})
                  </CardTitle>
                  {!readOnly && (
                    <Button size="sm" onClick={() => onAdd?.('world')}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardHeader>
              {expandedSections.has('world') && (
                <CardContent>
                  <div className="grid gap-3">
                    {Object.keys(filteredContent.worldRules).length === 0 ? (
                      <p className="text-center text-muted-foreground py-4">
                        No world rules found
                      </p>
                    ) : (
                      Object.entries(filteredContent.worldRules).map(([key, value]) => (
                        <Card key={key} className="p-3 hover:shadow-sm transition-shadow">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3 flex-1">
                              <div className="p-2 bg-muted rounded-lg">
                                <Globe className="h-4 w-4" />
                              </div>
                              <div className="flex-1">
                                <h4 className="font-medium capitalize mb-1">
                                  {key.replace(/_/g, ' ')}
                                </h4>
                                <p className="text-sm text-muted-foreground">
                                  {value}
                                </p>
                              </div>
                            </div>
                            {!readOnly && (
                              <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button 
                                  size="sm" 
                                  variant="ghost"
                                  onClick={() => onEdit?.('world', { key, value })}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                              </div>
                            )}
                          </div>
                        </Card>
                      ))
                    )}
                  </div>
                </CardContent>
              )}
            </Card>
          )}

          {/* Timeline Section */}
          {(activeCategory === 'all' || activeCategory === 'timeline') && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle 
                    className="flex items-center gap-2 cursor-pointer"
                    onClick={() => toggleSection('timeline')}
                  >
                    {expandedSections.has('timeline') ? 
                      <ChevronDown className="h-4 w-4" /> : 
                      <ChevronRight className="h-4 w-4" />
                    }
                    <Clock className="h-5 w-5" />
                    Timeline ({filteredContent.timeline.length})
                  </CardTitle>
                  {!readOnly && (
                    <Button size="sm" onClick={() => onAdd?.('timeline')}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardHeader>
              {expandedSections.has('timeline') && (
                <CardContent>
                  <div className="space-y-3">
                    {filteredContent.timeline.length === 0 ? (
                      <p className="text-center text-muted-foreground py-4">
                        No timeline events found
                      </p>
                    ) : (
                      filteredContent.timeline
                        .sort((a, b) => a.chapter - b.chapter)
                        .map((event, index) => (
                          <Card key={event.id || index} className="p-3 hover:shadow-sm transition-shadow">
                            <div className="flex items-start justify-between">
                              <div className="flex items-start gap-3 flex-1">
                                <Badge variant="outline" className="text-xs mt-0.5">
                                  Ch. {event.chapter}
                                </Badge>
                                <div className="flex-1">
                                  <p className="text-sm font-medium mb-1">{event.event}</p>
                                  {event.importance && (
                                    <Badge 
                                      variant="secondary" 
                                      className={`text-xs ${PRIORITY_COLORS[event.importance]}`}
                                    >
                                      {event.importance} priority
                                    </Badge>
                                  )}
                                </div>
                              </div>
                              {!readOnly && (
                                <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                  <Button 
                                    size="sm" 
                                    variant="ghost"
                                    onClick={() => onEdit?.('timeline', event)}
                                  >
                                    <Edit className="h-3 w-3" />
                                  </Button>
                                </div>
                              )}
                            </div>
                          </Card>
                        ))
                    )}
                  </div>
                </CardContent>
              )}
            </Card>
          )}

          {/* Plot Threads Section */}
          {(activeCategory === 'all' || activeCategory === 'plot') && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle 
                    className="flex items-center gap-2 cursor-pointer"
                    onClick={() => toggleSection('plot')}
                  >
                    {expandedSections.has('plot') ? 
                      <ChevronDown className="h-4 w-4" /> : 
                      <ChevronRight className="h-4 w-4" />
                    }
                    <Lightbulb className="h-5 w-5" />
                    Plot Threads ({filteredContent.plotThreads.length})
                  </CardTitle>
                  {!readOnly && (
                    <Button size="sm" onClick={() => onAdd?.('plot')}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardHeader>
              {expandedSections.has('plot') && (
                <CardContent>
                  <div className="grid gap-3">
                    {filteredContent.plotThreads.length === 0 ? (
                      <p className="text-center text-muted-foreground py-4">
                        No plot threads found
                      </p>
                    ) : (
                      filteredContent.plotThreads.map((thread) => (
                        <Card key={thread.id} className="p-3 hover:shadow-sm transition-shadow">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3 flex-1">
                              <div className="flex flex-col gap-1">
                                <Badge 
                                  variant="secondary" 
                                  className={`text-xs ${PLOT_STATUS_COLORS[thread.status]}`}
                                >
                                  {thread.status}
                                </Badge>
                                {thread.priority && (
                                  <Badge 
                                    variant="outline" 
                                    className={`text-xs ${PRIORITY_COLORS[thread.priority]}`}
                                  >
                                    {thread.priority}
                                  </Badge>
                                )}
                              </div>
                              <div className="flex-1">
                                <p className="text-sm font-medium">{thread.description}</p>
                              </div>
                            </div>
                            {!readOnly && (
                              <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button 
                                  size="sm" 
                                  variant="ghost"
                                  onClick={() => onEdit?.('plot', thread)}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                              </div>
                            )}
                          </div>
                        </Card>
                      ))
                    )}
                  </div>
                </CardContent>
              )}
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {!readOnly && (
                <>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => onAdd?.('character')}
                  >
                    <Users className="w-4 h-4 mr-2" />
                    Add Character
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => onAdd?.('world')}
                  >
                    <Map className="w-4 h-4 mr-2" />
                    Add World Rule
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => onAdd?.('timeline')}
                  >
                    <Clock className="w-4 h-4 mr-2" />
                    Add Timeline Event
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => onAdd?.('plot')}
                  >
                    <Lightbulb className="w-4 h-4 mr-2" />
                    Add Plot Thread
                  </Button>
                  <Separator />
                </>
              )}
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => window.print()}
              >
                <Book className="w-4 h-4 mr-2" />
                Export Bible
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={loadStoryBible}
              >
                <Settings className="w-4 h-4 mr-2" />
                Refresh Data
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

// Export with error boundary
export const StoryBibleExplorer = (props: StoryBibleExplorerProps) => (
  <ComponentErrorBoundary componentName="StoryBibleExplorer" isolate>
    <StoryBibleExplorerComponent {...props} />
  </ComponentErrorBoundary>
)