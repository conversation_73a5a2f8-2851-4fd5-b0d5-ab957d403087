import { NextRequest, NextResponse } from 'next/server'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'

import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
const consentSchema = z.object({
  consent: z.object({
    necessary: z.boolean(),
    analytics: z.boolean(),
    marketing: z.boolean(),
    functional: z.boolean()
  })
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = consentSchema.parse(body)
    
    // Try to get the user if logged in
    const supabase = await createTypedServerClient()
    const user = await UnifiedAuthService.authenticateUser(request);
    if (!user) {
      return handleAPIError(new AuthenticationError());
    }
    
    if (user) {
      // Store consent in database for logged-in users
      const consentRecords = Object.entries(validatedData.consent).map(([type, granted]) => ({
        user_id: user.id,
        consent_type: `cookie_${type}`,
        granted,
        granted_at: new Date().toISOString(),
        ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined
      }))

      // Insert consent records
      const { error } = await supabase
        .from('user_consent_history')
        .insert(consentRecords)

      if (error) {
        console.error('Error storing consent history:', error)
      }

      // Update privacy settings based on consent
      const privacyUpdates: Partial<{ analytics_enabled: boolean; personalization_enabled: boolean; email_notifications_enabled: boolean }> = {}
      if (validatedData.consent.analytics !== undefined) {
        privacyUpdates.analytics_enabled = validatedData.consent.analytics
      }
      if (validatedData.consent.marketing !== undefined) {
        privacyUpdates.marketing_emails = validatedData.consent.marketing
      }

      if (Object.keys(privacyUpdates).length > 0) {
        await supabase
          .from('user_privacy_settings')
          .upsert({
            user_id: user.id,
            ...privacyUpdates,
            updated_at: new Date().toISOString()
          })
      }
    }

    // Return success (cookie consent is stored client-side regardless)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error processing cookie consent:', error)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid consent data', details: error.errors },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to process consent' },
      { status: 500 }
    )
  }
}