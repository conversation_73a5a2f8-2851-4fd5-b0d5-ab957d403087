'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { UnifiedErrorBoundary } from '@/components/error/unified-error-boundary'
import { Download } from 'lucide-react'
import { Clock } from 'lucide-react'
import { CheckCircle } from 'lucide-react'
import { XCircle } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { RefreshCw } from 'lucide-react'
import { FileText } from 'lucide-react'
import { Loader2 } from 'lucide-react'
import { Eye } from 'lucide-react'
import { Trash2 } from 'lucide-react'
import { Pause } from 'lucide-react'
import { Play } from 'lucide-react'
import { cn } from '@/lib/utils'
import { createClient } from '@/lib/supabase'
import { formatDistanceToNow } from 'date-fns'
import { useToast } from '@/components/ui/use-toast'

interface ExportJob {
  id: string
  projectId: string
  projectTitle: string
  format: string
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: number
  currentStep?: string
  fileUrl?: string
  fileSize?: number
  error?: string
  createdAt: string
  updatedAt: string
  estimatedTime?: number
  processingTime?: number
}

interface ExportStatusProps {
  projectId?: string
  userId: string
  showAll?: boolean
  compact?: boolean
}

const statusConfig = {
  pending: {
    icon: Clock,
    color: 'text-orange-600 dark:text-orange-400',
    bgColor: 'bg-orange-100 dark:bg-orange-900/20',
    label: 'Pending'
  },
  processing: {
    icon: Loader2,
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-100 dark:bg-blue-900/20',
    label: 'Processing'
  },
  completed: {
    icon: CheckCircle,
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-100 dark:bg-green-900/20',
    label: 'Completed'
  },
  failed: {
    icon: XCircle,
    color: 'text-red-600 dark:text-red-400',
    bgColor: 'bg-red-100 dark:bg-red-900/20',
    label: 'Failed'
  },
  cancelled: {
    icon: XCircle,
    color: 'text-gray-600 dark:text-gray-400',
    bgColor: 'bg-gray-100 dark:bg-gray-900/20',
    label: 'Cancelled'
  }
}

const formatFileSize = (bytes: number) => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 Bytes'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

export function ExportStatus({ projectId, userId, showAll = false, compact = false }: ExportStatusProps) {
  const [jobs, setJobs] = useState<ExportJob[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()
  const supabase = getBrowserClient() //)

  // Fetch jobs
  useEffect(() => {
    const fetchJobs = async () => {
      try {
        let query = supabase
          .from('export_jobs')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(compact ? 5 : 20)

        if (projectId && !showAll) {
          query = query.eq('project_id', projectId)
        }

        const { data, error } = await query

        if (error) throw error

        const formattedJobs: ExportJob[] = data?.map(job => ({
          id: job.id,
          projectId: job.project_id,
          projectTitle: job.project_title,
          format: job.format,
          status: job.status,
          progress: job.progress || 0,
          currentStep: job.current_step,
          fileUrl: job.file_url,
          fileSize: job.file_size,
          error: job.error,
          createdAt: job.created_at,
          updatedAt: job.updated_at,
          estimatedTime: job.estimated_time,
          processingTime: job.processing_time
        })) || []

        setJobs(formattedJobs)
      } catch (error) {
        console.error('Failed to fetch export jobs:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchJobs()

    // Subscribe to real-time updates
    const subscription = supabase
      .channel('export_jobs')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'export_jobs',
          filter: projectId ? `project_id=eq.${projectId}` : `user_id=eq.${userId}`
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            const newJob: ExportJob = {
              id: payload.new.id,
              projectId: payload.new.project_id,
              projectTitle: payload.new.project_title,
              format: payload.new.format,
              status: payload.new.status,
              progress: payload.new.progress || 0,
              currentStep: payload.new.current_step,
              fileUrl: payload.new.file_url,
              fileSize: payload.new.file_size,
              error: payload.new.error,
              createdAt: payload.new.created_at,
              updatedAt: payload.new.updated_at,
              estimatedTime: payload.new.estimated_time,
              processingTime: payload.new.processing_time
            }
            setJobs(prev => [newJob, ...prev].slice(0, compact ? 5 : 20))
          } else if (payload.eventType === 'UPDATE') {
            setJobs(prev => prev.map(job => 
              job.id === payload.new.id 
                ? {
                    ...job,
                    status: payload.new.status,
                    progress: payload.new.progress || job.progress,
                    currentStep: payload.new.current_step,
                    fileUrl: payload.new.file_url,
                    fileSize: payload.new.file_size,
                    error: payload.new.error,
                    updatedAt: payload.new.updated_at,
                    processingTime: payload.new.processing_time
                  }
                : job
            ))
          } else if (payload.eventType === 'DELETE') {
            setJobs(prev => prev.filter(job => job.id !== payload.old.id))
          }
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [projectId, userId, showAll, compact])

  const handleCancel = async (jobId: string) => {
    try {
      const response = await fetch(`/api/export/jobs/${jobId}/cancel`, {
        method: 'POST'
      })

      if (!response.ok) throw new Error('Failed to cancel job')

      toast({
        title: 'Export cancelled',
        description: 'The export job has been cancelled.'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to cancel export job.',
        variant: 'destructive'
      })
    }
  }

  const handleRetry = async (job: ExportJob) => {
    try {
      const response = await fetch(`/api/projects/${job.projectId}/export`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ format: job.format })
      })

      if (!response.ok) throw new Error('Failed to retry export')

      toast({
        title: 'Export restarted',
        description: 'Your export job has been queued.'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to restart export.',
        variant: 'destructive'
      })
    }
  }

  const handleDelete = async (jobId: string) => {
    try {
      const response = await fetch(`/api/export/jobs/${jobId}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('Failed to delete job')

      setJobs(prev => prev.filter(job => job.id !== jobId))
      
      toast({
        title: 'Job deleted',
        description: 'The export job has been removed.'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete export job.',
        variant: 'destructive'
      })
    }
  }

  const activeJobs = jobs.filter(job => ['pending', 'processing'].includes(job.status))
  const completedJobs = jobs.filter(job => job.status === 'completed')
  const failedJobs = jobs.filter(job => job.status === 'failed')

  if (isLoading) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (compact && jobs.length === 0) {
    return null
  }

  const renderJob = (job: ExportJob) => {
    const config = statusConfig[job.status]
    const Icon = config.icon

    return (
      <div
        key={job.id}
        className={cn(
          "space-y-3 p-4 rounded-lg border",
          job.status === 'processing' && "border-blue-200 dark:border-blue-800"
        )}
      >
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <div className={cn("p-2 rounded-lg", config.bgColor)}>
              <Icon className={cn("h-4 w-4", config.color, job.status === 'processing' && 'animate-spin')} />
            </div>
            <div>
              <h4 className="font-medium">{job.projectTitle}</h4>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline" className="text-xs">
                  {job.format.toUpperCase()}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {formatDistanceToNow(new Date(job.createdAt), { addSuffix: true })}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            {job.status === 'processing' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleCancel(job.id)}
                title="Cancel export"
              >
                <Pause className="h-3 w-3" />
              </Button>
            )}
            {job.status === 'completed' && job.fileUrl && (
              <Button
                variant="ghost"
                size="sm"
                asChild
              >
                <a href={job.fileUrl} download title="Download file">
                  <Download className="h-3 w-3" />
                </a>
              </Button>
            )}
            {job.status === 'failed' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleRetry(job)}
                title="Retry export"
              >
                <RefreshCw className="h-3 w-3" />
              </Button>
            )}
            {['completed', 'failed', 'cancelled'].includes(job.status) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDelete(job.id)}
                title="Delete job"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        {job.status === 'processing' && (
          <div className="space-y-2">
            <Progress value={job.progress} className="h-2" />
            <div className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground">
                {job.currentStep || 'Processing...'}
              </span>
              <span className="font-medium">{job.progress}%</span>
            </div>
          </div>
        )}

        {job.status === 'completed' && job.fileSize && (
          <div className="text-xs text-muted-foreground">
            File size: {formatFileSize(job.fileSize)}
            {job.processingTime && ` • Processed in ${job.processingTime}s`}
          </div>
        )}

        {job.status === 'failed' && job.error && (
          <Alert variant="destructive" className="py-2">
            <AlertCircle className="h-3 w-3" />
            <AlertDescription className="text-xs">
              {job.error}
            </AlertDescription>
          </Alert>
        )}
      </div>
    )
  }

  return (
    <UnifiedErrorBoundary>
      <Card className={compact ? '' : 'h-full'}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Export Status
              </CardTitle>
              <CardDescription>
                {activeJobs.length > 0
                  ? `${activeJobs.length} active export${activeJobs.length === 1 ? '' : 's'}`
                  : 'No active exports'}
              </CardDescription>
            </div>
            {!compact && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {jobs.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">No export jobs yet</p>
              <p className="text-sm text-muted-foreground mt-1">
                Export your project to see the status here
              </p>
            </div>
          ) : (
            <ScrollArea className={compact ? 'h-[300px]' : 'h-[500px]'}>
              <div className="space-y-3 pr-4">
                {jobs.map(job => renderJob(job))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
    </UnifiedErrorBoundary>
  )
}