#!/usr/bin/env node
import { promises as fs } from 'fs';
import path from 'path';
import { glob } from 'glob';

interface DirectImportInfo {
  file: string;
  imports: string[];
  supabaseUsage: string[];
  suggestedService?: string;
}

async function findDirectSupabaseImports(): Promise<DirectImportInfo[]> {
  const apiDir = path.join(process.cwd(), 'src', 'app', 'api');
  const files = await glob('**/*.{ts,tsx}', { cwd: apiDir });
  
  const results: DirectImportInfo[] = [];
  
  for (const file of files) {
    const filePath = path.join(apiDir, file);
    const content = await fs.readFile(filePath, 'utf-8');
    
    // Check for direct Supabase imports
    const supabaseImportRegex = /import\s+{[^}]+}\s+from\s+['"]@\/lib\/supabase['"]/g;
    const imports = content.match(supabaseImportRegex) || [];
    
    if (imports.length > 0) {
      // Find Supabase usage patterns
      const supabaseUsage: string[] = [];
      
      // Common patterns
      const patterns = [
        /supabase\s*\.\s*from\s*\(['"](\w+)['"]\)/g,
        /supabase\s*\.\s*rpc\s*\(/g,
        /supabase\s*\.\s*storage\s*\./g,
        /createTypedServerClient\s*\(\)/g,
        /createServerClient\s*\(/g,
      ];
      
      patterns.forEach(pattern => {
        const matches = content.match(pattern);
        if (matches) {
          supabaseUsage.push(...matches);
        }
      });
      
      // Suggest appropriate service based on route path and usage
      const suggestedService = suggestService(file, supabaseUsage);
      
      results.push({
        file: filePath,
        imports,
        supabaseUsage,
        suggestedService
      });
    }
  }
  
  return results;
}

function suggestService(filePath: string, usage: string[]): string {
  // Based on route path
  if (filePath.includes('/projects/')) return 'ProjectService';
  if (filePath.includes('/chapters/')) return 'ChapterService';
  if (filePath.includes('/characters/')) return 'CharacterService';
  if (filePath.includes('/analytics/')) return 'WritingAnalyticsService';
  if (filePath.includes('/series/')) return 'SeriesService';
  if (filePath.includes('/collaboration/')) return 'CollaborationService';
  if (filePath.includes('/user/')) return 'UserService';
  if (filePath.includes('/billing/')) return 'BillingService';
  if (filePath.includes('/story-bible/')) return 'StoryBibleService';
  
  // Based on table usage
  const tableUsage = usage.join(' ');
  if (tableUsage.includes('writing_sessions')) return 'WritingAnalyticsService';
  if (tableUsage.includes('user_subscriptions')) return 'BillingService';
  if (tableUsage.includes('profiles')) return 'UserService';
  
  return 'Unknown - needs analysis';
}

async function generateReport(imports: DirectImportInfo[]): Promise<void> {
  console.log('\\n=== Direct Supabase Imports Report ===\\n');
  console.log(`Total files with direct imports: ${imports.length}\\n`);
  
  // Group by suggested service
  const byService = imports.reduce((acc, item) => {
    const service = item.suggestedService || 'Unknown';
    if (!acc[service]) acc[service] = [];
    acc[service].push(item);
    return acc;
  }, {} as Record<string, DirectImportInfo[]>);
  
  // Display by service
  Object.entries(byService).forEach(([service, files]) => {
    console.log(`\\n${service} (${files.length} files):`);
    console.log('='.repeat(50));
    
    files.slice(0, 5).forEach(file => {
      console.log(`\\nFile: ${file.file.replace(process.cwd(), '.')}`);
      console.log('Usage patterns:');
      file.supabaseUsage.slice(0, 3).forEach(usage => {
        console.log(`  - ${usage.trim()}`);
      });
    });
    
    if (files.length > 5) {
      console.log(`\\n... and ${files.length - 5} more files`);
    }
  });
  
  // Generate fix priority
  console.log('\\n\\n=== Fix Priority ===\\n');
  console.log('1. High Priority (Core functionality):');
  ['ProjectService', 'ChapterService', 'CharacterService'].forEach(service => {
    if (byService[service]) {
      console.log(`   - ${service}: ${byService[service].length} files`);
    }
  });
  
  console.log('\\n2. Medium Priority (Supporting features):');
  ['WritingAnalyticsService', 'CollaborationService', 'BillingService'].forEach(service => {
    if (byService[service]) {
      console.log(`   - ${service}: ${byService[service].length} files`);
    }
  });
  
  console.log('\\n3. Low Priority (Admin/Settings):');
  ['UserService', 'SeriesService', 'StoryBibleService'].forEach(service => {
    if (byService[service]) {
      console.log(`   - ${service}: ${byService[service].length} files`);
    }
  });
  
  // Save detailed report
  const reportPath = path.join(process.cwd(), 'direct-supabase-imports-report.json');
  await fs.writeFile(reportPath, JSON.stringify(imports, null, 2));
  console.log(`\\n\\nDetailed report saved to: ${reportPath}`);
}

// Main execution
async function main() {
  try {
    console.log('Analyzing API routes for direct Supabase imports...');
    const imports = await findDirectSupabaseImports();
    await generateReport(imports);
    
    console.log('\\n\\n=== Next Steps ===');
    console.log('1. Review the report to understand the scope');
    console.log('2. Ensure required service methods exist');
    console.log('3. Start refactoring files grouped by service');
    console.log('4. Test each refactored route thoroughly');
    
  } catch (error) {
    console.error('Error analyzing imports:', error);
    process.exit(1);
  }
}

main();