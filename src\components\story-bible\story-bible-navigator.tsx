'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Users } from 'lucide-react'
import { Map } from 'lucide-react'
import { Clock } from 'lucide-react'
import { Lightbulb } from 'lucide-react'
import { Book } from 'lucide-react'
import { Search } from 'lucide-react'
import { Filter } from 'lucide-react'
import { ChevronRight } from 'lucide-react'
import { ChevronDown } from 'lucide-react'
import { Star } from 'lucide-react'
import { BookOpen } from 'lucide-react'
import { FileText } from 'lucide-react'
import { Bookmark } from 'lucide-react'

interface StoryBibleNavigatorProps {
  storyBible: {
    characters: Array<{ id: string; name: string; role: string }>
    worldRules: Record<string, string>
    timeline: Array<{ id?: string; event: string; chapter: number }>
    plotThreads: Array<{ id: string; description: string; status: string }>
  }
  onNavigate?: (section: string, item?: any) => void
  activeSection?: string
  activeItem?: string
}

export function StoryBibleNavigator({ 
  storyBible, 
  onNavigate, 
  activeSection = 'overview',
  activeItem 
}: StoryBibleNavigatorProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['characters', 'world', 'timeline', 'plot'])
  )

  const toggleSection = (section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(section)) {
        newSet.delete(section)
      } else {
        newSet.add(section)
      }
      return newSet
    })
  }

  const handleNavigate = (section: string, item?: any) => {
    onNavigate?.(section, item)
  }

  const getSectionIcon = (section: string) => {
    switch (section) {
      case 'characters': return Users
      case 'world': return Map
      case 'timeline': return Clock
      case 'plot': return Lightbulb
      case 'overview': return Book
      case 'search': return Search
      case 'favorites': return Star
      default: return BookOpen
    }
  }

  const getItemCount = (section: string) => {
    switch (section) {
      case 'characters': return storyBible.characters.length
      case 'world': return Object.keys(storyBible.worldRules).length
      case 'timeline': return storyBible.timeline.length
      case 'plot': return storyBible.plotThreads.length
      default: return 0
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'resolved': return 'bg-blue-100 text-blue-800'
      case 'paused': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const sections = [
    { id: 'overview', label: 'Overview', icon: Book },
    { id: 'characters', label: 'Characters', icon: Users, expandable: true },
    { id: 'world', label: 'World Rules', icon: Map, expandable: true },
    { id: 'timeline', label: 'Timeline', icon: Clock, expandable: true },
    { id: 'plot', label: 'Plot Threads', icon: Lightbulb, expandable: true },
    { id: 'search', label: 'Search', icon: Search },
    { id: 'favorites', label: 'Favorites', icon: Star }
  ]

  return (
    <Card className="h-fit">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <BookOpen className="h-5 w-5" />
          Story Bible
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[600px]">
          <div className="p-4 space-y-2">
            {sections.map((section) => {
              const IconComponent = section.icon
              const isExpanded = expandedSections.has(section.id)
              const isActive = activeSection === section.id
              const itemCount = getItemCount(section.id)

              return (
                <div key={section.id}>
                  <Button
                    variant={isActive ? "secondary" : "ghost"}
                    className="w-full justify-start h-auto p-2"
                    onClick={() => {
                      if (section.expandable) {
                        toggleSection(section.id)
                      }
                      handleNavigate(section.id)
                    }}
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        {section.expandable && (
                          <div className="w-4 h-4 flex items-center justify-center">
                            {isExpanded ? (
                              <ChevronDown className="h-3 w-3" />
                            ) : (
                              <ChevronRight className="h-3 w-3" />
                            )}
                          </div>
                        )}
                        <IconComponent className="h-4 w-4" />
                        <span className="text-sm">{section.label}</span>
                      </div>
                      {itemCount > 0 && (
                        <Badge variant="outline" className="text-xs">
                          {itemCount}
                        </Badge>
                      )}
                    </div>
                  </Button>

                  {/* Expandable content */}
                  {section.expandable && isExpanded && (
                    <div className="ml-6 mt-1 space-y-1">
                      {section.id === 'characters' && storyBible.characters.map((character) => (
                        <Button
                          key={character.id}
                          variant={activeItem === character.id ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start h-auto p-2"
                          onClick={() => handleNavigate('characters', character)}
                        >
                          <div className="flex items-center justify-between w-full">
                            <span className="text-xs truncate">{character.name}</span>
                            <Badge variant="outline" className="text-xs capitalize">
                              {character.role}
                            </Badge>
                          </div>
                        </Button>
                      ))}

                      {section.id === 'world' && Object.entries(storyBible.worldRules).map(([key, value]) => (
                        <Button
                          key={key}
                          variant={activeItem === key ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start h-auto p-2"
                          onClick={() => handleNavigate('world', { key, value })}
                        >
                          <span className="text-xs truncate capitalize">
                            {key.replace(/_/g, ' ')}
                          </span>
                        </Button>
                      ))}

                      {section.id === 'timeline' && storyBible.timeline
                        .sort((a, b) => a.chapter - b.chapter)
                        .map((event, index) => (
                          <Button
                            key={event.id || index}
                            variant={activeItem === (event.id || index.toString()) ? "secondary" : "ghost"}
                            size="sm"
                            className="w-full justify-start h-auto p-2"
                            onClick={() => handleNavigate('timeline', event)}
                          >
                            <div className="flex items-center justify-between w-full">
                              <span className="text-xs truncate">{event.event}</span>
                              <Badge variant="outline" className="text-xs">
                                Ch. {event.chapter}
                              </Badge>
                            </div>
                          </Button>
                        ))}

                      {section.id === 'plot' && storyBible.plotThreads.map((thread) => (
                        <Button
                          key={thread.id}
                          variant={activeItem === thread.id ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start h-auto p-2"
                          onClick={() => handleNavigate('plot', thread)}
                        >
                          <div className="flex items-center justify-between w-full">
                            <span className="text-xs truncate">{thread.description}</span>
                            <Badge 
                              variant="secondary" 
                              className={`text-xs ${getStatusColor(thread.status)}`}
                            >
                              {thread.status}
                            </Badge>
                          </div>
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              )
            })}

            <Separator className="my-4" />

            {/* Quick Stats */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground px-2">Quick Stats</h3>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="bg-muted/50 rounded p-2 text-center">
                  <div className="font-semibold">{storyBible.characters.length}</div>
                  <div className="text-muted-foreground">Characters</div>
                </div>
                <div className="bg-muted/50 rounded p-2 text-center">
                  <div className="font-semibold">{Object.keys(storyBible.worldRules).length}</div>
                  <div className="text-muted-foreground">Rules</div>
                </div>
                <div className="bg-muted/50 rounded p-2 text-center">
                  <div className="font-semibold">{storyBible.timeline.length}</div>
                  <div className="text-muted-foreground">Events</div>
                </div>
                <div className="bg-muted/50 rounded p-2 text-center">
                  <div className="font-semibold">{storyBible.plotThreads.filter(p => p.status === 'active').length}</div>
                  <div className="text-muted-foreground">Active Plots</div>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground px-2">Recent Updates</h3>
              <div className="space-y-1">
                {storyBible.characters.slice(0, 3).map((character) => (
                  <div key={character.id} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-muted/50">
                    <Users className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground truncate">
                      Updated {character.name}
                    </span>
                  </div>
                ))}
                {storyBible.timeline.slice(-2).map((event, index) => (
                  <div key={event.id || index} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-muted/50">
                    <Clock className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground truncate">
                      Added timeline event
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}