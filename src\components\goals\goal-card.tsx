'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Target } from 'lucide-react'
import { Flame } from 'lucide-react'
import { Calendar } from 'lucide-react'
import { TrendingUp } from 'lucide-react'
import { Edit2 } from 'lucide-react'
import { Trash2 } from 'lucide-react'
import { CheckCircle } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { formatDistanceToNow } from 'date-fns'

interface WritingGoal {
  id: string
  goal_type: 'daily' | 'weekly' | 'monthly' | 'project'
  target_words: number
  start_date: string
  end_date?: string
  is_active: boolean
  current_progress: number
  percentage_complete: number
  days_active: number
  streak_count?: number
  project_id?: string
  project_name?: string
}

interface GoalCardProps {
  goal: WritingGoal
  onEdit?: (goal: Writing<PERSON>oa<PERSON>) => void
  onDelete?: (goalId: string) => void
  onToggle?: (goalId: string, active: boolean) => void
}

export function GoalCard({ goal, onEdit, onDelete, onToggle }: GoalCardProps) {
  const getGoalIcon = () => {
    switch (goal.goal_type) {
      case 'daily':
        return <Target className="h-5 w-5" />
      case 'weekly':
        return <Calendar className="h-5 w-5" />
      case 'monthly':
        return <TrendingUp className="h-5 w-5" />
      case 'project':
        return <CheckCircle className="h-5 w-5" />
      default:
        return <Target className="h-5 w-5" />
    }
  }

  const getGoalTypeLabel = () => {
    switch (goal.goal_type) {
      case 'daily':
        return 'Daily Goal'
      case 'weekly':
        return 'Weekly Goal'
      case 'monthly':
        return 'Monthly Goal'
      case 'project':
        return 'Project Goal'
      default:
        return 'Goal'
    }
  }

  const getProgressColor = () => {
    if (goal.percentage_complete >= 100) return 'text-green-600'
    if (goal.percentage_complete >= 75) return 'text-blue-600'
    if (goal.percentage_complete >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  const isOverdue = goal.end_date && new Date(goal.end_date) < new Date()
  const wordsRemaining = Math.max(0, goal.target_words - goal.current_progress)

  return (
    <Card className={cn(
      "relative",
      !goal.is_active && "opacity-60"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            {getGoalIcon()}
            <CardTitle className="text-base">
              {getGoalTypeLabel()}
              {goal.project_name && (
                <span className="text-sm font-normal text-muted-foreground ml-2">
                  • {goal.project_name}
                </span>
              )}
            </CardTitle>
          </div>
          <div className="flex items-center space-x-1">
            {goal.streak_count && goal.streak_count > 0 && (
              <Badge variant="secondary" className="flex items-center space-x-1">
                <Flame className="h-3 w-3" />
                <span>{goal.streak_count}</span>
              </Badge>
            )}
            {!goal.is_active && (
              <Badge variant="outline">Inactive</Badge>
            )}
            {isOverdue && goal.is_active && (
              <Badge variant="destructive">Overdue</Badge>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Progress Section */}
        <div className="space-y-2">
          <div className="flex justify-between items-baseline">
            <span className="text-2xl font-bold">
              {goal.current_progress.toLocaleString()}
            </span>
            <span className="text-sm text-muted-foreground">
              / {goal.target_words.toLocaleString()} words
            </span>
          </div>
          
          <Progress 
            value={goal.percentage_complete} 
            className="h-2"
          />
          
          <div className="flex justify-between text-sm">
            <span className={cn("font-medium", getProgressColor())}>
              {goal.percentage_complete}% complete
            </span>
            {wordsRemaining > 0 && (
              <span className="text-muted-foreground">
                {wordsRemaining.toLocaleString()} words to go
              </span>
            )}
          </div>
        </div>

        {/* Goal Details */}
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Started</span>
            <span>{formatDistanceToNow(new Date(goal.start_date), { addSuffix: true })}</span>
          </div>
          {goal.end_date && (
            <div className="flex justify-between">
              <span className="text-muted-foreground">Deadline</span>
              <span className={cn(isOverdue && "text-destructive")}>
                {formatDistanceToNow(new Date(goal.end_date), { addSuffix: true })}
              </span>
            </div>
          )}
          <div className="flex justify-between">
            <span className="text-muted-foreground">Active for</span>
            <span>{goal.days_active} days</span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 pt-2">
          {onToggle && (
            <Button
              variant={goal.is_active ? "outline" : "default"}
              size="sm"
              onClick={() => onToggle(goal.id, !goal.is_active)}
              className="flex-1"
            >
              {goal.is_active ? 'Pause' : 'Resume'}
            </Button>
          )}
          {onEdit && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(goal)}
            >
              <Edit2 className="h-4 w-4" />
            </Button>
          )}
          {onDelete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(goal.id)}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}