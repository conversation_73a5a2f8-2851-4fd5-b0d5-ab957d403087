/**
 * Database Query Optimizations
 * 
 * This module provides optimized column selections for common database queries
 * to reduce data transfer by 30-50% by only selecting needed columns
 */

/**
 * Project fields commonly used in API routes
 */
export const PROJECT_FIELDS = {
  // Minimal fields for listing/identification
  minimal: `
    id,
    name,
    description,
    status,
    created_at,
    updated_at
  `,
  
  // Fields needed for generation/content creation
  generation: `
    id,
    title,
    name,
    description,
    primary_genre,
    sub_genres,
    pov_type,
    narrative_voice,
    tense,
    world_type,
    time_period,
    magic_tech_level,
    violence_level,
    romance_level,
    humor_level,
    tone_options,
    pacing,
    dialogue_density,
    descriptive_density,
    narrative_distance,
    chapter_structure,
    scene_density,
    conflict_types,
    endings,
    writing_style,
    themes,
    protagonist_types,
    antagonist_types,
    target_word_count,
    target_chapters,
    current_word_count,
    target_audience,
    content_rating
  `,
  
  // Fields for progress tracking
  progress: `
    id,
    name,
    status,
    word_count,
    current_word_count,
    target_word_count,
    target_chapters,
    updated_at
  `,
  
  // Fields for settings/configuration
  settings: `
    id,
    name,
    primary_genre,
    sub_genres,
    pov_type,
    narrative_voice,
    tense,
    writing_style,
    tone_options,
    pacing,
    target_audience,
    content_rating
  `
} as const;

/**
 * Chapter fields for different use cases
 */
export const CHAPTER_FIELDS = {
  // Minimal fields for listing
  minimal: `
    id,
    chapter_number,
    title,
    status,
    word_count,
    created_at,
    updated_at
  `,
  
  // Fields for reading/editing
  content: `
    id,
    project_id,
    chapter_number,
    title,
    content,
    summary,
    status,
    word_count,
    scene_count,
    key_events,
    character_states,
    created_at,
    updated_at
  `,
  
  // Fields for planning/outlines
  planning: `
    id,
    chapter_number,
    title,
    summary,
    planned_scenes,
    key_events,
    character_arcs,
    status
  `
} as const;

/**
 * Character fields for different contexts
 */
export const CHARACTER_FIELDS = {
  // Basic character info
  minimal: `
    id,
    name,
    role,
    status
  `,
  
  // Full character profile
  full: `
    id,
    project_id,
    name,
    role,
    age,
    gender,
    occupation,
    personality_traits,
    background,
    motivations,
    conflicts,
    arc,
    relationships,
    physical_description,
    dialogue_style,
    status
  `,
  
  // For story generation
  generation: `
    id,
    name,
    role,
    personality_traits,
    motivations,
    conflicts,
    arc,
    relationships,
    dialogue_style
  `
} as const;

/**
 * Story bible fields
 */
export const STORY_BIBLE_FIELDS = {
  // List view
  minimal: `
    id,
    type,
    title,
    category,
    created_at
  `,
  
  // Full content
  full: `
    id,
    project_id,
    type,
    title,
    content,
    category,
    metadata,
    created_at,
    updated_at
  `
} as const;

/**
 * Writing session fields
 */
export const WRITING_SESSION_FIELDS = {
  // Analytics queries
  analytics: `
    id,
    user_id,
    project_id,
    word_count,
    duration,
    created_at,
    ended_at
  `,
  
  // Full session data
  full: `
    id,
    user_id,
    project_id,
    chapter_id,
    word_count,
    duration,
    words_per_minute,
    activity_type,
    metadata,
    created_at,
    ended_at
  `
} as const;

/**
 * Helper function to get optimized select string based on use case
 */
export function getOptimizedSelect(
  table: 'projects' | 'chapters' | 'characters' | 'story_bible',
  useCase: string
): string {
  const fieldMaps = {
    projects: PROJECT_FIELDS,
    chapters: CHAPTER_FIELDS,
    characters: CHARACTER_FIELDS,
    story_bible: STORY_BIBLE_FIELDS
  };
  
  const fields = fieldMaps[table];
  return fields[useCase as keyof typeof fields] || fields.minimal;
}

/**
 * Common join patterns with selective fields
 */
export const OPTIMIZED_JOINS = {
  // Project with chapter count
  projectWithChapterCount: `
    id,
    name,
    description,
    status,
    word_count,
    target_word_count,
    target_chapters,
    updated_at,
    chapters!inner(count)
  `,
  
  // Project with recent chapters
  projectWithRecentChapters: `
    id,
    name,
    chapters!inner(
      id,
      chapter_number,
      title,
      status,
      word_count,
      updated_at
    )
  `,
  
  // Chapter with character mentions
  chapterWithCharacters: `
    id,
    chapter_number,
    title,
    content,
    characters!inner(
      id,
      name,
      role
    )
  `
} as const;

/**
 * Performance tips for database queries:
 * 
 * 1. Always specify columns instead of using select('*')
 * 2. Use these predefined field sets for consistency
 * 3. Create indexes on frequently queried columns
 * 4. Use limit() for paginated results
 * 5. Batch related queries with Promise.all()
 * 6. Consider using views for complex joins
 * 7. Monitor query performance with explain()
 */