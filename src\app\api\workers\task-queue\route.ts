import { NextResponse } from 'next/server'
import { taskQueueService } from '@/lib/services/task-queue-service'
import { logger } from '@/lib/services/logger'

// This route should be called by a cron job or external scheduler
// For production, consider using Vercel Cron Jobs or a similar service

export async function POST(request: Request) {
  try {
    // Verify this is an internal request or from a trusted source
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET

    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Process task queue
    await taskQueueService.processTasks()

    logger.info('Task queue processed successfully')
    return NextResponse.json({ 
      success: true, 
      message: 'Task queue processed' 
    })
  } catch (error) {
    logger.error('Error processing task queue:', error)
    return NextResponse.json(
      { error: 'Failed to process task queue' },
      { status: 500 }
    )
  }
}

// Health check endpoint
export async function GET() {
  try {
    const pendingTasks = await taskQueueService.getTasks({
      status: 'pending' as const,
      limit: 1
    })
    
    const processingTasks = await taskQueueService.getTasks({
      status: 'processing' as const,
      limit: 1
    })
    
    return NextResponse.json({
      status: 'healthy',
      service: 'task-queue-worker',
      stats: {
        hasPendingTasks: pendingTasks.length > 0,
        hasProcessingTasks: processingTasks.length > 0
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json({
      status: 'error',
      service: 'task-queue-worker',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}