import { NextRequest, NextResponse } from 'next/server'
import { TaskService } from '@/lib/services/task-service'
import { logger } from '@/lib/services/logger'

// This endpoint should be called by a cron job (e.g., Vercel Cron or external service)
// Run every 10 minutes: */10 * * * *
export async function GET(request: NextRequest) {
  try {
    // Verify cron secret
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const taskService = new TaskService()
    await taskService.initialize()

    // Get pending tasks ready for processing
    const tasksResponse = await taskService.getPendingTasks(100)
    
    if (!tasksResponse.success) {
      logger.error('Failed to get pending tasks:', tasksResponse.error)
      return NextResponse.json(
        { error: 'Failed to get pending tasks' },
        { status: 500 }
      )
    }

    const tasks = tasksResponse.data
    let processedCount = 0
    let errorCount = 0

    // Process each task
    for (const task of tasks) {
      try {
        await taskService.updateTaskStatus(task.id, 'running')
        
        // Process task based on type
        let result
        switch (task.task_type) {
          case 'send_email':
          case 'bulk_email':
            result = await processEmailTask(task)
            break
            
          case 'export_project':
          case 'export_series':
            result = await processExportTask(task)
            break
            
          case 'user_data_erasure':
            result = await processErasureTask(task)
            break
            
          case 'data_export':
            result = await processDataExportTask(task)
            break
            
          case 'send_goal_reminder':
            result = await processGoalReminderTask(task)
            break
            
          case 'generate_embeddings':
            result = await processEmbeddingsTask(task)
            break
            
          default:
            throw new Error(`Unknown task type: ${task.task_type}`)
        }

        await taskService.updateTaskStatus(task.id, 'completed', {
          success: true,
          result,
          duration: Date.now() - new Date(task.created_at).getTime()
        })
        
        processedCount++
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        await taskService.handleTaskFailure(task.id, errorMessage)
        errorCount++
        
        logger.error(`Task ${task.id} failed:`, error)
      }
    }

    // Clean up old completed/failed tasks periodically
    if (Math.random() < 0.1) { // 10% chance to run cleanup
      const cleanupResponse = await taskService.cleanupOldTasks(30)
      const cleanedCount = cleanupResponse.success ? cleanupResponse.data : 0
      
      logger.info('Background task cleanup performed', {
        cleanedTasks: cleanedCount
      })
    }

    logger.info('Background tasks processed via cron', {
      totalTasks: tasks.length,
      processedCount,
      errorCount,
      timestamp: new Date().toISOString()
    })

    return NextResponse.json({ 
      success: true,
      message: 'Background tasks processed',
      stats: {
        totalTasks: tasks.length,
        processedCount,
        errorCount,
        successRate: tasks.length > 0 ? Math.round((processedCount / tasks.length) * 100) : 0
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    logger.error('Error processing background tasks:', error)
    return NextResponse.json(
      { error: 'Failed to process background tasks' },
      { status: 500 }
    )
  }
}

// Task processors

async function processEmailTask(task: ProcessingTask): Promise<ProcessingResult> {
  // Mock email processing - in a real implementation, this would use the email service
  logger.info(`Processing email task: ${task.id}`)
  
  const payload = task.payload
  
  // Simulate email sending
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  return {
    emailsSent: 1,
    recipient: payload.recipient || 'unknown',
    template: payload.template || 'default'
  }
}

async function processExportTask(task: ProcessingTask): Promise<ProcessingResult> {
  // Mock export processing - in a real implementation, this would generate actual files
  logger.info(`Processing export task: ${task.id}`)
  
  const payload = task.payload
  
  // Simulate file generation
  await new Promise(resolve => setTimeout(resolve, 5000))
  
  return {
    downloadUrl: `https://exports.bookscribe.ai/${task.id}.${payload.format}`,
    fileSize: Math.floor(Math.random() * 1000000) + 10000, // Random file size
    format: payload.format,
    exportType: payload.exportType
  }
}

async function processErasureTask(task: ProcessingTask): Promise<ProcessingResult> {
  // Mock user data erasure - in a real implementation, this would actually delete user data
  logger.info(`Processing erasure task: ${task.id}`)
  
  const payload = task.payload
  
  // In a real implementation, you would:
  // 1. Create a backup of user data for legal retention period
  // 2. Delete user data from all systems
  // 3. Anonymize any data that must be retained
  // 4. Send confirmation to user
  
  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 10000))
  
  return {
    userId: payload.userId,
    deletedAt: new Date().toISOString(),
    backupCreated: true,
    confirmationSent: true
  }
}

async function processDataExportTask(task: ProcessingTask): Promise<ProcessingResult> {
  // Mock data export processing
  logger.info(`Processing data export task: ${task.id}`)
  
  const payload = task.payload
  
  // Simulate data compilation and file generation
  await new Promise(resolve => setTimeout(resolve, 8000))
  
  return {
    downloadUrl: `https://exports.bookscribe.ai/data-${task.id}.${payload.format}`,
    fileSize: Math.floor(Math.random() * 5000000) + 100000, // Random file size
    format: payload.format,
    categories: payload.categories,
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
  }
}

async function processGoalReminderTask(task: ProcessingTask): Promise<ProcessingResult> {
  // Mock goal reminder processing
  logger.info(`Processing goal reminder task: ${task.id}`)
  
  const payload = task.payload
  
  // Simulate sending reminder email/notification
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  return {
    goalId: payload.goalId,
    reminderSent: true,
    reminderType: 'email'
  }
}

async function processEmbeddingsTask(task: ProcessingTask): Promise<ProcessingResult> {
  // Mock embeddings generation
  logger.info(`Processing embeddings task: ${task.id}`)
  
  const payload = task.payload
  
  // Simulate embeddings generation
  await new Promise(resolve => setTimeout(resolve, 3000))
  
  return {
    contentId: payload.contentId,
    embeddingsGenerated: true,
    vectorCount: Math.floor(Math.random() * 100) + 10
  }
}

// Also export POST for manual triggering
export async function POST(request: NextRequest) {
  return GET(request)
}