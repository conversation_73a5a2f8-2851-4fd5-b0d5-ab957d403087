-- Database Index Optimization Script
-- Generated: 2025-08-06T02:13:31.998Z
-- Total recommendations: 4

-- IMPORTANT: Review each index before applying
-- Some indexes may already exist or may not be needed in your specific use case


-- ==========================================
-- Table: projects
-- ==========================================

-- Frequent filtering on id (3 queries)
-- Impact: LOW (used in 3 queries)
CREATE INDEX IF NOT EXISTS idx_projects_id
  ON projects  (id);


-- ==========================================
-- Table: characters
-- ==========================================

-- Frequent project-based filtering
-- Impact: MEDIUM (used in 6 queries)
CREATE INDEX IF NOT EXISTS idx_characters_project_id
  ON characters  (project_id);


-- ==========================================
-- Table: chapters
-- ==========================================

-- Frequent project-based filtering
-- Impact: MEDIUM (used in 5 queries)
CREATE INDEX IF NOT EXISTS idx_chapters_project_id
  ON chapters  (project_id);


-- ==========================================
-- Table: story_bible
-- ==========================================

-- Frequent project-based filtering
-- Impact: LOW (used in 4 queries)
CREATE INDEX IF NOT EXISTS idx_story_bible_project_id
  ON story_bible  (project_id);


-- ==========================================
-- Additional Optimizations
-- ==========================================

-- 1. Partial indexes for status fields
CREATE INDEX IF NOT EXISTS idx_projects_active
  ON projects (user_id, updated_at)
  WHERE status = 'active';

-- 2. Text search indexes
CREATE INDEX IF NOT EXISTS idx_chapters_content_search
  ON chapters USING gin(to_tsvector('english', content));

CREATE INDEX IF NOT EXISTS idx_projects_title_search
  ON projects USING gin(to_tsvector('english', title));

-- 3. JSONB indexes for structured data
CREATE INDEX IF NOT EXISTS idx_projects_settings
  ON projects USING gin(project_settings);

CREATE INDEX IF NOT EXISTS idx_characters_personality
  ON characters USING gin(personality_traits);

-- 4. Foreign key indexes (if not automatically created)
CREATE INDEX IF NOT EXISTS idx_chapters_project_id
  ON chapters (project_id);

CREATE INDEX IF NOT EXISTS idx_characters_project_id
  ON characters (project_id);

-- 5. Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_period
  ON usage_tracking (user_id, period_start, period_end);

CREATE INDEX IF NOT EXISTS idx_writing_sessions_user_project
  ON writing_sessions (user_id, project_id, started_at DESC);

-- ==========================================
-- Query Optimization Tips
-- ==========================================
-- 1. Use EXPLAIN ANALYZE to verify index usage
-- 2. Monitor pg_stat_user_indexes for unused indexes
-- 3. Consider partitioning large tables (e.g., agent_logs)
-- 4. Use connection pooling for better performance
-- 5. Enable pg_stat_statements for query performance monitoring
