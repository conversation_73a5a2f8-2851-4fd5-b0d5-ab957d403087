import { NextResponse } from 'next/server'
import { UnifiedAuthService, type AuthenticatedRequest } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/api/unified-response'
import { logger } from '@/lib/services/logger'
import { z } from 'zod'
import { RequestValidationMiddleware } from '@/lib/api/request-validation-middleware'
import { ServiceManager } from '@/lib/services/service-manager'
import { baseSchemas } from '@/lib/validation/common-schemas'

const searchEventSchema = z.object({
  projectId: baseSchemas.uuid,
  query: z.string().min(1).max(100),
  resultCount: z.number().int().min(0),
  clickedResult: z.object({
    id: z.string(),
    type: z.string(),
    position: z.number().int().min(0)
  }).optional(),
  timestamp: z.string().datetime()
})

export const POST = UnifiedAuthService.withAuth(async (request: AuthenticatedRequest) => {
  // Enhanced request validation
  const validationResult = await RequestValidationMiddleware.validateRequest(request, {
    bodySchema: searchEventSchema,
    rateLimitKey: 'search-tracking',
    rateLimitCost: 1,
    maxBodySize: 10 * 1024, // 10KB
    allowedContentTypes: ['application/json'],
    validateCSRF: true,
    customValidator: async (req) => {
      const user = req.user;
      if (!user) return { valid: false, error: 'Authentication required' };
      
      const body = await req.json();
      const { projectId } = body;
      
      // Verify project access
      const serviceManager = ServiceManager.getInstance();
      const projectService = await serviceManager.getProjectService();
      
      if (!projectService) {
        return { valid: false, error: 'Service temporarily unavailable' };
      }
      
      const accessResponse = await projectService.checkProjectAccess(projectId, user.id);
      if (!accessResponse.success || !accessResponse.data) {
        return { valid: false, error: 'Access denied to this project' };
      }
      
      return { valid: true };
    }
  });

  if (validationResult instanceof NextResponse) {
    return validationResult;
  }

  const { context } = validationResult;
  const user = request.user!;
  const { projectId, query, resultCount, clickedResult, timestamp } = context.body;

  try {
    const serviceManager = ServiceManager.getInstance();
    const analyticsService = await serviceManager.getWritingAnalyticsService();
    
    if (!analyticsService) {
      logger.error('WritingAnalyticsService not available');
      return UnifiedResponse.error('Service temporarily unavailable', 503);
    }

    // Track search event using service
    const response = await analyticsService.trackSearchEvent(user.id, {
      projectId,
      query,
      resultCount,
      clickedResult,
      timestamp
    });

    if (!response.success) {
      logger.error('Failed to track search event:', response.error, {
        userId: user.id,
        projectId,
        clientIP: context.clientIP
      });
      return UnifiedResponse.error(response.error || 'Failed to track search event');
    }

    logger.info('Search event tracked', {
      userId: user.id,
      projectId,
      query: query.substring(0, 20) + '...',
      hasClickedResult: !!clickedResult,
      clientIP: context.clientIP
    });

    return UnifiedResponse.success({
      message: 'Search event tracked successfully',
      eventId: response.data?.id
    });

  } catch (error) {
    logger.error('Search tracking error:', error, {
      userId: user.id,
      projectId,
      clientIP: context.clientIP
    });
    return UnifiedResponse.error('Failed to track search event');
  }
})