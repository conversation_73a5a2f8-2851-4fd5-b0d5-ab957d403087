'use client'

import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import {
  <PERSON><PERSON>,
  DialogContent,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command'
import { FileText } from 'lucide-react'
import { Home } from 'lucide-react'
import { PenTool } from 'lucide-react'
import { Settings } from 'lucide-react'
import { Search } from 'lucide-react'
import { Plus } from 'lucide-react'
import { BookOpen } from 'lucide-react'
import { Brain } from 'lucide-react'
import { Users } from 'lucide-react'
import { BarChart } from 'lucide-react'
import { Feather } from 'lucide-react'
import { Moon } from 'lucide-react'
import { Sun } from 'lucide-react'
import { Laptop } from 'lucide-react'
import { Palette } from 'lucide-react'
import { LogOut } from 'lucide-react'
import { HelpCircle } from 'lucide-react'
import { useTheme } from 'next-themes'
import { useAuth } from '@/contexts/auth-context'

interface CommandItem {
  icon: React.ComponentType<{ className?: string }>
  label: string
  shortcut?: string
  action: () => void
  keywords?: string[]
}

export function CommandPalette() {
  const [open, setOpen] = useState(false)
  const router = useRouter()
  const { theme, setTheme } = useTheme()
  const { user, signOut } = useAuth()

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }

    document.addEventListener('keydown', down)
    return () => document.removeEventListener('keydown', down)
  }, [])

  const runCommand = useCallback((command: () => void) => {
    setOpen(false)
    command()
  }, [])

  const navigationCommands: CommandItem[] = [
    {
      icon: Home,
      label: 'Dashboard',
      action: () => router.push('/dashboard'),
      keywords: ['home', 'overview'],
    },
    {
      icon: FileText,
      label: 'Projects',
      action: () => router.push('/projects'),
      keywords: ['books', 'stories', 'manuscripts'],
    },
    {
      icon: BookOpen,
      label: 'Series',
      action: () => router.push('/series'),
      keywords: ['collections', 'sequences'],
    },
    {
      icon: Users,
      label: 'Characters',
      action: () => router.push('/characters'),
      keywords: ['people', 'cast'],
    },
    {
      icon: BarChart,
      label: 'Analytics',
      action: () => router.push('/analytics'),
      keywords: ['stats', 'progress', 'metrics'],
    },
  ]

  const actionCommands: CommandItem[] = [
    {
      icon: Plus,
      label: 'New Project',
      shortcut: '⌘N',
      action: () => router.push('/projects/new'),
      keywords: ['create', 'start', 'begin'],
    },
    {
      icon: PenTool,
      label: 'Continue Writing',
      shortcut: '⌘W',
      action: () => router.push('/projects'),
      keywords: ['write', 'resume', 'edit'],
    },
    {
      icon: Brain,
      label: 'AI Assistant',
      shortcut: '⌘I',
      action: () => router.push('/projects?tab=ai-assist'),
      keywords: ['help', 'brainstorm', 'generate'],
    },
    {
      icon: Search,
      label: 'Search Projects',
      shortcut: '⌘F',
      action: () => router.push('/search'),
      keywords: ['find', 'lookup', 'browse'],
    },
  ]

  const themeCommands: CommandItem[] = [
    {
      icon: Sun,
      label: 'Light Mode',
      action: () => setTheme('writers-sanctuary-light'),
      keywords: ['bright', 'day'],
    },
    {
      icon: Moon,
      label: 'Dark Mode',
      action: () => setTheme('evening-study-dark'),
      keywords: ['night', 'evening'],
    },
    {
      icon: Laptop,
      label: 'System Theme',
      action: () => setTheme('system'),
      keywords: ['auto', 'automatic'],
    },
    {
      icon: Palette,
      label: 'Customize Theme',
      action: () => router.push('/customization'),
      keywords: ['colors', 'appearance'],
    },
  ]

  const userCommands: CommandItem[] = [
    {
      icon: Settings,
      label: 'Settings',
      shortcut: '⌘,',
      action: () => router.push('/settings'),
      keywords: ['preferences', 'config'],
    },
    {
      icon: HelpCircle,
      label: 'Help & Support',
      shortcut: '⌘?',
      action: () => router.push('/help'),
      keywords: ['documentation', 'support'],
    },
    ...(user
      ? [
          {
            icon: LogOut,
            label: 'Sign Out',
            action: () => signOut(),
            keywords: ['logout', 'exit'],
          },
        ]
      : []),
  ]

  return (
    <CommandDialog open={open} onOpenChange={setOpen}>
      <CommandInput placeholder="Type a command or search..." />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        
        <CommandGroup heading="Navigation">
          {navigationCommands.map((command) => {
            const Icon = command.icon
            return (
              <CommandItem
                key={command.label}
                onSelect={() => runCommand(command.action)}
              >
                <Icon className="mr-2 h-4 w-4" />
                <span>{command.label}</span>
              </CommandItem>
            )
          })}
        </CommandGroup>

        <CommandSeparator />

        <CommandGroup heading="Actions">
          {actionCommands.map((command) => {
            const Icon = command.icon
            return (
              <CommandItem
                key={command.label}
                onSelect={() => runCommand(command.action)}
              >
                <Icon className="mr-2 h-4 w-4" />
                <span>{command.label}</span>
                {command.shortcut && (
                  <span className="ml-auto text-xs text-muted-foreground">
                    {command.shortcut}
                  </span>
                )}
              </CommandItem>
            )
          })}
        </CommandGroup>

        <CommandSeparator />

        <CommandGroup heading="Theme">
          {themeCommands.map((command) => {
            const Icon = command.icon
            return (
              <CommandItem
                key={command.label}
                onSelect={() => runCommand(command.action)}
              >
                <Icon className="mr-2 h-4 w-4" />
                <span>{command.label}</span>
              </CommandItem>
            )
          })}
        </CommandGroup>

        <CommandSeparator />

        <CommandGroup heading="User">
          {userCommands.map((command) => {
            const Icon = command.icon
            return (
              <CommandItem
                key={command.label}
                onSelect={() => runCommand(command.action)}
              >
                <Icon className="mr-2 h-4 w-4" />
                <span>{command.label}</span>
                {command.shortcut && (
                  <span className="ml-auto text-xs text-muted-foreground">
                    {command.shortcut}
                  </span>
                )}
              </CommandItem>
            )
          })}
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  )
}

export function CommandPaletteProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      {children}
      <CommandPalette />
    </>
  )
}