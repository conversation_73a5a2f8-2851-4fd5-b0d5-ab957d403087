'use client'

import React from 'react'

import { useState } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Separator } from '@/components/ui/separator'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { Users } from 'lucide-react'
import { Circle } from 'lucide-react'
import { Crown } from 'lucide-react'
import { Edit3 } from 'lucide-react'
import { Eye } from 'lucide-react'
import { Clock } from 'lucide-react'
import { PenTool } from 'lucide-react'
import { User<PERSON>he<PERSON> } from 'lucide-react'
import { Wifi } from 'lucide-react'
import { WifiOff } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { Moon } from 'lucide-react'
import { Coffee } from 'lucide-react'
import { Zap } from 'lucide-react'
import { usePresence, type PresenceUser } from '@/hooks/use-presence'
import { formatDistanceToNow } from 'date-fns'

interface PresenceIndicatorProps {
  projectId: string
  variant?: 'compact' | 'full' | 'minimal'
  showWritingIndicator?: boolean
  maxAvatars?: number
  className?: string
}

const statusIcons = {
  online: { icon: Circle, color: 'text-green-500', label: 'Online' },
  away: { icon: Moon, color: 'text-yellow-500', label: 'Away' },
  busy: { icon: Coffee, color: 'text-red-500', label: 'Busy' },
  offline: { icon: Circle, color: 'text-gray-400', label: 'Offline' }
}

const roleIcons = {
  owner: { icon: Crown, color: 'text-yellow-600', label: 'Owner' },
  editor: { icon: Edit3, color: 'text-blue-600', label: 'Editor' },
  viewer: { icon: Eye, color: 'text-gray-600', label: 'Viewer' },
  commenter: { icon: UserCheck, color: 'text-purple-600', label: 'Commenter' }
}

const UserAvatar = React.memo(function UserAvatar({ user, showStatus = true, size = 'sm' }: {
  user: PresenceUser
  showStatus?: boolean
  size?: 'xs' | 'sm' | 'md' | 'lg'
}) {
  const StatusIcon = statusIcons[user.status]?.icon || Circle
  const RoleIcon = roleIcons[user.role as keyof typeof roleIcons]?.icon || UserCheck
  
  const sizeClasses = {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  }

  const statusSizeClasses = {
    xs: 'w-2 h-2',
    sm: 'w-2.5 h-2.5',
    md: 'w-3 h-3',
    lg: 'w-3.5 h-3.5'
  }

  return (
    <div className="relative">
      <Avatar className={cn(sizeClasses[size])}>
        <AvatarImage src={user.avatar_url || undefined} alt={user.full_name || user.email} />
        <AvatarFallback className="text-xs">
          {(user.full_name || user.email).substring(0, 2).toUpperCase()}
        </AvatarFallback>
      </Avatar>
      
      {showStatus && (
        <>
          {/* Status indicator */}
          <div className={cn(
            "absolute -bottom-0.5 -right-0.5 rounded-full border-2 border-background",
            statusSizeClasses[size]
          )}>
            <StatusIcon className={cn("w-full h-full", statusIcons[user.status]?.color)} />
          </div>
          
          {/* Writing indicator */}
          {user.is_writing && user.status === 'online' && (
            <motion.div
              className="absolute -top-1 -left-1"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ repeat: Infinity, duration: 1.5 }}
            >
              <PenTool className="w-3 h-3 text-blue-500" />
            </motion.div>
          )}
        </>
      )}
    </div>
  )
}, (prevProps, nextProps) => {
  return (
    prevProps.user.user_id === nextProps.user.user_id &&
    prevProps.user.status === nextProps.user.status &&
    prevProps.user.is_writing === nextProps.user.is_writing &&
    prevProps.user.avatar_url === nextProps.user.avatar_url &&
    prevProps.showStatus === nextProps.showStatus &&
    prevProps.size === nextProps.size
  )
})

const UserTooltipContent = React.memo(function UserTooltipContent({ user }: { user: PresenceUser }) {
  const StatusIcon = statusIcons[user.status]?.icon || Circle
  const RoleIcon = roleIcons[user.role as keyof typeof roleIcons]?.icon || UserCheck
  
  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <UserAvatar user={user} showStatus={false} size="sm" />
        <div>
          <p className="font-medium text-sm">{user.full_name || user.email}</p>
          <p className="text-xs text-muted-foreground">{user.email}</p>
        </div>
      </div>
      
      <Separator />
      
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <StatusIcon className={cn("w-3 h-3", statusIcons[user.status]?.color)} />
          <span className="text-xs">{statusIcons[user.status]?.label}</span>
        </div>
        
        <div className="flex items-center gap-2">
          <RoleIcon className={cn("w-3 h-3", roleIcons[user.role as keyof typeof roleIcons]?.color)} />
          <span className="text-xs capitalize">{user.role}</span>
        </div>
        
        {user.is_writing && user.status === 'online' && (
          <div className="flex items-center gap-2">
            <PenTool className="w-3 h-3 text-blue-500" />
            <span className="text-xs">Currently writing</span>
          </div>
        )}
        
        {user.current_section && (
          <div className="flex items-center gap-2">
            <Zap className="w-3 h-3 text-purple-500" />
            <span className="text-xs">Working on: {user.current_section}</span>
          </div>
        )}
        
        <div className="flex items-center gap-2">
          <Clock className="w-3 h-3 text-gray-500" />
          <span className="text-xs">
            {user.status === 'online' ? 'Active now' : 
             `Last seen ${formatDistanceToNow(new Date(user.last_seen))} ago`}
          </span>
        </div>
      </div>
    </div>
  )
}, (prevProps, nextProps) => {
  return (
    prevProps.user.user_id === nextProps.user.user_id &&
    prevProps.user.status === nextProps.user.status &&
    prevProps.user.role === nextProps.user.role &&
    prevProps.user.is_writing === nextProps.user.is_writing &&
    prevProps.user.current_section === nextProps.user.current_section &&
    prevProps.user.last_seen === nextProps.user.last_seen
  )
})

export function PresenceIndicator({
  projectId,
  variant = 'compact',
  showWritingIndicator = true,
  maxAvatars = 5,
  className
}: PresenceIndicatorProps) {
  const [isOpen, setIsOpen] = useState(false)
  
  const { 
    users, 
    isLoading, 
    error, 
    onlineCount, 
    writingUsers 
  } = usePresence({ projectId })

  if (isLoading) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className="w-8 h-8 rounded-full bg-muted animate-pulse" />
        <div className="w-4 h-4 bg-muted rounded animate-pulse" />
      </div>
    )
  }

  if (error) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className={cn("flex items-center gap-2 text-destructive", className)}>
              <WifiOff className="w-4 h-4" />
              <AlertCircle className="w-4 h-4" />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Connection error: {error}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  const onlineUsers = users.filter(u => u.status === 'online')
  const displayUsers = onlineUsers.slice(0, maxAvatars)
  const hiddenCount = Math.max(0, onlineUsers.length - maxAvatars)

  if (variant === 'minimal') {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        <Wifi className="w-3 h-3 text-green-500" />
        <span className="text-xs text-muted-foreground">{onlineCount}</span>
      </div>
    )
  }

  if (variant === 'compact') {
    return (
      <TooltipProvider>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={cn("h-auto p-1 gap-2", className)}
            >
              <div className="flex -space-x-2">
                {displayUsers.map((user) => (
                  <UserAvatar key={user.user_id} user={user} size="sm" />
                ))}
                {hiddenCount > 0 && (
                  <div className="w-8 h-8 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                    <span className="text-xs font-medium">+{hiddenCount}</span>
                  </div>
                )}
              </div>
              
              {showWritingIndicator && writingUsers.length > 0 && (
                <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                  <PenTool className="w-3 h-3 mr-1" />
                  {writingUsers.length}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          
          <PopoverContent className="w-80 p-0" align="end">
            <Card className="border-0 shadow-none">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Users className="w-4 h-4" />
                  Collaborators ({users.length})
                </CardTitle>
              </CardHeader>
              
              <CardContent className="space-y-3">
                {users.length === 0 ? (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No collaborators online
                  </p>
                ) : (
                  <div className="space-y-2">
                    {users.map((user) => (
                      <TooltipProvider key={user.user_id}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                              <UserAvatar user={user} size="md" />
                              
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">
                                  {user.full_name || user.email}
                                </p>
                                <div className="flex items-center gap-2">
                                  <Badge 
                                    variant="outline" 
                                    className={cn(
                                      "text-xs",
                                      user.status === 'online' && "border-green-500 text-green-700",
                                      user.status === 'away' && "border-yellow-500 text-yellow-700",
                                      user.status === 'busy' && "border-red-500 text-red-700",
                                      user.status === 'offline' && "border-gray-400 text-gray-600"
                                    )}
                                  >
                                    {user.status}
                                  </Badge>
                                  
                                  {user.is_writing && user.status === 'online' && (
                                    <Badge variant="secondary" className="text-xs">
                                      <PenTool className="w-3 h-3 mr-1" />
                                      Writing
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                          </TooltipTrigger>
                          
                          <TooltipContent side="left">
                            <UserTooltipContent user={user} />
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </PopoverContent>
        </Popover>
      </TooltipProvider>
    )
  }

  // Full variant
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Collaborators
          </div>
          <Badge variant="outline">{onlineCount} online</Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <AnimatePresence>
          {users.map((user) => (
            <motion.div
              key={user.user_id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center gap-3 p-2 rounded-lg bg-muted/30"
            >
              <UserAvatar user={user} size="md" />
              
              <div className="flex-1 min-w-0">
                <p className="font-medium truncate">
                  {user.full_name || user.email}
                </p>
                <div className="flex items-center gap-2">
                  <Badge 
                    variant="outline" 
                    className={cn(
                      "text-xs",
                      user.status === 'online' && "border-green-500 text-green-700",
                      user.status === 'away' && "border-yellow-500 text-yellow-700",
                      user.status === 'busy' && "border-red-500 text-red-700",
                      user.status === 'offline' && "border-gray-400 text-gray-600"
                    )}
                  >
                    {user.status}
                  </Badge>
                  
                  <Badge variant="outline" className="text-xs">
                    {user.role}
                  </Badge>
                  
                  {user.is_writing && user.status === 'online' && (
                    <Badge variant="secondary" className="text-xs">
                      <PenTool className="w-3 h-3 mr-1" />
                      Writing
                    </Badge>
                  )}
                </div>
                
                {user.current_section && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Working on: {user.current_section}
                  </p>
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
        
        {users.length === 0 && (
          <div className="text-center py-8">
            <Users className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">No collaborators online</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}