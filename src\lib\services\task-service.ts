import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { ServiceBase, ServiceResponse } from './base-service';
import { Database } from '@/lib/db/database.types';

type ProcessingTask = Database['public']['Tables']['processing_tasks']['Row'];

export interface TaskData {
  type: string;
  payload: Record<string, unknown>;
  priority?: 'low' | 'medium' | 'high';
  retryAttempts?: number;
  maxRetries?: number;
  scheduledFor?: Date;
}

export interface TaskResult {
  success: boolean;
  result?: unknown;
  error?: string;
  duration?: number;
}

export class TaskService extends ServiceBase {
  constructor() {
    super({
      name: 'task-service',
      version: '1.0.0',
      endpoints: ['/api/processing-tasks'],
      dependencies: [],
      healthCheck: '/api/services/task/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, { status: 'healthy' });
  }

  /**
   * Create a new background task
   */
  async createTask(
    userId: string,
    taskData: TaskData
  ): Promise<ServiceResponse<ProcessingTask>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('processing_tasks')
        .insert({
          user_id: userId,
          task_type: taskData.type,
          payload: taskData.payload,
          status: 'pending',
          priority: taskData.priority || 'medium',
          retry_attempts: 0,
          max_retries: taskData.maxRetries || 3,
          scheduled_for: taskData.scheduledFor?.toISOString() || new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        logger.error('[TaskService] Error creating task:', error);
        throw error;
      }

      return data;
    });
  }

  /**
   * Get pending tasks ready for processing
   */
  async getPendingTasks(limit: number = 50): Promise<ServiceResponse<ProcessingTask[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('processing_tasks')
        .select('*')
        .eq('status', 'pending')
        .lte('scheduled_for', new Date().toISOString())
        .order('priority', { ascending: false })
        .order('created_at', { ascending: true })
        .limit(limit);

      if (error) {
        logger.error('[TaskService] Error fetching pending tasks:', error);
        throw error;
      }

      return data || [];
    });
  }

  /**
   * Update task status
   */
  async updateTaskStatus(
    taskId: string,
    status: 'pending' | 'running' | 'completed' | 'failed',
    result?: TaskResult
  ): Promise<ServiceResponse<ProcessingTask>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      };

      if (result) {
        updateData.result = result.result;
        updateData.error_message = result.error;
        
        if (result.duration) {
          updateData.processing_time = result.duration;
        }

        if (status === 'completed') {
          updateData.completed_at = new Date().toISOString();
        }
      }

      const { data, error } = await supabase
        .from('processing_tasks')
        .update(updateData)
        .eq('id', taskId)
        .select()
        .single();

      if (error) {
        logger.error('[TaskService] Error updating task status:', error);
        throw error;
      }

      return data;
    });
  }

  /**
   * Mark task as failed and handle retry logic
   */
  async handleTaskFailure(
    taskId: string,
    error: string
  ): Promise<ServiceResponse<ProcessingTask>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      // Get current task to check retry attempts
      const { data: currentTask } = await supabase
        .from('processing_tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (!currentTask) {
        throw new Error('Task not found');
      }

      const retryAttempts = (currentTask.retry_attempts || 0) + 1;
      const maxRetries = currentTask.max_retries || 3;

      let status: 'pending' | 'failed' = 'failed';
      let scheduledFor = currentTask.scheduled_for;

      // If we haven't exceeded max retries, schedule for retry
      if (retryAttempts <= maxRetries) {
        status = 'pending';
        // Exponential backoff: 2^attempt minutes
        const backoffMinutes = Math.pow(2, retryAttempts);
        scheduledFor = new Date(Date.now() + backoffMinutes * 60 * 1000).toISOString();
      }

      const { data, error: updateError } = await supabase
        .from('processing_tasks')
        .update({
          status,
          retry_attempts: retryAttempts,
          scheduled_for: scheduledFor,
          error_message: error,
          updated_at: new Date().toISOString()
        })
        .eq('id', taskId)
        .select()
        .single();

      if (updateError) {
        logger.error('[TaskService] Error handling task failure:', updateError);
        throw updateError;
      }

      return data;
    });
  }

  /**
   * Get task by ID
   */
  async getTask(taskId: string): Promise<ServiceResponse<ProcessingTask>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('processing_tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (error) {
        logger.error('[TaskService] Error fetching task:', error);
        throw error;
      }

      if (!data) {
        throw new Error('Task not found');
      }

      return data;
    });
  }

  /**
   * Get user tasks
   */
  async getUserTasks(
    userId: string,
    options: {
      status?: string;
      taskType?: string;
      limit?: number;
    } = {}
  ): Promise<ServiceResponse<ProcessingTask[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      let query = supabase
        .from('processing_tasks')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (options.status) {
        query = query.eq('status', options.status);
      }

      if (options.taskType) {
        query = query.eq('task_type', options.taskType);
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error } = await query;

      if (error) {
        logger.error('[TaskService] Error fetching user tasks:', error);
        throw error;
      }

      return data || [];
    });
  }

  /**
   * Delete completed or failed tasks older than specified days
   */
  async cleanupOldTasks(daysOld: number = 30): Promise<ServiceResponse<number>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const { data, error } = await supabase
        .from('processing_tasks')
        .delete()
        .in('status', ['completed', 'failed'])
        .lt('updated_at', cutoffDate.toISOString())
        .select('id');

      if (error) {
        logger.error('[TaskService] Error cleaning up old tasks:', error);
        throw error;
      }

      const deletedCount = data?.length || 0;
      logger.info(`[TaskService] Cleaned up ${deletedCount} old tasks`);
      
      return deletedCount;
    });
  }

  /**
   * Cancel a pending task
   */
  async cancelTask(taskId: string, userId?: string): Promise<ServiceResponse<ProcessingTask>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      let query = supabase
        .from('processing_tasks')
        .update({
          status: 'failed',
          error_message: 'Task cancelled by user',
          updated_at: new Date().toISOString()
        })
        .eq('id', taskId)
        .eq('status', 'pending');

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query
        .select()
        .single();

      if (error) {
        logger.error('[TaskService] Error cancelling task:', error);
        throw error;
      }

      return data;
    });
  }

  /**
   * Get task statistics
   */
  async getTaskStats(): Promise<ServiceResponse<Record<string, number>>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('processing_tasks')
        .select('status, task_type')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

      if (error) {
        logger.error('[TaskService] Error fetching task stats:', error);
        throw error;
      }

      const stats: Record<string, number> = {
        total: data?.length || 0,
        pending: 0,
        running: 0,
        completed: 0,
        failed: 0
      };

      data?.forEach(task => {
        stats[task.status]++;
      });

      return stats;
    });
  }

  /**
   * Process email queue tasks
   */
  async processEmailQueue(): Promise<ServiceResponse<number>> {
    return this.withErrorHandling(async () => {
      const tasksResponse = await this.getPendingTasks(50);
      
      if (!tasksResponse.success) {
        throw new Error('Failed to fetch pending tasks');
      }

      const emailTasks = tasksResponse.data.filter(task => 
        task.task_type === 'send_email' || task.task_type === 'bulk_email'
      );

      let processedCount = 0;
      
      for (const task of emailTasks) {
        try {
          await this.updateTaskStatus(task.id, 'running');
          
          // Process email task here
          // This would integrate with the email service
          
          await this.updateTaskStatus(task.id, 'completed', {
            success: true,
            result: { emailsSent: 1 }
          });
          
          processedCount++;
        } catch (error) {
          await this.handleTaskFailure(task.id, error instanceof Error ? error.message : 'Unknown error');
        }
      }

      return processedCount;
    });
  }

  /**
   * Process export queue tasks
   */
  async processExportQueue(): Promise<ServiceResponse<number>> {
    return this.withErrorHandling(async () => {
      const tasksResponse = await this.getPendingTasks(10); // Smaller batch for exports
      
      if (!tasksResponse.success) {
        throw new Error('Failed to fetch pending tasks');
      }

      const exportTasks = tasksResponse.data.filter(task => 
        task.task_type === 'export_project' || task.task_type === 'export_series'
      );

      let processedCount = 0;
      
      for (const task of exportTasks) {
        try {
          await this.updateTaskStatus(task.id, 'running');
          
          // Process export task here
          // This would integrate with export service
          
          await this.updateTaskStatus(task.id, 'completed', {
            success: true,
            result: { exportUrl: 'https://example.com/export.pdf' }
          });
          
          processedCount++;
        } catch (error) {
          await this.handleTaskFailure(task.id, error instanceof Error ? error.message : 'Unknown error');
        }
      }

      return processedCount;
    });
  }

  /**
   * Check writing goals and send notifications
   */
  async checkWritingGoals(): Promise<ServiceResponse<number>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Get active goals that should be checked
      const { data: goals, error } = await supabase
        .from('writing_goals')
        .select(`
          *,
          profiles!inner(id, email)
        `)
        .eq('status', 'active')
        .lte('deadline', new Date().toISOString())
        .is('last_reminder_sent', null);

      if (error) {
        logger.error('[TaskService] Error fetching goals for checking:', error);
        throw error;
      }

      let notificationsCreated = 0;

      for (const goal of goals || []) {
        try {
          // Create notification task
          await this.createTask(goal.user_id, {
            type: 'send_goal_reminder',
            payload: {
              goalId: goal.id,
              goalTitle: goal.title,
              deadline: goal.deadline,
              currentValue: goal.current_value,
              targetValue: goal.target_value
            }
          });

          // Mark goal as notified
          await supabase
            .from('writing_goals')
            .update({ last_reminder_sent: new Date().toISOString() })
            .eq('id', goal.id);

          notificationsCreated++;
        } catch (error) {
          logger.error(`[TaskService] Error creating goal reminder for goal ${goal.id}:`, error);
        }
      }

      return notificationsCreated;
    });
  }
}