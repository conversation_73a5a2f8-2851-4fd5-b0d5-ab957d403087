// Shared types for location components to prevent circular dependencies

export interface Location {
  id: string
  name: string
  description?: string
  parent_location_id?: string
  location_type: 'world' | 'continent' | 'country' | 'region' | 'city' | 'building' | 'room' | 'other'
  features: string[]
  significance?: string
  is_shareable: boolean
  series_id?: string
  universe_id?: string
  created_at: string
  updated_at: string
  parent_location?: {
    id: string
    name: string
  }
  child_locations?: Location[]
  series?: {
    id: string
    title: string
  }
  universe?: {
    id: string
    name: string
  }
}

export interface LocationWithHierarchy extends Location {
  level: number
  path: string[]
}

export interface LocationMapPosition {
  x: number
  y: number
  z?: number
}

export interface LocationNode {
  id: string
  name: string
  type: Location['location_type']
  description?: string
  children: LocationNode[]
  isExpanded?: boolean
  isSelected?: boolean
  position?: LocationMapPosition
}