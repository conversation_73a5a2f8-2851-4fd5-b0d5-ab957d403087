'use client'

import React, { <PERSON>mpo<PERSON>, <PERSON>rrorInfo, ReactNode } from 'react'
import { AlertCircle } from 'lucide-react'
import { RefreshCw } from 'lucide-react'
import { Home } from 'lucide-react'
import { Bug } from 'lucide-react'
import { Wifi } from 'lucide-react'
import { ShieldAlert } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { cn } from '@/lib/utils'
import { logger } from '@/lib/services/logger'

// ============================================================================
// UNIFIED ERROR SYSTEM - Consolidates all error handling components
// ============================================================================

// Error types
type ErrorType = 'general' | 'api' | 'ai' | 'payment' | 'network' | 'permission' | 'validation'
type ErrorSeverity = 'info' | 'warning' | 'error' | 'critical'

interface ErrorDetails {
  type?: ErrorType
  severity?: ErrorSeverity
  code?: string
  timestamp?: Date
  context?: Record<string, any>
}

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorDetails: ErrorDetails
}

interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: (error: Error, reset: () => void) => ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo, details: ErrorDetails) => void
  type?: ErrorType
  showDetails?: boolean
  className?: string
}

// ============================================================================
// BASE ERROR BOUNDARY
// ============================================================================

export class UnifiedErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorDetails: {
        type: props.type || 'general',
        severity: 'error',
        timestamp: new Date()
      }
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorDetails: {
        severity: 'error',
        timestamp: new Date()
      }
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const errorDetails: ErrorDetails = {
      type: this.props.type || this.detectErrorType(error),
      severity: this.detectErrorSeverity(error),
      code: this.extractErrorCode(error),
      timestamp: new Date(),
      context: {
        componentStack: errorInfo.componentStack,
        ...this.state.errorDetails.context
      }
    }

    // Log error
    logger.logError(error, {
      ...errorDetails,
      errorInfo
    })

    // Update state
    this.setState({
      error,
      errorInfo,
      errorDetails
    })

    // Call custom error handler
    this.props.onError?.(error, errorInfo, errorDetails)
  }

  detectErrorType(error: Error): ErrorType {
    const message = error.message.toLowerCase()
    
    if (message.includes('fetch') || message.includes('network')) return 'network'
    if (message.includes('api') || message.includes('request')) return 'api'
    if (message.includes('ai') || message.includes('openai') || message.includes('generation')) return 'ai'
    if (message.includes('payment') || message.includes('stripe')) return 'payment'
    if (message.includes('permission') || message.includes('unauthorized')) return 'permission'
    if (message.includes('validation') || message.includes('invalid')) return 'validation'
    
    return 'general'
  }

  detectErrorSeverity(error: Error): ErrorSeverity {
    const message = error.message.toLowerCase()
    
    if (message.includes('critical') || message.includes('fatal')) return 'critical'
    if (message.includes('warning')) return 'warning'
    if (message.includes('info')) return 'info'
    
    return 'error'
  }

  extractErrorCode(error: Error): string | undefined {
    // Try to extract error code from various formats
    const codeMatch = error.message.match(/\[([A-Z0-9_]+)\]/)
    if (codeMatch) return codeMatch[1]
    
    if ('code' in error) return String(error.code)
    
    return undefined
  }

  reset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorDetails: {
        type: this.props.type || 'general',
        severity: 'error',
        timestamp: new Date()
      }
    })
  }

  render() {
    if (this.state.hasError && this.state.error) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.reset)
      }

      // Use default error UI
      return (
        <ErrorDisplay
          error={this.state.error}
          errorDetails={this.state.errorDetails}
          onReset={this.reset}
          showDetails={this.props.showDetails}
          className={this.props.className}
        />
      )
    }

    return this.props.children
  }
}

// ============================================================================
// ERROR DISPLAY COMPONENT
// ============================================================================

interface ErrorDisplayProps {
  error: Error
  errorDetails: ErrorDetails
  onReset: () => void
  showDetails?: boolean
  className?: string
}

function ErrorDisplay({ error, errorDetails, onReset, showDetails = false, className }: ErrorDisplayProps) {
  const { icon: Icon, title, description, actions } = getErrorContent(error, errorDetails)

  return (
    <div className={cn('flex items-center justify-center min-h-[400px] p-4', className)}>
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-destructive/10">
              <Icon className="h-6 w-6 text-destructive" />
            </div>
            <div>
              <CardTitle className="text-display-sm font-literary-display">{title}</CardTitle>
              <CardDescription className="font-mono text-mono-sm">{description}</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {errorDetails.code && (
            <div className="text-mono-xs font-mono text-muted-foreground">
              Error Code: {errorDetails.code}
            </div>
          )}
          
          {showDetails && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle className="font-mono font-bold">Technical Details</AlertTitle>
              <AlertDescription className="font-mono text-mono-xs">
                <pre className="whitespace-pre-wrap break-all mt-2">
                  {error.stack || error.message}
                </pre>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2">
            <Button onClick={onReset} className="font-literary-display">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            {actions}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// ============================================================================
// ERROR CONTENT MAPPING
// ============================================================================

function getErrorContent(error: Error, details: ErrorDetails) {
  const configs: Record<ErrorType, {
    icon: React.ComponentType<{ className?: string }>
    title: string
    description: string
    actions?: ReactNode
  }> = {
    network: {
      icon: Wifi,
      title: 'Connection Issue',
      description: 'Unable to connect to our servers. Please check your internet connection.',
      actions: (
        <Button variant="outline" asChild className="font-literary-display">
          <a href="/">
            <Home className="mr-2 h-4 w-4" />
            Go Home
          </a>
        </Button>
      )
    },
    api: {
      icon: AlertCircle,
      title: 'Server Error',
      description: 'Something went wrong on our end. Please try again in a moment.',
    },
    ai: {
      icon: Bug,
      title: 'AI Generation Error',
      description: 'The AI encountered an issue. Please try regenerating or contact support.',
    },
    payment: {
      icon: ShieldAlert,
      title: 'Payment Error',
      description: 'There was an issue processing your payment. Please try again.',
    },
    permission: {
      icon: ShieldAlert,
      title: 'Access Denied',
      description: 'You don\'t have permission to access this resource.',
      actions: (
        <Button variant="outline" asChild className="font-literary-display">
          <a href="/login">
            Sign In
          </a>
        </Button>
      )
    },
    validation: {
      icon: AlertCircle,
      title: 'Invalid Input',
      description: 'Please check your input and try again.',
    },
    general: {
      icon: AlertCircle,
      title: 'Something went wrong',
      description: 'An unexpected error occurred. Please try again.',
    }
  }

  return configs[details.type || 'general']
}

// ============================================================================
// SPECIALIZED ERROR BOUNDARIES
// ============================================================================

export function APIErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <UnifiedErrorBoundary type="api">
      {children}
    </UnifiedErrorBoundary>
  )
}

export function AIErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <UnifiedErrorBoundary type="ai">
      {children}
    </UnifiedErrorBoundary>
  )
}

export function PaymentErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <UnifiedErrorBoundary type="payment">
      {children}
    </UnifiedErrorBoundary>
  )
}

// ============================================================================
// ERROR STATE COMPONENTS
// ============================================================================

interface ErrorStateProps {
  error?: Error | string
  title?: string
  description?: string
  action?: ReactNode
  className?: string
}

export function ErrorState({ 
  error, 
  title = 'Something went wrong',
  description = 'An error occurred while loading this content.',
  action,
  className 
}: ErrorStateProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center p-8 text-center', className)}>
      <div className="p-3 rounded-full bg-destructive/10 mb-4">
        <AlertCircle className="h-8 w-8 text-destructive" />
      </div>
      <h3 className="text-display-sm font-literary-display mb-2">{title}</h3>
      <p className="text-mono-base font-mono text-muted-foreground mb-4 max-w-md">
        {description}
      </p>
      {error && (
        <p className="text-mono-sm font-mono text-muted-foreground mb-4">
          {typeof error === 'string' ? error : error.message}
        </p>
      )}
      {action || (
        <Button onClick={() => window.location.reload()} className="font-literary-display">
          <RefreshCw className="mr-2 h-4 w-4" />
          Try Again
        </Button>
      )}
    </div>
  )
}

// ============================================================================
// INLINE ERROR COMPONENT
// ============================================================================

interface InlineErrorProps {
  error: string | Error
  className?: string
}

export function InlineError({ error, className }: InlineErrorProps) {
  const message = typeof error === 'string' ? error : error.message

  return (
    <div className={cn('flex items-center gap-2 text-destructive text-mono-sm font-mono', className)}>
      <AlertCircle className="h-4 w-4 shrink-0" />
      <span>{message}</span>
    </div>
  )
}

// ============================================================================
// ASYNC ERROR BOUNDARY
// ============================================================================

interface AsyncBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  errorFallback?: (error: Error, reset: () => void) => ReactNode
}

export function AsyncBoundary({ children, fallback, errorFallback }: AsyncBoundaryProps) {
  return (
    <UnifiedErrorBoundary fallback={errorFallback}>
      <React.Suspense fallback={fallback || <SectionLoading />}>
        {children}
      </React.Suspense>
    </UnifiedErrorBoundary>
  )
}

// Import from unified loading
import { SectionLoading } from '@/components/ui/unified-loading'