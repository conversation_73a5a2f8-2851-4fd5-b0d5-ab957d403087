// Optimized Query Patterns for BookScribe
// These patterns reduce data transfer and improve performance

import { createTypedServerClient } from '@/lib/supabase'
import type { Database } from '@/lib/db/types'

// ============================================
// Project Queries
// ============================================

// List projects (optimized for dashboard)
export async function getProjectsList(userId: string) {
  const supabase = await createTypedServerClient()
  
  return supabase
    .from('projects')
    .select('id, title, status, primary_genre, updated_at, total_word_count')
    .eq('user_id', userId)
    .order('updated_at', { ascending: false })
    .limit(20)
}

// Get project details (only needed fields)
export async function getProjectDetails(projectId: string) {
  const supabase = await createTypedServerClient()
  
  return supabase
    .from('projects')
    .select(`
      id, title, description, status, 
      primary_genre, secondary_genre,
      target_audience, writing_style,
      narrative_voice, setting_time_period,
      setting_location, target_word_count,
      target_chapters, total_word_count,
      project_settings, created_at, updated_at
    `)
    .eq('id', projectId)
    .single()
}

// Count user projects efficiently
export async function getProjectCount(userId: string) {
  const supabase = await createTypedServerClient()
  
  const { count } = await supabase
    .from('projects')
    .select('id', { count: 'exact', head: true })
    .eq('user_id', userId)
  
  return count || 0
}

// ============================================
// Chapter Queries
// ============================================

// List chapters (minimal data for performance)
export async function getChaptersList(projectId: string) {
  const supabase = await createTypedServerClient()
  
  return supabase
    .from('chapters')
    .select('id, title, chapter_number, word_count, status')
    .eq('project_id', projectId)
    .order('chapter_number')
}

// Get chapter for editing (full content)
export async function getChapterForEdit(chapterId: string) {
  const supabase = await createTypedServerClient()
  
  return supabase
    .from('chapters')
    .select(`
      id, title, chapter_number, content,
      word_count, status, scenes,
      character_states, plot_advancement,
      ai_analysis, updated_at
    `)
    .eq('id', chapterId)
    .single()
}

// ============================================
// Character Queries
// ============================================

// List characters (optimized for character panel)
export async function getCharactersList(projectId: string) {
  const supabase = await createTypedServerClient()
  
  return supabase
    .from('characters')
    .select('id, name, role, avatar_url')
    .eq('project_id', projectId)
    .order('created_at')
}

// ============================================
// Analytics Queries
// ============================================

// Get writing statistics efficiently
export async function getWritingStats(userId: string, dateRange: { start: Date, end: Date }) {
  const supabase = await createTypedServerClient()
  
  // Use aggregate functions instead of fetching all data
  const { data } = await supabase
    .from('writing_sessions')
    .select('word_count.sum(), duration.sum(), started_at.count()')
    .eq('user_id', userId)
    .gte('started_at', dateRange.start.toISOString())
    .lte('started_at', dateRange.end.toISOString())
    .single()
  
  return {
    totalWords: data?.word_count || 0,
    totalDuration: data?.duration || 0,
    sessionCount: data?.started_at || 0
  }
}

// ============================================
// Batch Operations
// ============================================

// Fetch multiple related entities efficiently
export async function getProjectWithRelations(projectId: string) {
  const supabase = await createTypedServerClient()
  
  // Single query with joins instead of multiple queries
  return supabase
    .from('projects')
    .select(`
      id, title, status, total_word_count,
      chapters!inner(
        id, title, chapter_number, word_count, status
      ),
      characters!inner(
        id, name, role
      )
    `)
    .eq('id', projectId)
    .single()
}

// ============================================
// Search Queries
// ============================================

// Efficient full-text search
export async function searchProjects(userId: string, query: string) {
  const supabase = await createTypedServerClient()
  
  return supabase
    .from('projects')
    .select('id, title, description, primary_genre')
    .eq('user_id', userId)
    .or(`title.ilike.%${query}%,description.ilike.%${query}%`)
    .limit(10)
}

// ============================================
// Real-time Subscriptions
// ============================================

// Subscribe to specific fields only
export function subscribeToProjectUpdates(projectId: string) {
  const supabase = createTypedBrowserClient()
  
  return supabase
    .channel(`project:${projectId}`)
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'projects',
        filter: `id=eq.${projectId}`
      },
      (payload) => {
        // Only listen for specific field changes
        const { title, status, total_word_count } = payload.new
        // Handle update...
      }
    )
    .subscribe()
}
