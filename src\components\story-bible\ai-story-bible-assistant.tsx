'use client'

import { useState, useC<PERSON>back, useRef, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { logger } from '@/lib/services/logger'
import { Bot } from 'lucide-react'
import { Brain } from 'lucide-react'
import { Sparkles } from 'lucide-react'
import { MessageSquare } from 'lucide-react'
import { Lightbulb } from 'lucide-react'
import { Users } from 'lucide-react'
import { Map } from 'lucide-react'
import { Clock } from 'lucide-react'
import { AlertCircle } from 'lucide-react'
import { CheckCircle } from 'lucide-react'
import { Loader2 } from 'lucide-react'
import { Send } from 'lucide-react'
import { RefreshCw } from 'lucide-react'
import { Star } from 'lucide-react'
import { Target } from 'lucide-react'
import { Zap } from 'lucide-react'
import { BookOpen } from 'lucide-react'
import { Settings } from 'lucide-react'
import { Wand2 } from 'lucide-react'
import { ComponentErrorBoundary } from '@/components/error/unified-error-boundary'

interface AIMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  type?: 'suggestion' | 'analysis' | 'question' | 'enhancement'
  metadata?: {
    category?: 'character' | 'world' | 'plot' | 'timeline'
    confidence?: number
    source?: string
  }
}

interface AISuggestion {
  id: string
  type: 'character' | 'world' | 'plot' | 'timeline'
  title: string
  description: string
  confidence: number
  impact: 'low' | 'medium' | 'high'
  actionable: boolean
  data?: any
}

interface AIStoryBibleAssistantProps {
  projectId: string
  storyBible: {
    characters: Array<{ id: string; name: string; role: string; description: string }>
    worldRules: Record<string, string>
    timeline: Array<{ id?: string; event: string; chapter: number }>
    plotThreads: Array<{ id: string; description: string; status: string }>
  }
  onApplySuggestion?: (suggestion: AISuggestion) => void
  onUpdateBible?: (updates: any) => void
}

function AIStoryBibleAssistantComponent({ 
  projectId, 
  storyBible, 
  onApplySuggestion,
  onUpdateBible 
}: AIStoryBibleAssistantProps) {
  const [messages, setMessages] = useState<AIMessage[]>([])
  const [currentMessage, setCurrentMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([])
  const [activeTab, setActiveTab] = useState<'chat' | 'suggestions' | 'analysis'>('chat')
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [analysisData, setAnalysisData] = useState<any>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    // Initial AI greeting and analysis
    initializeAssistant()
  }, [projectId])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const initializeAssistant = async () => {
    try {
      const response = await fetch('/api/ai/story-bible-assistant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'initialize',
          projectId,
          storyBible
        })
      })

      const data = await response.json()
      
      if (data.greeting) {
        addMessage({
          role: 'assistant',
          content: data.greeting,
          type: 'question'
        })
      }

      if (data.suggestions) {
        setSuggestions(data.suggestions)
      }

      if (data.analysis) {
        setAnalysisData(data.analysis)
      }
    } catch (error) {
      logger.error('Failed to initialize AI assistant:', error)
    }
  }

  const addMessage = (message: Omit<AIMessage, 'id' | 'timestamp'>) => {
    const newMessage: AIMessage = {
      ...message,
      id: Date.now().toString(),
      timestamp: new Date()
    }
    setMessages(prev => [...prev, newMessage])
  }

  const sendMessage = async () => {
    if (!currentMessage.trim() || isLoading) return

    const userMessage = currentMessage.trim()
    setCurrentMessage('')
    
    addMessage({
      role: 'user',
      content: userMessage
    })

    setIsLoading(true)

    try {
      const response = await fetch('/api/ai/story-bible-assistant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'chat',
          projectId,
          message: userMessage,
          storyBible,
          context: {
            recentMessages: messages.slice(-5),
            currentSuggestions: suggestions
          }
        })
      })

      const data = await response.json()

      if (data.response) {
        addMessage({
          role: 'assistant',
          content: data.response,
          type: data.type || 'question',
          metadata: data.metadata
        })
      }

      if (data.suggestions) {
        setSuggestions(prev => [...prev, ...data.suggestions])
      }

      if (data.updatedAnalysis) {
        setAnalysisData(data.updatedAnalysis)
      }
    } catch (error) {
      logger.error('Failed to send message:', error)
      toast({
        title: 'Error',
        description: 'Failed to get AI response',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const applySuggestion = async (suggestion: AISuggestion) => {
    try {
      const response = await fetch('/api/ai/story-bible-assistant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'apply_suggestion',
          projectId,
          suggestion
        })
      })

      const data = await response.json()

      if (data.success) {
        setSuggestions(prev => prev.filter(s => s.id !== suggestion.id))
        onApplySuggestion?.(suggestion)
        
        if (data.updatedBible) {
          onUpdateBible?.(data.updatedBible)
        }

        toast({
          title: 'Success',
          description: 'Suggestion applied successfully'
        })

        addMessage({
          role: 'assistant',
          content: `I've applied the suggestion: "${suggestion.title}". Your story bible has been updated accordingly.`,
          type: 'enhancement'
        })
      }
    } catch (error) {
      logger.error('Failed to apply suggestion:', error)
      toast({
        title: 'Error',
        description: 'Failed to apply suggestion',
        variant: 'destructive'
      })
    }
  }

  const generateNewSuggestions = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/ai/story-bible-assistant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generate_suggestions',
          projectId,
          storyBible,
          excludeExisting: suggestions.map(s => s.id)
        })
      })

      const data = await response.json()

      if (data.suggestions) {
        setSuggestions(prev => [...prev, ...data.suggestions])
        toast({
          title: 'New Suggestions',
          description: `Generated ${data.suggestions.length} new suggestions`
        })
      }
    } catch (error) {
      logger.error('Failed to generate suggestions:', error)
      toast({
        title: 'Error',
        description: 'Failed to generate new suggestions',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const runDeepAnalysis = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/ai/story-bible-assistant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'deep_analysis',
          projectId,
          storyBible
        })
      })

      const data = await response.json()

      if (data.analysis) {
        setAnalysisData(data.analysis)
        addMessage({
          role: 'assistant',
          content: `I've completed a deep analysis of your story bible. Here are the key insights: ${data.analysis.summary}`,
          type: 'analysis'
        })
        
        if (data.suggestions) {
          setSuggestions(prev => [...prev, ...data.suggestions])
        }
      }
    } catch (error) {
      logger.error('Failed to run deep analysis:', error)
      toast({
        title: 'Error',
        description: 'Failed to run deep analysis',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'character': return Users
      case 'world': return Map
      case 'plot': return Lightbulb
      case 'timeline': return Clock
      default: return Sparkles
    }
  }

  const getSuggestionColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'border-red-200 bg-red-50'
      case 'medium': return 'border-yellow-200 bg-yellow-50'
      case 'low': return 'border-blue-200 bg-blue-50'
      default: return 'border-gray-200 bg-gray-50'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                <Bot className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl">AI Story Bible Assistant</CardTitle>
                <CardDescription>
                  Intelligent assistance for building and maintaining your story bible
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={generateNewSuggestions}
                disabled={isLoading}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvanced(!showAdvanced)}
              >
                <Settings className="w-4 h-4 mr-2" />
                Advanced
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg">
        <Button
          variant={activeTab === 'chat' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('chat')}
          className="flex-1"
        >
          <MessageSquare className="w-4 h-4 mr-2" />
          Chat
        </Button>
        <Button
          variant={activeTab === 'suggestions' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('suggestions')}
          className="flex-1"
        >
          <Lightbulb className="w-4 h-4 mr-2" />
          Suggestions ({suggestions.length})
        </Button>
        <Button
          variant={activeTab === 'analysis' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('analysis')}
          className="flex-1"
        >
          <Brain className="w-4 h-4 mr-2" />
          Analysis
        </Button>
      </div>

      {/* Content */}
      {activeTab === 'chat' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Chat with AI Assistant</CardTitle>
            <CardDescription>
              Ask questions about your story, get suggestions, or discuss improvements
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-4">
                {messages.length === 0 && (
                  <div className="text-center py-8">
                    <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      Start a conversation about your story bible
                    </p>
                  </div>
                )}
                
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg p-3 ${
                        message.role === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                      }`}
                    >
                      <div className="flex items-start gap-2">
                        {message.role === 'assistant' && (
                          <Bot className="h-4 w-4 mt-0.5 text-muted-foreground" />
                        )}
                        <div className="flex-1">
                          <p className="text-sm">{message.content}</p>
                          {message.metadata && (
                            <div className="flex items-center gap-2 mt-2">
                              {message.metadata.category && (
                                <Badge variant="outline" className="text-xs">
                                  {message.metadata.category}
                                </Badge>
                              )}
                              {message.metadata.confidence && (
                                <Badge variant="secondary" className="text-xs">
                                  {Math.round(message.metadata.confidence * 100)}% confident
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                
                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-muted rounded-lg p-3">
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm">AI is thinking...</span>
                      </div>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
            
            <div className="flex gap-2">
              <Input
                placeholder="Ask about your story, characters, world..."
                value={currentMessage}
                onChange={(e) => setCurrentMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={isLoading}
              />
              <Button onClick={sendMessage} disabled={isLoading || !currentMessage.trim()}>
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'suggestions' && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">AI Suggestions</CardTitle>
                <CardDescription>
                  Intelligent recommendations to improve your story bible
                </CardDescription>
              </div>
              <Button
                onClick={generateNewSuggestions}
                disabled={isLoading}
                size="sm"
              >
                <Wand2 className="w-4 h-4 mr-2" />
                Generate More
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {suggestions.length === 0 ? (
                <div className="text-center py-8">
                  <Lightbulb className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    No suggestions available. Chat with the AI to get personalized recommendations.
                  </p>
                </div>
              ) : (
                suggestions.map((suggestion) => {
                  const IconComponent = getSuggestionIcon(suggestion.type)
                  return (
                    <Card 
                      key={suggestion.id} 
                      className={`transition-all hover:shadow-sm ${getSuggestionColor(suggestion.impact)}`}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-3 flex-1">
                            <div className="p-2 bg-white rounded-lg shadow-sm">
                              <IconComponent className="h-4 w-4" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h4 className="font-medium">{suggestion.title}</h4>
                                <Badge variant="outline" className="text-xs capitalize">
                                  {suggestion.type}
                                </Badge>
                                <Badge 
                                  variant="secondary" 
                                  className={`text-xs ${
                                    suggestion.impact === 'high' ? 'bg-red-100 text-red-800' :
                                    suggestion.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-blue-100 text-blue-800'
                                  }`}
                                >
                                  {suggestion.impact} impact
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground mb-2">
                                {suggestion.description}
                              </p>
                              <div className="flex items-center gap-2">
                                <div className="flex items-center gap-1">
                                  <Star className="h-3 w-3 text-yellow-500" />
                                  <span className="text-xs text-muted-foreground">
                                    {Math.round(suggestion.confidence * 100)}% confidence
                                  </span>
                                </div>
                                {suggestion.actionable && (
                                  <Badge variant="secondary" className="text-xs">
                                    <Target className="h-3 w-3 mr-1" />
                                    Actionable
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              onClick={() => applySuggestion(suggestion)}
                              disabled={!suggestion.actionable}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Apply
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'analysis' && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Story Bible Analysis</CardTitle>
                <CardDescription>
                  Deep insights into your story's structure and consistency
                </CardDescription>
              </div>
              <Button
                onClick={runDeepAnalysis}
                disabled={isLoading}
                size="sm"
              >
                <Brain className="w-4 h-4 mr-2" />
                Run Analysis
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {analysisData ? (
              <div className="space-y-6">
                {/* Overall Health Score */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {analysisData.overallScore || 85}
                      </div>
                      <p className="text-sm text-muted-foreground">Overall Health</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {analysisData.consistencyScore || 92}
                      </div>
                      <p className="text-sm text-muted-foreground">Consistency</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {analysisData.completenessScore || 78}
                      </div>
                      <p className="text-sm text-muted-foreground">Completeness</p>
                    </CardContent>
                  </Card>
                </div>

                {/* Detailed Analysis */}
                <div className="space-y-4">
                  {analysisData.insights?.map((insight: any, index: number) => (
                    <Card key={index}>
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <div className={`p-2 rounded-lg ${
                            insight.type === 'strength' ? 'bg-green-100 text-green-600' :
                            insight.type === 'weakness' ? 'bg-red-100 text-red-600' :
                            'bg-blue-100 text-blue-600'
                          }`}>
                            {insight.type === 'strength' ? <CheckCircle className="h-4 w-4" /> :
                             insight.type === 'weakness' ? <AlertCircle className="h-4 w-4" /> :
                             <Lightbulb className="h-4 w-4" />}
                          </div>
                          <div>
                            <h4 className="font-medium mb-1">{insight.title}</h4>
                            <p className="text-sm text-muted-foreground">{insight.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Run an analysis to get insights into your story bible
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Advanced Settings Dialog */}
      <Dialog open={showAdvanced} onOpenChange={setShowAdvanced}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Advanced AI Settings</DialogTitle>
            <DialogDescription>
              Configure AI assistant behavior and analysis preferences
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Analysis Depth</label>
              <Select defaultValue="medium">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="surface">Surface Level</SelectItem>
                  <SelectItem value="medium">Medium Depth</SelectItem>
                  <SelectItem value="deep">Deep Analysis</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Suggestion Style</label>
              <Select defaultValue="balanced">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="conservative">Conservative</SelectItem>
                  <SelectItem value="balanced">Balanced</SelectItem>
                  <SelectItem value="creative">Creative</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end">
              <Button onClick={() => setShowAdvanced(false)}>Save Settings</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Export with error boundary
export const AIStoryBibleAssistant = (props: AIStoryBibleAssistantProps) => (
  <ComponentErrorBoundary componentName="AIStoryBibleAssistant" isolate>
    <AIStoryBibleAssistantComponent {...props} />
  </ComponentErrorBoundary>
)