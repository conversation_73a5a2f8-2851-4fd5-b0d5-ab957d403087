import { NextRequest, NextResponse } from 'next/server'
import { TaskService } from '@/lib/services/task-service'
import { mailerooEmailService } from '@/lib/services'
import { logger } from '@/lib/services/logger'

// This endpoint should be called by a cron job (e.g., Vercel Cron or external service)
// Run every 5 minutes: */5 * * * *
export async function GET(request: NextRequest) {
  try {
    // Verify cron secret (optional but recommended)
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const taskService = new TaskService()
    await taskService.initialize()

    // Process email queue using TaskService
    const processedCountResponse = await taskService.processEmailQueue()
    
    if (!processedCountResponse.success) {
      logger.error('Failed to process email queue via TaskService:', processedCountResponse.error)
      
      // Fallback to direct email service processing
      await mailerooEmailService.processEmailQueue()
      
      return NextResponse.json({ 
        success: true, 
        message: 'Email queue processed (fallback method)',
        timestamp: new Date().toISOString(),
        processedCount: 'unknown'
      })
    }

    const processedCount = processedCountResponse.data

    logger.info('Email queue processed via cron', {
      processedTasks: processedCount,
      timestamp: new Date().toISOString()
    })

    return NextResponse.json({ 
      success: true, 
      message: 'Email queue processed',
      processedTasks: processedCount,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    logger.error('Error processing email queue:', error)
    return NextResponse.json(
      { error: 'Failed to process email queue' },
      { status: 500 }
    )
  }
}
