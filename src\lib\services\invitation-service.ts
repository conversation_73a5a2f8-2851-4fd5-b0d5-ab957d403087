import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { ServiceBase, ServiceResponse } from './base-service';
import { Database } from '@/lib/db/database.types';
import { v4 as uuidv4 } from 'uuid';

type ProjectInvitation = Database['public']['Tables']['project_invitations']['Row'];
type ProjectInvitationInsert = Database['public']['Tables']['project_invitations']['Insert'];
type ProjectCollaborator = Database['public']['Tables']['project_collaborators']['Row'];

export interface InvitationWithDetails extends ProjectInvitation {
  project?: {
    id: string;
    title: string;
  };
  inviter?: {
    id: string;
    email: string;
    profiles?: {
      full_name?: string;
    };
  };
}

export class InvitationService extends ServiceBase {
  constructor() {
    super({
      name: 'invitation-service',
      version: '1.0.0',
      endpoints: ['/api/invitations'],
      dependencies: [],
      healthCheck: '/api/services/invitation/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, { status: 'healthy' });
  }

  /**
   * Create a new project invitation
   */
  async createInvitation(
    projectId: string,
    inviterId: string,
    email: string,
    role: 'viewer' | 'editor' | 'admin' = 'viewer',
    expiresInDays: number = 7
  ): Promise<ServiceResponse<ProjectInvitation>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      // Check if invitation already exists for this email and project
      const { data: existingInvite } = await supabase
        .from('project_invitations')
        .select('id, status')
        .eq('project_id', projectId)
        .eq('email', email)
        .eq('status', 'pending')
        .single();

      if (existingInvite) {
        throw new Error('An invitation for this email already exists for this project');
      }

      // Check if user is already a collaborator
      const { data: existingCollab } = await supabase
        .from('project_collaborators')
        .select('id, status')
        .eq('project_id', projectId)
        .ilike('users.email', email)
        .single();

      if (existingCollab && existingCollab.status === 'active') {
        throw new Error('User is already a collaborator on this project');
      }

      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + expiresInDays);

      const invitationData: ProjectInvitationInsert = {
        project_id: projectId,
        inviter_id: inviterId,
        email: email.toLowerCase(),
        role,
        token: uuidv4(),
        expires_at: expiresAt.toISOString(),
        status: 'pending'
      };

      const { data, error } = await supabase
        .from('project_invitations')
        .insert(invitationData)
        .select()
        .single();

      if (error) {
        logger.error('[InvitationService] Error creating invitation:', error);
        throw error;
      }

      if (!data) {
        throw new Error('Failed to create invitation');
      }

      return data;
    });
  }

  /**
   * Get invitation by token
   */
  async getInvitationByToken(token: string): Promise<ServiceResponse<InvitationWithDetails>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('project_invitations')
        .select(`
          *,
          project:projects(
            id,
            title
          ),
          inviter:users!inviter_id(
            id,
            email,
            profiles(full_name)
          )
        `)
        .eq('token', token)
        .eq('status', 'pending')
        .single();

      if (error) {
        logger.error('[InvitationService] Error fetching invitation:', error);
        throw error;
      }

      if (!data) {
        throw new Error('Invitation not found or already used');
      }

      // Check if invitation has expired
      if (new Date(data.expires_at) < new Date()) {
        throw new Error('This invitation has expired');
      }

      return data as InvitationWithDetails;
    });
  }

  /**
   * Accept an invitation
   */
  async acceptInvitation(
    token: string,
    userId: string,
    userEmail: string
  ): Promise<ServiceResponse<{ 
    project: { id: string; title: string; role: string }; 
    message: string 
  }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      // Get the invitation details
      const invitationResponse = await this.getInvitationByToken(token);
      if (!invitationResponse.success) {
        throw new Error(invitationResponse.error?.message || 'Invalid invitation');
      }

      const invitation = invitationResponse.data;

      // Check if user's email matches the invitation (if not already registered)
      if (invitation.email && userEmail !== invitation.email) {
        throw new Error('This invitation was sent to a different email address');
      }

      // Check if user is already a collaborator
      const { data: existingCollab } = await supabase
        .from('project_collaborators')
        .select('id, status')
        .eq('project_id', invitation.project_id)
        .eq('user_id', userId)
        .single();

      if (existingCollab) {
        if (existingCollab.status === 'active') {
          throw new Error('You are already a collaborator on this project');
        } else if (existingCollab.status === 'removed') {
          // Reactivate the collaboration
          const { error: reactivateError } = await supabase
            .from('project_collaborators')
            .update({
              status: 'active',
              role: invitation.role,
              rejoined_at: new Date().toISOString()
            })
            .eq('id', existingCollab.id);

          if (reactivateError) {
            throw reactivateError;
          }

          // Mark invitation as accepted
          await supabase
            .from('project_invitations')
            .update({
              status: 'accepted',
              accepted_at: new Date().toISOString(),
              user_id: userId
            })
            .eq('id', invitation.id);

          return {
            project: {
              id: invitation.project!.id,
              title: invitation.project!.title,
              role: invitation.role
            },
            message: 'You have been re-added to the project'
          };
        }
      }

      // Mark invitation as accepted
      const { error: updateError } = await supabase
        .from('project_invitations')
        .update({
          status: 'accepted',
          accepted_at: new Date().toISOString(),
          user_id: userId
        })
        .eq('id', invitation.id);

      if (updateError) {
        logger.error('[InvitationService] Failed to accept invitation:', updateError);
        throw updateError;
      }

      // Add user as project collaborator
      const { error: collabError } = await supabase
        .from('project_collaborators')
        .insert({
          project_id: invitation.project_id,
          user_id: userId,
          role: invitation.role,
          status: 'active',
          invited_by: invitation.inviter_id,
          joined_at: new Date().toISOString()
        });

      if (collabError) {
        logger.error('[InvitationService] Failed to add collaborator:', collabError);
        
        // Revert invitation status
        await supabase
          .from('project_invitations')
          .update({ status: 'pending' })
          .eq('id', invitation.id);
        
        throw collabError;
      }

      // Create initial collaboration session for this user
      const sessionId = `project:${invitation.project_id}:welcome`;
      await supabase
        .from('collaboration_sessions')
        .upsert({
          id: sessionId,
          project_id: invitation.project_id,
          owner_id: invitation.project?.user_id || invitation.inviter_id,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'id'
        });

      return {
        project: {
          id: invitation.project!.id,
          title: invitation.project!.title,
          role: invitation.role
        },
        message: 'Successfully joined the project'
      };
    });
  }

  /**
   * Cancel an invitation
   */
  async cancelInvitation(invitationId: string): Promise<ServiceResponse<{ success: boolean }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { error } = await supabase
        .from('project_invitations')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString()
        })
        .eq('id', invitationId)
        .eq('status', 'pending');

      if (error) {
        logger.error('[InvitationService] Error cancelling invitation:', error);
        throw error;
      }

      return { success: true };
    });
  }

  /**
   * Get invitations for a project
   */
  async getProjectInvitations(projectId: string): Promise<ServiceResponse<InvitationWithDetails[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('project_invitations')
        .select(`
          *,
          inviter:users!inviter_id(
            id,
            email,
            profiles(full_name)
          )
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('[InvitationService] Error fetching project invitations:', error);
        throw error;
      }

      return data as InvitationWithDetails[] || [];
    });
  }

  /**
   * Clean up expired invitations
   */
  async cleanupExpiredInvitations(): Promise<ServiceResponse<{ deleted_count: number }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { count, error } = await supabase
        .from('project_invitations')
        .update({ status: 'expired' })
        .eq('status', 'pending')
        .lt('expires_at', new Date().toISOString())
        .select('id', { count: 'exact' });

      if (error) {
        logger.error('[InvitationService] Error cleaning up expired invitations:', error);
        throw error;
      }

      return { deleted_count: count || 0 };
    });
  }

  /**
   * Get user's active collaborations
   */
  async getUserCollaborations(userId: string): Promise<ServiceResponse<any[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data, error } = await supabase
        .from('project_collaborators')
        .select(`
          *,
          project:projects(
            id,
            title,
            description,
            status
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('joined_at', { ascending: false });

      if (error) {
        logger.error('[InvitationService] Error fetching user collaborations:', error);
        throw error;
      }

      return data || [];
    });
  }
}

// Create and export singleton instance
export const invitationService = new InvitationService();