#!/usr/bin/env node
import { readdir, readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface QueryOptimization {
  file: string;
  line: number;
  original: string;
  optimized: string;
  improvement: string;
  impact: 'high' | 'medium' | 'low';
}

// Common inefficient patterns and their optimizations
const OPTIMIZATION_PATTERNS = [
  {
    name: 'Select all when only count needed',
    pattern: /\.select\(['"][*]['"]\).*\.then\([^)]*data[^)]*\)[^}]*data\.length/,
    optimization: '.select("id", { count: "exact", head: true })',
    improvement: 'Use count instead of fetching all data',
    impact: 'high' as const
  },
  {
    name: 'Missing select specification',
    pattern: /\.from\(['"](\w+)['"]\)\s*\.eq/,
    optimization: 'Add .select() with specific columns',
    improvement: 'Specify columns to reduce data transfer',
    impact: 'medium' as const
  },
  {
    name: 'Inefficient pagination',
    pattern: /\.range\(0,\s*\d+\)/,
    optimization: 'Use .limit() with .order() for better performance',
    improvement: 'Use limit with proper ordering',
    impact: 'medium' as const
  },
  {
    name: 'Multiple single queries in loop',
    pattern: /for\s*\([^)]+\)\s*{[^}]*await\s+supabase[^}]*\.single\(\)/,
    optimization: 'Use .in() filter to fetch all at once',
    improvement: 'Replace loop queries with single batch query',
    impact: 'high' as const
  },
  {
    name: 'Unnecessary nested selects',
    pattern: /\.select\(['"][^'"]*\*[^'"]*['"]\)/,
    optimization: 'Specify exact fields needed',
    improvement: 'Select only required fields',
    impact: 'medium' as const
  }
];

// Query optimization recommendations
const QUERY_OPTIMIZATIONS = {
  // Projects table
  'projects': {
    commonFields: 'id, title, description, status, primary_genre, updated_at, total_word_count',
    listFields: 'id, title, status, primary_genre, updated_at, total_word_count',
    detailFields: `id, title, description, status, primary_genre, secondary_genre, 
                   target_audience, writing_style, narrative_voice, setting_time_period,
                   setting_location, target_word_count, target_chapters, total_word_count,
                   project_settings, created_at, updated_at`
  },
  
  // Chapters table
  'chapters': {
    commonFields: 'id, title, chapter_number, word_count, status, updated_at',
    listFields: 'id, title, chapter_number, word_count, status',
    detailFields: 'id, title, chapter_number, content, word_count, status, scenes, character_states, plot_advancement, ai_analysis, created_at, updated_at'
  },
  
  // Characters table
  'characters': {
    commonFields: 'id, name, role, description',
    listFields: 'id, name, role, avatar_url',
    detailFields: 'id, name, role, description, backstory, personality_traits, arc, voice_data, created_at, updated_at'
  },
  
  // Writing sessions
  'writing_sessions': {
    commonFields: 'id, word_count, duration, started_at, ended_at',
    listFields: 'id, word_count, duration, started_at',
    detailFields: 'id, project_id, chapter_id, word_count, duration, started_at, ended_at'
  }
};

async function findSourceFiles(dir: string): Promise<string[]> {
  const files: string[] = [];
  
  async function walk(currentDir: string) {
    const entries = await readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        if (!['node_modules', '.next', 'dist', '.git'].includes(entry.name)) {
          await walk(fullPath);
        }
      } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
        files.push(fullPath);
      }
    }
  }
  
  await walk(dir);
  return files;
}

function analyzeFile(content: string, filePath: string): QueryOptimization[] {
  const optimizations: QueryOptimization[] = [];
  const lines = content.split('\n');
  
  // Check for select * patterns
  const selectAllRegex = /\.from\(['"](\w+)['"]\)\s*\.select\(\s*\)/g;
  let match;
  
  while ((match = selectAllRegex.exec(content)) !== null) {
    const table = match[1];
    const lineNumber = content.substring(0, match.index).split('\n').length;
    const tableOptimization = QUERY_OPTIMIZATIONS[table as keyof typeof QUERY_OPTIMIZATIONS];
    
    if (tableOptimization) {
      optimizations.push({
        file: filePath,
        line: lineNumber,
        original: match[0],
        optimized: `.from('${table}').select('${tableOptimization.commonFields}')`,
        improvement: 'Specify columns instead of selecting all',
        impact: 'medium'
      });
    }
  }
  
  // Check for inefficient count queries
  lines.forEach((line, index) => {
    if (line.includes('.select(') && line.includes('.length')) {
      optimizations.push({
        file: filePath,
        line: index + 1,
        original: line.trim(),
        optimized: 'Use .select("id", { count: "exact", head: true })',
        improvement: 'Use database count instead of fetching all rows',
        impact: 'high'
      });
    }
    
    // Check for missing order in limit queries
    if (line.includes('.limit(') && !lines.slice(Math.max(0, index - 3), index + 3).some(l => l.includes('.order('))) {
      optimizations.push({
        file: filePath,
        line: index + 1,
        original: line.trim(),
        optimized: 'Add .order() before .limit()',
        improvement: 'Always use order with limit for consistent results',
        impact: 'low'
      });
    }
  });
  
  return optimizations;
}

async function generateOptimizedQueries(): Promise<void> {
  const optimizedQueries = `// Optimized Query Patterns for BookScribe
// These patterns reduce data transfer and improve performance

import { createTypedServerClient } from '@/lib/supabase'
import type { Database } from '@/lib/db/types'

// ============================================
// Project Queries
// ============================================

// List projects (optimized for dashboard)
export async function getProjectsList(userId: string) {
  const supabase = await createTypedServerClient()
  
  return supabase
    .from('projects')
    .select('id, title, status, primary_genre, updated_at, total_word_count')
    .eq('user_id', userId)
    .order('updated_at', { ascending: false })
    .limit(20)
}

// Get project details (only needed fields)
export async function getProjectDetails(projectId: string) {
  const supabase = await createTypedServerClient()
  
  return supabase
    .from('projects')
    .select(\`
      id, title, description, status, 
      primary_genre, secondary_genre,
      target_audience, writing_style,
      narrative_voice, setting_time_period,
      setting_location, target_word_count,
      target_chapters, total_word_count,
      project_settings, created_at, updated_at
    \`)
    .eq('id', projectId)
    .single()
}

// Count user projects efficiently
export async function getProjectCount(userId: string) {
  const supabase = await createTypedServerClient()
  
  const { count } = await supabase
    .from('projects')
    .select('id', { count: 'exact', head: true })
    .eq('user_id', userId)
  
  return count || 0
}

// ============================================
// Chapter Queries
// ============================================

// List chapters (minimal data for performance)
export async function getChaptersList(projectId: string) {
  const supabase = await createTypedServerClient()
  
  return supabase
    .from('chapters')
    .select('id, title, chapter_number, word_count, status')
    .eq('project_id', projectId)
    .order('chapter_number')
}

// Get chapter for editing (full content)
export async function getChapterForEdit(chapterId: string) {
  const supabase = await createTypedServerClient()
  
  return supabase
    .from('chapters')
    .select(\`
      id, title, chapter_number, content,
      word_count, status, scenes,
      character_states, plot_advancement,
      ai_analysis, updated_at
    \`)
    .eq('id', chapterId)
    .single()
}

// ============================================
// Character Queries
// ============================================

// List characters (optimized for character panel)
export async function getCharactersList(projectId: string) {
  const supabase = await createTypedServerClient()
  
  return supabase
    .from('characters')
    .select('id, name, role, avatar_url')
    .eq('project_id', projectId)
    .order('created_at')
}

// ============================================
// Analytics Queries
// ============================================

// Get writing statistics efficiently
export async function getWritingStats(userId: string, dateRange: { start: Date, end: Date }) {
  const supabase = await createTypedServerClient()
  
  // Use aggregate functions instead of fetching all data
  const { data } = await supabase
    .from('writing_sessions')
    .select('word_count.sum(), duration.sum(), started_at.count()')
    .eq('user_id', userId)
    .gte('started_at', dateRange.start.toISOString())
    .lte('started_at', dateRange.end.toISOString())
    .single()
  
  return {
    totalWords: data?.word_count || 0,
    totalDuration: data?.duration || 0,
    sessionCount: data?.started_at || 0
  }
}

// ============================================
// Batch Operations
// ============================================

// Fetch multiple related entities efficiently
export async function getProjectWithRelations(projectId: string) {
  const supabase = await createTypedServerClient()
  
  // Single query with joins instead of multiple queries
  return supabase
    .from('projects')
    .select(\`
      id, title, status, total_word_count,
      chapters!inner(
        id, title, chapter_number, word_count, status
      ),
      characters!inner(
        id, name, role
      )
    \`)
    .eq('id', projectId)
    .single()
}

// ============================================
// Search Queries
// ============================================

// Efficient full-text search
export async function searchProjects(userId: string, query: string) {
  const supabase = await createTypedServerClient()
  
  return supabase
    .from('projects')
    .select('id, title, description, primary_genre')
    .eq('user_id', userId)
    .or(\`title.ilike.%\${query}%,description.ilike.%\${query}%\`)
    .limit(10)
}

// ============================================
// Real-time Subscriptions
// ============================================

// Subscribe to specific fields only
export function subscribeToProjectUpdates(projectId: string) {
  const supabase = createTypedBrowserClient()
  
  return supabase
    .channel(\`project:\${projectId}\`)
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'projects',
        filter: \`id=eq.\${projectId}\`
      },
      (payload) => {
        // Only listen for specific field changes
        const { title, status, total_word_count } = payload.new
        // Handle update...
      }
    )
    .subscribe()
}
`;

  const filePath = join(__dirname, '..', 'src', 'lib', 'db', 'optimized-queries.ts');
  await writeFile(filePath, optimizedQueries);
  console.log('Created optimized query patterns at: src/lib/db/optimized-queries.ts');
}

async function generateReport(optimizations: QueryOptimization[]): Promise<void> {
  const reportPath = join(__dirname, '..', 'query-optimization-report.md');
  
  let report = '# Database Query Optimization Report\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  report += `Total optimization opportunities found: ${optimizations.length}\n\n`;
  
  // Group by impact
  const highImpact = optimizations.filter(o => o.impact === 'high');
  const mediumImpact = optimizations.filter(o => o.impact === 'medium');
  const lowImpact = optimizations.filter(o => o.impact === 'low');
  
  report += '## Summary\n\n';
  report += `- **High Impact**: ${highImpact.length} optimizations\n`;
  report += `- **Medium Impact**: ${mediumImpact.length} optimizations\n`;
  report += `- **Low Impact**: ${lowImpact.length} optimizations\n\n`;
  
  // High impact optimizations
  if (highImpact.length > 0) {
    report += '## High Impact Optimizations\n\n';
    highImpact.forEach(opt => {
      report += `### ${opt.file.replace(/\\/g, '/')}:${opt.line}\n`;
      report += `- **Issue**: ${opt.improvement}\n`;
      report += `- **Current**: \`${opt.original}\`\n`;
      report += `- **Optimized**: \`${opt.optimized}\`\n\n`;
    });
  }
  
  // Best practices
  report += '## Query Optimization Best Practices\n\n';
  report += '1. **Always specify columns** - Never use `select()` without columns\n';
  report += '2. **Use count properly** - Use `{ count: "exact", head: true }` for counts\n';
  report += '3. **Limit data transfer** - Only select fields you actually use\n';
  report += '4. **Batch operations** - Use `.in()` instead of loops with queries\n';
  report += '5. **Index usage** - Ensure queries use available indexes\n';
  report += '6. **Connection pooling** - Reuse database connections\n';
  report += '7. **Pagination** - Always use `.order()` with `.limit()`\n';
  report += '8. **Caching** - Cache frequently accessed, rarely changed data\n\n';
  
  report += '## Implementation Guide\n\n';
  report += '1. Review `src/lib/db/optimized-queries.ts` for patterns\n';
  report += '2. Update queries following the optimization patterns\n';
  report += '3. Test query performance with EXPLAIN ANALYZE\n';
  report += '4. Monitor query execution times\n';
  
  await writeFile(reportPath, report);
  console.log(`Report saved to: ${reportPath}`);
}

async function main() {
  console.log('🔍 Analyzing database queries for optimization...\n');
  
  const srcDir = join(__dirname, '..', 'src');
  const files = await findSourceFiles(srcDir);
  
  console.log(`Found ${files.length} source files\n`);
  
  const allOptimizations: QueryOptimization[] = [];
  
  // Analyze files
  for (const file of files) {
    const content = await readFile(file, 'utf-8');
    if (content.includes('supabase') && content.includes('.from(')) {
      const optimizations = analyzeFile(content, file);
      allOptimizations.push(...optimizations);
    }
  }
  
  console.log(`Found ${allOptimizations.length} optimization opportunities\n`);
  
  // Generate optimized query patterns
  await generateOptimizedQueries();
  
  // Generate report
  await generateReport(allOptimizations);
  
  console.log('\n✅ Query optimization analysis complete!');
}

// Run the script
main().catch(console.error);