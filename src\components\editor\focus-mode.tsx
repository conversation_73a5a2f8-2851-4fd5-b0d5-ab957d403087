'use client'

import { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { X } from 'lucide-react'
import { Maximize2 } from 'lucide-react'
import { Minimize2 } from 'lucide-react'
import { Volume2 } from 'lucide-react'
import { VolumeX } from 'lucide-react'
import { Timer } from 'lucide-react'
import { Settings } from 'lucide-react'
import { Coffee } from 'lucide-react'
import { Moon } from 'lucide-react'
import { Cloud } from 'lucide-react'
import { Trees } from 'lucide-react'
import { Waves } from 'lucide-react'
import { Flame } from 'lucide-react'
import { CircleDot } from 'lucide-react'
import { Eye } from 'lucide-react'
import { EyeOff } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FocusModeProps {
  isActive: boolean
  onToggle: () => void
  onExit?: () => void
  children?: React.ReactNode
}

interface AmbientSound {
  id: string
  name: string
  icon: React.ComponentType<{ className?: string }>
  url: string
}

const ambientSounds: AmbientSound[] = [
  { id: 'rain', name: 'Rain', icon: Cloud, url: '/sounds/rain.mp3' },
  { id: 'forest', name: 'Forest', icon: Trees, url: '/sounds/forest.mp3' },
  { id: 'ocean', name: 'Ocean', icon: Waves, url: '/sounds/ocean.mp3' },
  { id: 'fireplace', name: 'Fireplace', icon: Flame, url: '/sounds/fireplace.mp3' },
  { id: 'cafe', name: 'Café', icon: Coffee, url: '/sounds/cafe.mp3' },
  { id: 'night', name: 'Night', icon: Moon, url: '/sounds/night.mp3' },
]

export function FocusMode({ isActive, onToggle, onExit, children }: FocusModeProps) {
  const [showSettings, setShowSettings] = useState(false)
  const [selectedSound, setSelectedSound] = useState<string | null>(null)
  const [volume, setVolume] = useState(50)
  const [showTimer, setShowTimer] = useState(false)
  const [timerMinutes, setTimerMinutes] = useState(25)
  const [timeRemaining, setTimeRemaining] = useState(0)
  const [isTimerActive, setIsTimerActive] = useState(false)
  const [showElements, setShowElements] = useState({
    wordCount: true,
    timer: true,
    settings: true,
  })

  // Timer effect
  useEffect(() => {
    if (!isTimerActive || timeRemaining === 0) return

    const interval = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          setIsTimerActive(false)
          // Play notification sound
          const audio = new Audio('/sounds/bell.mp3')
          audio.play().catch(() => {})
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [isTimerActive, timeRemaining])

  const startTimer = () => {
    setTimeRemaining(timerMinutes * 60)
    setIsTimerActive(true)
    setShowTimer(false)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleKeyPress = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape' && isActive) {
      onExit?.()
    }
  }, [isActive, onExit])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress)
    return () => document.removeEventListener('keydown', handleKeyPress)
  }, [handleKeyPress])

  if (!isActive) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={onToggle}
        className="font-mono"
      >
        <Maximize2 className="w-4 h-4 mr-2" />
        Focus Mode
      </Button>
    )
  }

  return (
    <div className="fixed inset-0 z-50 bg-background">
      {/* Ambient background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/20 opacity-50" />
      
      {/* Top controls bar */}
      <div className={cn(
        "absolute top-0 left-0 right-0 p-4 flex items-center justify-between transition-opacity duration-500",
        showElements.settings ? "opacity-100" : "opacity-0 hover:opacity-100"
      )}>
        <div className="flex items-center gap-4">
          {/* Exit button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={onExit}
            className="font-mono"
          >
            <X className="w-4 h-4" />
          </Button>

          {/* Sound selector */}
          <div className="flex items-center gap-2">
            {selectedSound ? (
              <VolumeX 
                className="w-4 h-4 cursor-pointer text-muted-foreground hover:text-foreground"
                onClick={() => setSelectedSound(null)}
              />
            ) : (
              <Volume2 
                className="w-4 h-4 cursor-pointer text-muted-foreground hover:text-foreground"
                onClick={() => setShowSettings(true)}
              />
            )}
            {ambientSounds.map((sound) => {
              const Icon = sound.icon
              return (
                <Button
                  key={sound.id}
                  variant={selectedSound === sound.id ? 'default' : 'ghost'}
                  size="icon"
                  onClick={() => setSelectedSound(sound.id)}
                  className="font-mono"
                >
                  <Icon className="w-4 h-4" />
                </Button>
              )
            })}
          </div>
        </div>

        <div className="flex items-center gap-4">
          {/* Timer */}
          {showElements.timer && (
            <div className="flex items-center gap-2">
              {isTimerActive ? (
                <div className="font-mono text-sm bg-primary/10 px-3 py-1 rounded-lg">
                  {formatTime(timeRemaining)}
                </div>
              ) : (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowTimer(true)}
                  className="font-mono"
                >
                  <Timer className="w-4 h-4 mr-2" />
                  Timer
                </Button>
              )}
            </div>
          )}

          {/* Settings */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowSettings(true)}
            className="font-mono"
          >
            <Settings className="w-4 h-4" />
          </Button>

          {/* Visibility toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowElements(prev => ({ ...prev, settings: !prev.settings }))}
            className="font-mono"
          >
            {showElements.settings ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {/* Main content area */}
      <div className="h-full flex items-center justify-center p-8 md:p-16 lg:p-24">
        <div className="w-full max-w-4xl">
          {children}
        </div>
      </div>

      {/* Bottom status bar */}
      {showElements.wordCount && (
        <div className="absolute bottom-0 left-0 right-0 p-4 flex justify-center">
          <div className="bg-muted/50 backdrop-blur-sm rounded-lg px-4 py-2 text-mono-sm font-mono text-muted-foreground">
            1,247 words • 5 min read • Chapter 3
          </div>
        </div>
      )}

      {/* Timer Dialog */}
      <Dialog open={showTimer} onOpenChange={setShowTimer}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="font-literary-display text-display-sm">
              Focus Timer
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-mono-sm font-mono">
                Minutes: {timerMinutes}
              </label>
              <Slider
                value={[timerMinutes]}
                onValueChange={([value]) => setTimerMinutes(value)}
                min={5}
                max={120}
                step={5}
                className="w-full"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setTimerMinutes(25)}
                className="font-mono"
              >
                25 min
              </Button>
              <Button
                variant="outline"
                onClick={() => setTimerMinutes(45)}
                className="font-mono"
              >
                45 min
              </Button>
              <Button
                variant="outline"
                onClick={() => setTimerMinutes(60)}
                className="font-mono"
              >
                1 hour
              </Button>
            </div>
            <Button 
              onClick={startTimer} 
              className="w-full font-mono"
            >
              Start Timer
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Settings Dialog */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="font-literary-display text-display-sm">
              Focus Mode Settings
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-mono-sm font-mono">
                Sound Volume
              </label>
              <Slider
                value={[volume]}
                onValueChange={([value]) => setVolume(value)}
                min={0}
                max={100}
                step={10}
                className="w-full"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-mono-sm font-mono">
                Show Elements
              </label>
              <div className="space-y-2">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showElements.wordCount}
                    onChange={(e) => setShowElements(prev => ({ ...prev, wordCount: e.target.checked }))}
                    className="rounded"
                  />
                  <span className="text-mono-sm font-mono">Word count</span>
                </label>
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showElements.timer}
                    onChange={(e) => setShowElements(prev => ({ ...prev, timer: e.target.checked }))}
                    className="rounded"
                  />
                  <span className="text-mono-sm font-mono">Timer</span>
                </label>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}