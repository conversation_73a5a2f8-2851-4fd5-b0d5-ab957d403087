{"summary": {"totalFiles": 111, "totalAnyTypes": 424, "byCategory": [{"category": "Other", "files": 111, "anyTypes": 424}]}, "files": [{"filePath": "lib\\server.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\server.ts", "line": 12, "column": 8, "context": "body?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\server.ts", "line": 48, "column": 40, "context": "export function createMockResponse(data: any, options?: ResponseInit): NextResponse {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\server.ts", "line": 48, "column": 35, "context": "export function createMockResponse(data: any, options?: ResponseInit): NextResponse {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\server.ts", "line": 61, "column": 44, "context": "set(name: string, value: string, options?: any): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\server.ts", "line": 110, "column": 53, "context": "async extractJson(response: NextResponse): Promise<any> {"}], "category": "Other"}, {"filePath": "lib\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\route.ts", "line": 10, "column": 59, "context": "export type RouteHandler = (request: NextRequest, context?: any) => Promise<NextResponse> | NextResponse", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\monaco-workers.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\monaco-workers.ts", "line": 13, "column": 10, "context": ";(self as any).MonacoEnvironment = {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\monaco-workers.ts", "line": 36, "column": 10, "context": ";(self as any).MonacoEnvironment = {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\monaco-workers.ts", "line": 89, "column": 10, "context": ";(self as any).MonacoEnvironment = {"}], "category": "Other"}, {"filePath": "lib\\monaco-theme-generator.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\monaco-theme-generator.ts", "line": 130, "column": 41, "context": "export function updateMonacoTheme(monaco: any, themeName: string = 'bookscribe-dynamic') {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\monaco-theme-generator.ts", "line": 130, "column": 34, "context": "export function updateMonacoTheme(monaco: any, themeName: string = 'bookscribe-dynamic') {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "hooks\\use-timeline-calendar.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\hooks\\use-timeline-calendar.tsx", "line": 41, "column": 77, "context": "const calendarEvents: TimelineEvent[] = (data.events || []).map((event: any) => ({", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\hooks\\use-timeline-calendar.tsx", "line": 41, "column": 71, "context": "const calendarEvents: TimelineEvent[] = (data.events || []).map((event: any) => ({", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "hooks\\use-streaming-ai.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\hooks\\use-streaming-ai.ts", "line": 215, "column": 38, "context": "handleSubmit(new Event('submit') as any, { data: { message } })"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\hooks\\use-streaming-ai.ts", "line": 262, "column": 13, "context": "params: Record<string, any>,", "suggestedFix": "Record<string, unknown>"}], "category": "Other"}, {"filePath": "hooks\\use-first-time-user.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\hooks\\use-first-time-user.ts", "line": 41, "column": 25, "context": "// Check if user has any projects"}], "category": "Other"}, {"filePath": "hooks\\use-collaboration.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\hooks\\use-collaboration.tsx", "line": 103, "column": 38, "context": "presences.forEach((presence: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\hooks\\use-collaboration.tsx", "line": 103, "column": 29, "context": "presences.forEach((presence: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\hooks\\use-collaboration.tsx", "line": 113, "column": 39, "context": "newPresences.forEach((presence: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\hooks\\use-collaboration.tsx", "line": 113, "column": 30, "context": "newPresences.forEach((presence: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\hooks\\use-collaboration.tsx", "line": 126, "column": 40, "context": "leftPresences.forEach((presence: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\hooks\\use-collaboration.tsx", "line": 126, "column": 31, "context": "leftPresences.forEach((presence: any) => {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\validation\\api-schemas.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\validation\\api-schemas.ts", "line": 59, "column": 24, "context": "settings: z.record(z.any()).optional(),", "suggestedFix": "z.unknown()"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\validation\\api-schemas.ts", "line": 90, "column": 24, "context": "settings: z.record(z.any()).optional()", "suggestedFix": "z.unknown()"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\validation\\api-schemas.ts", "line": 370, "column": 23, "context": "options: z.record(z.any()).optional()", "suggestedFix": "z.unknown()"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\validation\\api-schemas.ts", "line": 391, "column": 30, "context": "const schema = (apiSchemas as any)[category]?.[action || operation];"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\validation\\api-schemas.ts", "line": 400, "column": 28, "context": "validateFileUpload: (file: any, allowedTypes: string[], maxSize: number = 50 * 1024 * 1024) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\validation\\api-schemas.ts", "line": 400, "column": 23, "context": "validateFileUpload: (file: any, allowedTypes: string[], maxSize: number = 50 * 1024 * 1024) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\validation\\api-schemas.ts", "line": 416, "column": 30, "context": "validatePagination: (params: any, maxLimit: number = 100) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\validation\\api-schemas.ts", "line": 416, "column": 23, "context": "validatePagination: (params: any, maxLimit: number = 100) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\validation\\api-schemas.ts", "line": 430, "column": 26, "context": "validateSearch: (params: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\validation\\api-schemas.ts", "line": 430, "column": 19, "context": "validateSearch: (params: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\validation\\api-schemas.ts", "line": 434, "column": 25, "context": "filters: z.record(z.any()).optional()", "suggestedFix": "z.unknown()"}], "category": "Other"}, {"filePath": "lib\\supabase\\unified-client.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\supabase\\unified-client.ts", "line": 37, "column": 16, "context": "options: any", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\services\\writing-analytics-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 281, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 459, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 539, "column": 13, "context": "data: Record<string, any>;", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 576, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 635, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 674, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 780, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 821, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1067, "column": 29, "context": "const mode = (session as any).mode || 'Standard Writing';"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1071, "column": 20, "context": "if ((session as any).actions) {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1072, "column": 33, "context": "Object.entries((session as any).actions).forEach(([action, count]) => {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1144, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1257, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1327, "column": 9, "context": "data: any[],", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1327, "column": 11, "context": "data: any[],", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1371, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1401, "column": 21, "context": "const response: any = {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1567, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1658, "column": 55, "context": "private calculateProfilePerformanceMetrics(analytics: any[]): any {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1658, "column": 63, "context": "private calculateProfilePerformanceMetrics(analytics: any[]): any {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1658, "column": 57, "context": "private calculateProfilePerformanceMetrics(analytics: any[]): any {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1792, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1795, "column": 23, "context": "let projectsData: any[] = [];", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1795, "column": 25, "context": "let projectsData: any[] = [];", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1920, "column": 50, "context": "private generateDetailedRecommendations(project: any): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1920, "column": 56, "context": "private generateDetailedRecommendations(project: any): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1920, "column": 58, "context": "private generateDetailedRecommendations(project: any): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1920, "column": 42, "context": "private generateDetailedRecommendations(project: any): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1921, "column": 26, "context": "const recommendations: any[] = [];", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1921, "column": 28, "context": "const recommendations: any[] = [];", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1933, "column": 24, "context": "category.key as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1941, "column": 26, "context": "const severityOrder: any = { critical: 4, high: 3, medium: 2, low: 1 };", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1949, "column": 51, "context": "private generatePriorityRecommendations(projects: any[]): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1949, "column": 59, "context": "private generatePriorityRecommendations(projects: any[]): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1949, "column": 53, "context": "private generatePriorityRecommendations(projects: any[]): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1949, "column": 61, "context": "private generatePriorityRecommendations(projects: any[]): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1950, "column": 29, "context": "const allRecommendations: any[] = [];", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1950, "column": 31, "context": "const allRecommendations: any[] = [];", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1967, "column": 29, "context": "worstCategory.key as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1976, "column": 28, "context": "const severityOrder: any = { critical: 4, high: 3, medium: 2, low: 1 };", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1986, "column": 12, "context": "project: any,", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1989, "column": 4, "context": "): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1989, "column": 6, "context": "): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1992, "column": 26, "context": "const recommendations: any[] = [];", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 1992, "column": 28, "context": "const recommendations: any[] = [];", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2124, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2188, "column": 21, "context": "selectionData?: any;", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2189, "column": 19, "context": "outcomeData?: any;", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2191, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2258, "column": 30, "context": "): Promise<ServiceResponse<any[]>> {", "suggestedFix": "unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2314, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2428, "column": 45, "context": "private calculateSuccessPatterns(completed: any[], abandoned: any[]): any {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2428, "column": 63, "context": "private calculateSuccessPatterns(completed: any[], abandoned: any[]): any {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2428, "column": 71, "context": "private calculateSuccessPatterns(completed: any[], abandoned: any[]): any {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2428, "column": 47, "context": "private calculateSuccessPatterns(completed: any[], abandoned: any[]): any {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2428, "column": 65, "context": "private calculateSuccessPatterns(completed: any[], abandoned: any[]): any {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2430, "column": 27, "context": "genreSuccess: {} as Record<string, any>,", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2431, "column": 31, "context": "structureSuccess: {} as Record<string, any>,", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2432, "column": 26, "context": "paceSuccess: {} as Record<string, any>,", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2433, "column": 37, "context": "targetWordCountSuccess: {} as Record<string, any>,", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2434, "column": 25, "context": "povSuccess: {} as Record<string, any>,", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2435, "column": 26, "context": "toneSuccess: {} as Record<string, any>", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2498, "column": 37, "context": "private incrementPattern(pattern: Record<string, any>, key: string, type: 'completed' | 'abandoned'): void {", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2514, "column": 14, "context": "): Promise<any[]> {", "suggestedFix": "unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2540, "column": 27, "context": "const profileStats: Record<string, any> = {};", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2590, "column": 50, "context": "private generateSuccessRecommendations(patterns: any): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2590, "column": 56, "context": "private generateSuccessRecommendations(patterns: any): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2590, "column": 58, "context": "private generateSuccessRecommendations(patterns: any): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2590, "column": 41, "context": "private generateSuccessRecommendations(patterns: any): any[] {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2591, "column": 26, "context": "const recommendations: any[] = [];", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2591, "column": 28, "context": "const recommendations: any[] = [];", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\writing-analytics-service.ts", "line": 2641, "column": 30, "context": "}): Promise<ServiceResponse<any>> {"}], "category": "Other"}, {"filePath": "lib\\services\\text-extraction-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\text-extraction-service.ts", "line": 60, "column": 48, "context": "private static async extractFromNote(material: any): Promise<ExtractionResult> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\text-extraction-service.ts", "line": 60, "column": 39, "context": "private static async extractFromNote(material: any): Promise<ExtractionResult> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\text-extraction-service.ts", "line": 81, "column": 47, "context": "private static async extractFromUrl(material: any): Promise<ExtractionResult> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\text-extraction-service.ts", "line": 81, "column": 38, "context": "private static async extractFromUrl(material: any): Promise<ExtractionResult> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\text-extraction-service.ts", "line": 103, "column": 52, "context": "private static async extractFromDocument(material: any): Promise<ExtractionResult> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\text-extraction-service.ts", "line": 103, "column": 43, "context": "private static async extractFromDocument(material: any): Promise<ExtractionResult> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\text-extraction-service.ts", "line": 122, "column": 52, "context": "private static async extractFromTextFile(material: any): Promise<ExtractionResult> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\text-extraction-service.ts", "line": 122, "column": 43, "context": "private static async extractFromTextFile(material: any): Promise<ExtractionResult> {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\services\\task-queue-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\task-queue-service.ts", "line": 81, "column": 14, "context": "context: z.any()", "suggestedFix": "z.unknown()"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\task-queue-service.ts", "line": 102, "column": 5, "context": ": Record<string, any>", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\task-queue-service.ts", "line": 107, "column": 7, "context": "data: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\task-queue-service.ts", "line": 115, "column": 10, "context": "result?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\task-queue-service.ts", "line": 126, "column": 13, "context": ") => Promise<any>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\task-queue-service.ts", "line": 130, "column": 51, "context": "private handlers = new Map<TaskType, TaskHandler<any>>()"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\task-queue-service.ts", "line": 408, "column": 21, "context": "const userData: any = {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\services\\streaming-content-generator.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\streaming-content-generator.ts", "line": 222, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\streaming-content-generator.ts", "line": 247, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}], "category": "Other"}, {"filePath": "lib\\services\\stream-data-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\stream-data-service.ts", "line": 191, "column": 45, "context": "const result = await this.generateWithAI<any>(prompt, options)"}], "category": "Other"}, {"filePath": "lib\\services\\selective-subscription-manager.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\selective-subscription-manager.ts", "line": 444, "column": 35, "context": "private emit(event: string, data: any) {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\services\\project-settings-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\project-settings-service.ts", "line": 63, "column": 58, "context": "async upsertProjectSettings(projectId: string, settings: any): Promise<ServiceResponse<ProjectSettings>> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\project-settings-service.ts", "line": 95, "column": 21, "context": "updates: Partial<any>"}], "category": "Other"}, {"filePath": "lib\\services\\project-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\project-service.ts", "line": 306, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}], "category": "Other"}, {"filePath": "lib\\services\\maileroo-email-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\maileroo-email-service.ts", "line": 249, "column": 61, "context": "private generateEmailContent(template: EmailTemplate, data: any): {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\maileroo-email-service.ts", "line": 393, "column": 9, "context": "data: any,", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\services\\export-queue-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\export-queue-service.ts", "line": 12, "column": 11, "context": "options?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\export-queue-service.ts", "line": 54, "column": 13, "context": "options?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\export-queue-service.ts", "line": 147, "column": 38, "context": "private static async processJob(job: any) {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\export-queue-service.ts", "line": 147, "column": 34, "context": "private static async processJob(job: any) {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\export-queue-service.ts", "line": 288, "column": 23, "context": "const updateData: any = {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\services\\embedding-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\embedding-service.ts", "line": 10, "column": 14, "context": "metadata?: Record<string, any>", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\embedding-service.ts", "line": 155, "column": 15, "context": "metadata: Record<string, any>", "suggestedFix": "Record<string, unknown>"}], "category": "Other"}, {"filePath": "lib\\services\\content-indexing-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\content-indexing-service.ts", "line": 11, "column": 13, "context": "metadata: Record<string, any>", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\content-indexing-service.ts", "line": 23, "column": 13, "context": "metadata: Record<string, any>", "suggestedFix": "Record<string, unknown>"}], "category": "Other"}, {"filePath": "lib\\services\\content-generator.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\content-generator.ts", "line": 546, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\content-generator.ts", "line": 587, "column": 29, "context": "): Promise<ServiceResponse<any>> {"}], "category": "Other"}, {"filePath": "lib\\services\\ai-model-selector.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\ai-model-selector.ts", "line": 237, "column": 36, "context": "private selectEmbeddingModel(tier: any, taskType: string): AIModelSelection {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\services\\ai-model-selector.ts", "line": 237, "column": 31, "context": "private selectEmbeddingModel(tier: any, taskType: string): AIModelSelection {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\privacy\\gdpr-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 52, "column": 28, "context": "additionalInfo: z.record(z.any()).optional()", "suggestedFix": "z.unknown()"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 74, "column": 14, "context": "metadata?: Record<string, any>;", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 92, "column": 19, "context": "dataCategories: Record<DataCategory, any>;"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 229, "column": 52, "context": "private static async processAccessRequest(request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 229, "column": 44, "context": "private static async processAccessRequest(request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 253, "column": 53, "context": "private static async processErasureRequest(request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 253, "column": 45, "context": "private static async processErasureRequest(request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 281, "column": 14, "context": "): Promise<Record<DataCategory, any>> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 283, "column": 17, "context": "const data: Record<string, any> = {};", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 430, "column": 20, "context": "return data as Record<DataCategory, any>;"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 618, "column": 60, "context": "private static async archiveUserData(userId: string, data: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 637, "column": 9, "context": "data: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 688, "column": 42, "context": "private static sanitizeBillingData(data: any): any {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 688, "column": 48, "context": "private static sanitizeBillingData(data: any): any {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 688, "column": 37, "context": "private static sanitizeBillingData(data: any): any {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 710, "column": 71, "context": "private static async sendRequestConfirmation(userId: string, request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 743, "column": 59, "context": "private static async processRectificationRequest(request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 743, "column": 51, "context": "private static async processRectificationRequest(request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 751, "column": 57, "context": "private static async processPortabilityRequest(request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 751, "column": 49, "context": "private static async processPortabilityRequest(request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 771, "column": 56, "context": "private static async processConsentWithdrawal(request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 771, "column": 48, "context": "private static async processConsentWithdrawal(request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 790, "column": 57, "context": "private static async processRestrictionRequest(request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 790, "column": 49, "context": "private static async processRestrictionRequest(request: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 810, "column": 46, "context": "private static convertToPortableFormat(data: any): any {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 810, "column": 52, "context": "private static convertToPortableFormat(data: any): any {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 810, "column": 41, "context": "private static convertToPortableFormat(data: any): any {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 818, "column": 62, "context": "owns: data[DataCategory.CONTENT_DATA]?.projects?.map((p: any) => ({", "suggestedFix": "Project"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 818, "column": 60, "context": "owns: data[DataCategory.CONTENT_DATA]?.projects?.map((p: any) => ({", "suggestedFix": "Project"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 830, "column": 40, "context": "private static formatGDPRRequest(data: any): GDPRRequest {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\privacy\\gdpr-service.ts", "line": 830, "column": 35, "context": "private static formatGDPRRequest(data: any): GDPRRequest {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\monitoring\\sentry.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\monitoring\\sentry.tsx", "line": 94, "column": 36, "context": "const errorCode = (error as any).code;"}], "category": "Other"}, {"filePath": "lib\\panels\\panel-wrapper.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-wrapper.tsx", "line": 69, "column": 46, "context": "const handleAction = (action: string, data?: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-wrapper.tsx", "line": 85, "column": 43, "context": "const handleSettingsChange = (settings: Record<string, any>) => {", "suggestedFix": "Record<string, unknown>"}], "category": "Other"}, {"filePath": "lib\\panels\\panel-registry.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-registry.tsx", "line": 195, "column": 27, "context": "chapters: Chapters<PERSON>ane<PERSON> as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-registry.tsx", "line": 196, "column": 29, "context": "knowledge: KnowledgePanel as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-registry.tsx", "line": 197, "column": 26, "context": "'ai-chat': AIChatPanel as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-registry.tsx", "line": 198, "column": 34, "context": "'story-bible': StoryBiblePanel as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-registry.tsx", "line": 199, "column": 40, "context": "'character-arcs': Character<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-registry.tsx", "line": 200, "column": 40, "context": "'plot-structure': PlotStructurePanel as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-registry.tsx", "line": 201, "column": 40, "context": "'voice-analysis': VoiceAnalysisPanel as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-registry.tsx", "line": 202, "column": 46, "context": "'voice-consistency': VoiceConsistencyPanel as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-registry.tsx", "line": 203, "column": 42, "context": "'version-history': VersionHistoryPanel as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-registry.tsx", "line": 204, "column": 38, "context": "'writing-stats': WritingStatsPanel as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-registry.tsx", "line": 205, "column": 36, "context": "'series-tools': SeriesToolsPanel as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\panels\\panel-registry.tsx", "line": 206, "column": 33, "context": "consistency: ConsistencyPanel as any"}], "category": "Other"}, {"filePath": "lib\\memory\\types.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\memory\\types.ts", "line": 38, "column": 13, "context": "metrics?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\memory\\types.ts", "line": 83, "column": 14, "context": "metadata?: Record<string, any>", "suggestedFix": "Record<string, unknown>"}], "category": "Other"}, {"filePath": "lib\\memory\\memory-manager.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\memory\\memory-manager.ts", "line": 9, "column": 51, "context": "private contextCache = new Map<string, { content: any; timestamp: number }>()", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\memory\\memory-manager.ts", "line": 80, "column": 74, "context": "async getRelevantContext(query: string, chapterNumber: number): Promise<any> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\memory\\memory-manager.ts", "line": 105, "column": 29, "context": "async addMemoryChunk(chunk: any): Promise<string> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\memory\\memory-manager.ts", "line": 105, "column": 23, "context": "async addMemoryChunk(chunk: any): Promise<string> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\memory\\memory-manager.ts", "line": 112, "column": 46, "context": "async updateMemoryChunk(id: string, updates: any): Promise<boolean> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\memory\\memory-manager.ts", "line": 390, "column": 37, "context": "private async mergeEntries(entries: any[]): Promise<any> {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\memory\\memory-manager.ts", "line": 390, "column": 39, "context": "private async mergeEntries(entries: any[]): Promise<any> {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\memory\\memory-manager.ts", "line": 390, "column": 54, "context": "private async mergeEntries(entries: any[]): Promise<any> {", "suggestedFix": ": unknown[]"}], "category": "Other"}, {"filePath": "lib\\memory\\context-compression-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\memory\\context-compression-service.ts", "line": 9, "column": 13, "context": "metadata: Record<string, any>", "suggestedFix": "Record<string, unknown>"}], "category": "Other"}, {"filePath": "lib\\memory\\auto-optimization-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\memory\\auto-optimization-service.ts", "line": 322, "column": 72, "context": "async getOptimizationHistory(projectId: string, limit = 10): Promise<any[]> {", "suggestedFix": "unknown[]"}], "category": "Other"}, {"filePath": "lib\\export\\export-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\export\\export-service.ts", "line": 1437, "column": 38, "context": "static async exportToTxt(exportData: any): Promise<Blob> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\export\\export-service.ts", "line": 1437, "column": 27, "context": "static async exportToTxt(exportData: any): Promise<Blob> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\export\\export-service.ts", "line": 1459, "column": 43, "context": "static async exportToMarkdown(exportData: any): Promise<Blob> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\export\\export-service.ts", "line": 1459, "column": 32, "context": "static async exportToMarkdown(exportData: any): Promise<Blob> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\export\\export-service.ts", "line": 1481, "column": 39, "context": "static async exportToDocx(exportData: any): Promise<Blob> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\export\\export-service.ts", "line": 1481, "column": 28, "context": "static async exportToDocx(exportData: any): Promise<Blob> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\export\\export-service.ts", "line": 1514, "column": 38, "context": "static async exportToPdf(exportData: any): Promise<Blob> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\export\\export-service.ts", "line": 1514, "column": 27, "context": "static async exportToPdf(exportData: any): Promise<Blob> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\export\\export-service.ts", "line": 1548, "column": 39, "context": "static async exportToEpub(exportData: any): Promise<Blob> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\export\\export-service.ts", "line": 1548, "column": 28, "context": "static async exportToEpub(exportData: any): Promise<Blob> {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\email\\types.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\types.ts", "line": 16, "column": 14, "context": "metadata?: Record<string, any>", "suggestedFix": "Record<string, unknown>"}], "category": "Other"}, {"filePath": "lib\\email\\service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\service.ts", "line": 270, "column": 53, "context": "async getEmailPreferences(userId: string): Promise<any> {"}], "category": "Other"}, {"filePath": "lib\\config\\error-messages.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\config\\error-messages.ts", "line": 253, "column": 32, "context": "errorObj[key] = (error as any)[key];"}], "category": "Other"}, {"filePath": "lib\\config\\animation-timing.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\config\\animation-timing.ts", "line": 89, "column": 32, "context": "debounce: <T extends (...args: any[]) => any>(", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\config\\animation-timing.ts", "line": 89, "column": 34, "context": "debounce: <T extends (...args: any[]) => any>(", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\config\\animation-timing.ts", "line": 101, "column": 32, "context": "throttle: <T extends (...args: any[]) => any>(", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\config\\animation-timing.ts", "line": 101, "column": 34, "context": "throttle: <T extends (...args: any[]) => any>(", "suggestedFix": ": unknown[]"}], "category": "Other"}, {"filePath": "lib\\collaboration\\websocket-client.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\websocket-client.ts", "line": 8, "column": 10, "context": "payload: any;", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\websocket-client.ts", "line": 16, "column": 7, "context": "data: any;", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\websocket-client.ts", "line": 150, "column": 29, "context": "send(type: string, payload: any): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\websocket-client.ts", "line": 179, "column": 35, "context": "sendSelection(selection: { start: any; end: any }): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\websocket-client.ts", "line": 179, "column": 45, "context": "sendSelection(selection: { start: any; end: any }): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\websocket-client.ts", "line": 188, "column": 13, "context": "position: any;", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\websocket-client.ts", "line": 200, "column": 12, "context": "cursor?: any;", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\websocket-client.ts", "line": 201, "column": 15, "context": "selection?: any;", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\websocket-client.ts", "line": 291, "column": 45, "context": "private handleError(context: string, error: any): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\websocket-client.ts", "line": 303, "column": 22, "context": "private log(...args: any[]): void {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\websocket-client.ts", "line": 303, "column": 24, "context": "private log(...args: any[]): void {", "suggestedFix": ": unknown[]"}], "category": "Other"}, {"filePath": "lib\\collaboration\\collaboration-manager.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 17, "column": 22, "context": "selection?: { start: any; end: any };", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 17, "column": 32, "context": "selection?: { start: any; end: any };", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 158, "column": 37, "context": "updateSelection(selection: { start: any; end: any }): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 158, "column": 47, "context": "updateSelection(selection: { start: any; end: any }): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 265, "column": 52, "context": "private handleRemoteCursor(userId: string, cursor: any): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 276, "column": 58, "context": "private handleRemoteSelection(userId: string, selection: any): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 287, "column": 36, "context": "private handlePresenceUpdate(data: any): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 287, "column": 31, "context": "private handlePresenceUpdate(data: any): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 319, "column": 26, "context": "private handleSync(data: any): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 319, "column": 21, "context": "private handleSync(data: any): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 336, "column": 36, "context": "data.collaborators.forEach((c: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 336, "column": 34, "context": "data.collaborators.forEach((c: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 356, "column": 32, "context": "private handleMessage(message: any): void {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\collaboration\\collaboration-manager.ts", "line": 356, "column": 24, "context": "private handleMessage(message: any): void {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\api\\with-api-route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\with-api-route.ts", "line": 19, "column": 29, "context": "validateBody?: <PERSON><PERSON><any>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\with-api-route.ts", "line": 27, "column": 12, "context": "params?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\with-api-route.ts", "line": 46, "column": 24, "context": "context?: { params?: any }", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\api\\unified-response.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\unified-response.ts", "line": 353, "column": 22, "context": "typeof (response as any).success === 'boolean'"}], "category": "Other"}, {"filePath": "lib\\api\\unified-middleware.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\unified-middleware.ts", "line": 55, "column": 17, "context": "validatedBody?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\unified-middleware.ts", "line": 56, "column": 18, "context": "validatedQuery?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\unified-middleware.ts", "line": 57, "column": 19, "context": "validatedParams?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\unified-middleware.ts", "line": 121, "column": 8, "context": "body?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\unified-middleware.ts", "line": 122, "column": 9, "context": "query?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\unified-middleware.ts", "line": 123, "column": 10, "context": "params?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\unified-middleware.ts", "line": 125, "column": 16, "context": "const results: any = {}", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\unified-middleware.ts", "line": 170, "column": 51, "context": "handler: (request: EnhancedRequest & T, context?: any) => Promise<NextResponse>,", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\unified-middleware.ts", "line": 175, "column": 47, "context": "return async (request: NextRequest, context?: any): Promise<NextResponse> => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\unified-middleware.ts", "line": 373, "column": 56, "context": "project: (handler: (request: EnhancedRequest, context: any) => Promise<NextResponse>) =>", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\unified-middleware.ts", "line": 380, "column": 55, "context": "series: (handler: (request: EnhancedRequest, context: any) => Promise<NextResponse>) =>", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\api\\service-layer-guide.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\service-layer-guide.ts", "line": 61, "column": 61, "context": "export async function getAnalytics(userId: string, dateRange: any) {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\service-layer-guide.ts", "line": 73, "column": 59, "context": "export async function performSearch(query: string, filters: any) {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\api\\request-validation-middleware.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 62, "column": 8, "context": "body?: any;", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 63, "column": 9, "context": "query?: any;", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 64, "column": 11, "context": "headers?: any;", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 65, "column": 10, "context": "params?: any;", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 66, "column": 8, "context": "user?: any;", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 90, "column": 37, "context": "type: config.rateLimitKey as any || 'api',"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 378, "column": 47, "context": "private static containsMaliciousContent(data: any): boolean {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 378, "column": 42, "context": "private static containsMaliciousContent(data: any): boolean {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 444, "column": 47, "context": "private static detectSuspiciousPatterns(data: any): string[] {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 444, "column": 42, "context": "private static detectSuspiciousPatterns(data: any): string[] {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 494, "column": 52, "context": "private static formatZodErrors(error: z.<PERSON>od<PERSON>rror): any {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 526, "column": 14, "context": "details: Record<string, any>", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 552, "column": 11, "context": "target: any,", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 558, "column": 69, "context": "descriptor.value = async function (request: NextRequest, ...args: any[]) {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\request-validation-middleware.ts", "line": 558, "column": 71, "context": "descriptor.value = async function (request: NextRequest, ...args: any[]) {", "suggestedFix": ": unknown[]"}], "category": "Other"}, {"filePath": "lib\\api\\enhanced-middleware.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\enhanced-middleware.ts", "line": 52, "column": 43, "context": "handler: (request: NextRequest, context?: any) => Promise<NextResponse>,", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\enhanced-middleware.ts", "line": 57, "column": 47, "context": "return async (request: NextRequest, context?: any): Promise<NextResponse> => {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\api\\admin-security-middleware.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\admin-security-middleware.ts", "line": 38, "column": 20, "context": "): Promise<{ user: any; adminClient: any } | NextResponse> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\admin-security-middleware.ts", "line": 38, "column": 38, "context": "): Promise<{ user: any; adminClient: any } | NextResponse> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\admin-security-middleware.ts", "line": 331, "column": 52, "context": "static async detectSuspiciousActivity(): Promise<any[]> {", "suggestedFix": "unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\admin-security-middleware.ts", "line": 410, "column": 11, "context": "target: any,", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\admin-security-middleware.ts", "line": 416, "column": 69, "context": "descriptor.value = async function (request: NextRequest, ...args: any[]) {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\api\\admin-security-middleware.ts", "line": 416, "column": 71, "context": "descriptor.value = async function (request: NextRequest, ...args: any[]) {", "suggestedFix": ": unknown[]"}], "category": "Other"}, {"filePath": "lib\\auth\\unified-auth-service.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\auth\\unified-auth-service.ts", "line": 221, "column": 53, "context": "handler: (request: AuthenticatedRequest, context: any) => Promise<NextResponse>", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\auth\\unified-auth-service.ts", "line": 254, "column": 53, "context": "handler: (request: AuthenticatedRequest, context: any) => Promise<NextResponse>", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\ai\\vercel-ai-client.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\ai\\vercel-ai-client.ts", "line": 619, "column": 47, "context": "const openaiProvider = providers.openai as any"}], "category": "Other"}, {"filePath": "lib\\accessibility\\aria-labels.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\accessibility\\aria-labels.ts", "line": 190, "column": 14, "context": "let current: any = ARIA_LABELS;", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\agents\\voice-aware-writing-agent.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\agents\\voice-aware-writing-agent.ts", "line": 125, "column": 43, "context": "const patterns = profile.patterns as any;"}], "category": "Other"}, {"filePath": "components\\wizard\\demo-to-live-wizard.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\wizard\\demo-to-live-wizard.tsx", "line": 34, "column": 21, "context": "onComplete?: (data: any) => void", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\wizard\\demo-to-live-wizard.tsx", "line": 34, "column": 16, "context": "onComplete?: (data: any) => void", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\wizard\\demo-to-live-wizard.tsx", "line": 62, "column": 59, "context": "const [generatedContent, setGeneratedContent] = useState<any>(null)"}], "category": "Other"}, {"filePath": "components\\voice\\voice-trainer.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\voice\\voice-trainer.tsx", "line": 270, "column": 68, "context": "<Select value={profileType} onValueChange={(value: any) => setProfileType(value)}>", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\voice\\voice-trainer.tsx", "line": 270, "column": 62, "context": "<Select value={profileType} onValueChange={(value: any) => setProfileType(value)}>", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\voice\\voice-trainer-enhanced.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\voice\\voice-trainer-enhanced.tsx", "line": 386, "column": 68, "context": "<Select value={profileType} onValueChange={(value: any) => setProfileType(value)}>", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\voice\\voice-trainer-enhanced.tsx", "line": 386, "column": 62, "context": "<Select value={profileType} onValueChange={(value: any) => setProfileType(value)}>", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\voice\\voice-profile-templates.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\voice\\voice-profile-templates.tsx", "line": 81, "column": 42, "context": "function convertToMetrics(characteristics: any) {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\voice\\voice-profile-templates.tsx", "line": 81, "column": 26, "context": "function convertToMetrics(characteristics: any) {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\voice\\voice-profile-creation-dialog.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\voice\\voice-profile-creation-dialog.tsx", "line": 51, "column": 55, "context": "const [analysisResult, setAnalysisResult] = useState<any>(null)"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\voice\\voice-profile-creation-dialog.tsx", "line": 212, "column": 72, "context": "<Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)}>"}], "category": "Other"}, {"filePath": "components\\universe\\universe-map.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\universe\\universe-map.tsx", "line": 41, "column": 10, "context": "series?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\universe\\universe-map.tsx", "line": 42, "column": 8, "context": "book?: any", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\universe\\universe-manager.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\universe\\universe-manager.tsx", "line": 302, "column": 35, "context": "return rules.map((rule: any) => ({", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\universe\\universe-manager.tsx", "line": 302, "column": 30, "context": "return rules.map((rule: any) => ({", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\universe\\universe-manager.tsx", "line": 397, "column": 31, "context": "}, {} as Record<string, any[]>)", "suggestedFix": "unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\universe\\universe-manager.tsx", "line": 892, "column": 106, "context": "onValueChange={(value) => setNewTimelineEvent({ ...newTimelineEvent, event_type: value as any })}"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\universe\\universe-manager.tsx", "line": 911, "column": 106, "context": "onValueChange={(value) => setNewTimelineEvent({ ...newTimelineEvent, importance: value as any })}"}], "category": "Other"}, {"filePath": "components\\universe\\create-universe-dialog.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\universe\\create-universe-dialog.tsx", "line": 85, "column": 51, "context": "const [universeData, setUniverseData] = useState<any>({"}], "category": "Other"}, {"filePath": "components\\timeline\\timeline-view.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\timeline\\timeline-view.tsx", "line": 144, "column": 45, "context": "const handleEventCreate = async (eventData: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\timeline\\timeline-view.tsx", "line": 144, "column": 35, "context": "const handleEventCreate = async (eventData: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\timeline\\timeline-view.tsx", "line": 172, "column": 41, "context": "const handleEventUpdate = async (event: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\timeline\\timeline-view.tsx", "line": 172, "column": 35, "context": "const handleEventUpdate = async (event: any) => {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\timeline\\timeline-calendar-view.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\timeline\\timeline-calendar-view.tsx", "line": 234, "column": 92, "context": "<Select value={filterType} onValueChange={(value) => setFilterType(value as any)}>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\timeline\\timeline-calendar-view.tsx", "line": 252, "column": 104, "context": "<Select value={filterImportance} onValueChange={(value) => setFilterImportance(value as any)}>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\timeline\\timeline-calendar-view.tsx", "line": 270, "column": 80, "context": "<Select value={viewMode} onValueChange={(value) => setViewMode(value as any)}>"}], "category": "Other"}, {"filePath": "components\\tasks\\task-queue.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\tasks\\task-queue.tsx", "line": 125, "column": 42, "context": "<Badge variant={variants[priority] as any}>"}], "category": "Other"}, {"filePath": "components\\tasks\\task-progress-card.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\tasks\\task-progress-card.tsx", "line": 18, "column": 23, "context": "onComplete?: (result: any) => void", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\tasks\\task-progress-card.tsx", "line": 18, "column": 16, "context": "onComplete?: (result: any) => void", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\tasks\\task-progress-card.tsx", "line": 23, "column": 35, "context": "const [task, setTask] = useState<any>(null)"}], "category": "Other"}, {"filePath": "components\\story-bible\\story-bible-navigator.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\story-bible\\story-bible-navigator.tsx", "line": 30, "column": 39, "context": "onNavigate?: (section: string, item?: any) => void", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\story-bible\\story-bible-navigator.tsx", "line": 57, "column": 49, "context": "const handleNavigate = (section: string, item?: any) => {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\story-bible\\story-bible-explorer.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\story-bible\\story-bible-explorer.tsx", "line": 82, "column": 31, "context": "onEdit?: (type: string, item: any) => void", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\story-bible\\story-bible-explorer.tsx", "line": 327, "column": 90, "context": "<Tabs value={activeCategory} onValueChange={(value) => setActiveCategory(value as any)}>"}], "category": "Other"}, {"filePath": "components\\story-bible\\ai-story-bible-assistant.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\story-bible\\ai-story-bible-assistant.tsx", "line": 57, "column": 8, "context": "data?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\story-bible\\ai-story-bible-assistant.tsx", "line": 69, "column": 27, "context": "onUpdateBible?: (updates: any) => void", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\story-bible\\ai-story-bible-assistant.tsx", "line": 69, "column": 19, "context": "onUpdateBible?: (updates: any) => void", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\story-bible\\ai-story-bible-assistant.tsx", "line": 84, "column": 51, "context": "const [analysisData, setAnalysisData] = useState<any>(null)"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\story-bible\\ai-story-bible-assistant.tsx", "line": 660, "column": 55, "context": "{analysisData.insights?.map((insight: any, index: number) => (", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\story-bible\\ai-story-bible-assistant.tsx", "line": 660, "column": 47, "context": "{analysisData.insights?.map((insight: any, index: number) => (", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\search\\semantic-search-interface.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\search\\semantic-search-interface.tsx", "line": 83, "column": 85, "context": "const transformedResults: SearchResult[] = (data.data.results || []).map((item: any) => ({", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\search\\semantic-search-interface.tsx", "line": 83, "column": 80, "context": "const transformedResults: SearchResult[] = (data.data.results || []).map((item: any) => ({", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\search\\content-search-interface.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\search\\content-search-interface.tsx", "line": 306, "column": 36, "context": "onValueChange={(value: any) => setFilters(prev => ({ ...prev, dateRange: value }))}", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\search\\content-search-interface.tsx", "line": 306, "column": 30, "context": "onValueChange={(value: any) => setFilters(prev => ({ ...prev, dateRange: value }))}", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\search\\content-search-interface.tsx", "line": 325, "column": 36, "context": "onValueChange={(value: any) => setFilters(prev => ({ ...prev, sortBy: value }))}", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\search\\content-search-interface.tsx", "line": 325, "column": 30, "context": "onValueChange={(value: any) => setFilters(prev => ({ ...prev, sortBy: value }))}", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\notifications\\notification-panel.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\notifications\\notification-panel.tsx", "line": 20, "column": 8, "context": "data?: any", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\notifications\\notification-card.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\notifications\\notification-card.tsx", "line": 25, "column": 10, "context": "data?: any", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\optimized\\memoized-list-item.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\optimized\\memoized-list-item.tsx", "line": 81, "column": 23, "context": "onClick?: (project: any) => void", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\optimized\\memoized-list-item.tsx", "line": 81, "column": 15, "context": "onClick?: (project: any) => void", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\memory\\memory-usage-chart.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\memory\\memory-usage-chart.tsx", "line": 118, "column": 52, "context": "const CustomTooltip = ({ active, payload, label }: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\memory\\memory-usage-chart.tsx", "line": 124, "column": 32, "context": "{payload.map((entry: any, index: number) => (", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\memory\\memory-usage-chart.tsx", "line": 124, "column": 26, "context": "{payload.map((entry: any, index: number) => (", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\locations\\location-manager.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\locations\\location-manager.tsx", "line": 124, "column": 51, "context": "const handleCreateLocation = async (locationData: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\locations\\location-manager.tsx", "line": 124, "column": 38, "context": "const handleCreateLocation = async (locationData: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\locations\\location-manager.tsx", "line": 341, "column": 59, "context": "onChange={(e) => setFilterType(e.target.value as any)}"}], "category": "Other"}, {"filePath": "components\\locations\\create-location-dialog.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\locations\\create-location-dialog.tsx", "line": 20, "column": 34, "context": "onLocationCreate: (locationData: any) => Promise<void>", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\locations\\create-location-dialog.tsx", "line": 20, "column": 21, "context": "onLocationCreate: (locationData: any) => Promise<void>", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\goals\\writing-goals-dashboard.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\goals\\writing-goals-dashboard.tsx", "line": 87, "column": 43, "context": "const handleCreateGoal = async (goalData: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\goals\\writing-goals-dashboard.tsx", "line": 87, "column": 34, "context": "const handleCreateGoal = async (goalData: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\goals\\writing-goals-dashboard.tsx", "line": 114, "column": 58, "context": "const handleUpdateGoal = async (goalId: string, updates: any) => {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\goals\\create-goal-dialog.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\goals\\create-goal-dialog.tsx", "line": 18, "column": 18, "context": "onSubmit: (data: any) => void", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\goals\\create-goal-dialog.tsx", "line": 18, "column": 13, "context": "onSubmit: (data: any) => void", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\goals\\create-goal-dialog.tsx", "line": 19, "column": 15, "context": "initialData?: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\goals\\create-goal-dialog.tsx", "line": 32, "column": 44, "context": "const [projects, setProjects] = useState<any[]>([])", "suggestedFix": "unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\goals\\create-goal-dialog.tsx", "line": 63, "column": 15, "context": "const data: any = {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\error\\unified-error-system.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\error\\unified-error-system.tsx", "line": 29, "column": 13, "context": "context?: Record<string, any>", "suggestedFix": "Record<string, unknown>"}], "category": "Other"}, {"filePath": "components\\editor\\voice-analysis-panel.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\editor\\voice-analysis-panel.tsx", "line": 209, "column": 34, "context": "const getMatchSeverity = (match: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\editor\\voice-analysis-panel.tsx", "line": 209, "column": 28, "context": "const getMatchSeverity = (match: any) => {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\editor\\voice-analysis-panel-enhanced.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\editor\\voice-analysis-panel-enhanced.tsx", "line": 41, "column": 11, "context": "patterns: any", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\editor\\voice-analysis-panel-enhanced.tsx", "line": 73, "column": 52, "context": "const [voiceMatches, setVoiceMatches] = useState<any[]>([])", "suggestedFix": "unknown[]"}], "category": "Other"}, {"filePath": "components\\editor\\panel-layout.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\editor\\panel-layout.tsx", "line": 15, "column": 14, "context": "metadata?: Record<string, any>", "suggestedFix": "Record<string, unknown>"}], "category": "Other"}, {"filePath": "components\\editor\\consistency-checker.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\editor\\consistency-checker.tsx", "line": 246, "column": 86, "context": "<Tabs value={selectedType} onValueChange={(v) => setSelectedType(v as any)}>"}], "category": "Other"}, {"filePath": "components\\editor\\collaborative-editor-wrapper.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\editor\\collaborative-editor-wrapper.tsx", "line": 124, "column": 74, "context": "const handleEditorMount = (editor: editor.IStandaloneCodeEditor, monaco: any) => {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\dashboard\\quick-actions-widget.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\dashboard\\quick-actions-widget.tsx", "line": 151, "column": 62, "context": "export function RecentProjectsWidget({ projects }: { projects: any[] }) {", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\dashboard\\quick-actions-widget.tsx", "line": 151, "column": 64, "context": "export function RecentProjectsWidget({ projects }: { projects: any[] }) {", "suggestedFix": ": unknown[]"}], "category": "Other"}, {"filePath": "components\\analysis\\character-insights-panel.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\analysis\\character-insights-panel.tsx", "line": 97, "column": 48, "context": "const [characters, setCharacters] = useState<any[]>([])", "suggestedFix": "unknown[]"}], "category": "Other"}, {"filePath": "components\\analysis\\character-arc-timeline.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\analysis\\character-arc-timeline.tsx", "line": 101, "column": 48, "context": "const [characters, setCharacters] = useState<any[]>([])", "suggestedFix": "unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\analysis\\character-arc-timeline.tsx", "line": 189, "column": 82, "context": "<Select value={timelineScale} onValueChange={(v) => setTimelineScale(v as any)}>"}], "category": "Other"}, {"filePath": "components\\ai\\streaming-writing-assistant.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\ai\\streaming-writing-assistant.tsx", "line": 242, "column": 64, "context": "onClick={() => setSelectedModel(model.id as any)}"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\ai\\streaming-writing-assistant.tsx", "line": 433, "column": 40, "context": "handleChatSubmit(e as any)"}], "category": "Other"}, {"filePath": "components\\ai\\ai-streaming-demo.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\ai\\ai-streaming-demo.tsx", "line": 116, "column": 73, "context": "const getStatusIcon = (isLoading: boolean, isStreaming: boolean, error: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\ai\\ai-streaming-demo.tsx", "line": 197, "column": 64, "context": "onClick={() => setSelectedModel(model.id as any)}"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\ai\\ai-streaming-demo.tsx", "line": 389, "column": 51, "context": "contentType: e.target.value as any"}], "category": "Other"}, {"filePath": "components\\agents\\agent-performance-metrics.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\agents\\agent-performance-metrics.tsx", "line": 43, "column": 56, "context": "const [timeSeriesData, setTimeSeriesData] = useState<any[]>([])", "suggestedFix": "unknown[]"}], "category": "Other"}, {"filePath": "components\\achievements\\achievement-unlock-modal.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\achievements\\achievement-unlock-modal.tsx", "line": 48, "column": 21, "context": "const interval: any = setInterval(function() {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\achievements\\achievement-card.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\achievements\\achievement-card.tsx", "line": 22, "column": 11, "context": "criteria: any", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\email\\queue\\email-queue.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\queue\\email-queue.ts", "line": 20, "column": 67, "context": "private readonly sendEmail: (request: EmailRequest) => Promise<any>,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\queue\\email-queue.ts", "line": 62, "column": 29, "context": "user_id: (request as any).userId // If tracking by user"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\queue\\email-queue.ts", "line": 120, "column": 33, "context": "private async processItem(item: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\queue\\email-queue.ts", "line": 120, "column": 28, "context": "private async processItem(item: any): Promise<void> {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\queue\\email-queue.ts", "line": 328, "column": 30, "context": "private mapToQueueItem(data: any): EmailQueueItem {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\queue\\email-queue.ts", "line": 328, "column": 25, "context": "private mapToQueueItem(data: any): EmailQueueItem {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "lib\\email\\providers\\sendgrid-provider.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\providers\\sendgrid-provider.ts", "line": 112, "column": 50, "context": "errorMessage = errorData.errors?.map((e: any) => e.message).join(', ') || errorMessage", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\providers\\sendgrid-provider.ts", "line": 112, "column": 48, "context": "errorMessage = errorData.errors?.map((e: any) => e.message).join(', ') || errorMessage", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\providers\\sendgrid-provider.ts", "line": 159, "column": 19, "context": "return null as any"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\providers\\sendgrid-provider.ts", "line": 162, "column": 19, "context": "return null as any"}], "category": "Other"}, {"filePath": "lib\\email\\providers\\base-provider.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\providers\\base-provider.ts", "line": 59, "column": 46, "context": "async getStatus(messageId: string): Promise<any> {"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\providers\\base-provider.ts", "line": 171, "column": 40, "context": "protected formatAttachment(attachment: any): any {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\providers\\base-provider.ts", "line": 171, "column": 46, "context": "protected formatAttachment(attachment: any): any {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\lib\\email\\providers\\base-provider.ts", "line": 171, "column": 29, "context": "protected formatAttachment(attachment: any): any {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "components\\analytics\\components\\writing-calendar.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\analytics\\components\\writing-calendar.tsx", "line": 128, "column": 38, "context": "data.sessions?.forEach((session: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\analytics\\components\\writing-calendar.tsx", "line": 128, "column": 30, "context": "data.sessions?.forEach((session: any) => {", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\analytics\\components\\writing-calendar.tsx", "line": 428, "column": 70, "context": "<Tabs value={viewMode} onValueChange={(v) => setViewMode(v as any)}>"}], "category": "Other"}, {"filePath": "components\\analytics\\components\\productivity-metrics.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\components\\analytics\\components\\productivity-metrics.tsx", "line": 184, "column": 67, "context": "style={{ '--progress-color': productivityScoreColor } as any}"}], "category": "Other"}, {"filePath": "app\\api\\universes\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\universes\\route.ts", "line": 49, "column": 23, "context": "const universeData: any = {", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "app\\api\\project-collaborators\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\project-collaborators\\route.ts", "line": 470, "column": 65, "context": "const { collaboration, isOwner, isSelf } = context.customData as any;"}], "category": "Other"}, {"filePath": "app\\(dashboard)\\productivity\\page-client.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\(dashboard)\\productivity\\page-client.tsx", "line": 103, "column": 56, "context": "<Select value={timeframe} onValueChange={(value: any) => setTimeframe(value)}>", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\(dashboard)\\productivity\\page-client.tsx", "line": 103, "column": 50, "context": "<Select value={timeframe} onValueChange={(value: any) => setTimeframe(value)}>", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "app\\(dashboard)\\memory\\page.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\(dashboard)\\memory\\page.tsx", "line": 36, "column": 74, "context": "const projectMemoryStats: ProjectMemoryStats[] = projects?.map((project: any) => ({", "suggestedFix": "unknown"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\(dashboard)\\memory\\page.tsx", "line": 36, "column": 66, "context": "const projectMemoryStats: ProjectMemoryStats[] = projects?.map((project: any) => ({", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "app\\(dashboard)\\analytics\\page-client.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\(dashboard)\\analytics\\page-client.tsx", "line": 7, "column": 11, "context": "projects: any[]", "suggestedFix": ": unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\(dashboard)\\analytics\\page-client.tsx", "line": 7, "column": 13, "context": "projects: any[]", "suggestedFix": ": unknown[]"}], "category": "Other"}, {"filePath": "app\\(app)\\playground\\page.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\(app)\\playground\\page.tsx", "line": 39, "column": 61, "context": "const [playgroundProject, setPlaygroundProject] = useState<any>(null);"}], "category": "Other"}, {"filePath": "app\\api\\workers\\task-queue\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\workers\\task-queue\\route.ts", "line": 39, "column": 25, "context": "status: 'pending' as any,"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\workers\\task-queue\\route.ts", "line": 44, "column": 28, "context": "status: 'processing' as any,"}], "category": "Other"}, {"filePath": "app\\api\\user\\cookie-consent\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\user\\cookie-consent\\route.ts", "line": 47, "column": 27, "context": "const privacyUpdates: any = {}", "suggestedFix": "unknown"}], "category": "Other"}, {"filePath": "app\\api\\security\\validate\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\security\\validate\\route.ts", "line": 156, "column": 12, "context": "details: Record<string, any>;", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\security\\validate\\route.ts", "line": 208, "column": 12, "context": "details: Record<string, any>;", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\security\\validate\\route.ts", "line": 251, "column": 12, "context": "details: Record<string, any>;", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\security\\validate\\route.ts", "line": 296, "column": 12, "context": "details: Record<string, any>;", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\security\\validate\\route.ts", "line": 335, "column": 12, "context": "details: Record<string, any>;", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\security\\validate\\route.ts", "line": 377, "column": 12, "context": "details: Record<string, any>;", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\security\\validate\\route.ts", "line": 402, "column": 12, "context": "details: Record<string, any>;", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\security\\validate\\route.ts", "line": 421, "column": 12, "context": "details: Record<string, any>;", "suggestedFix": "Record<string, unknown>"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\security\\validate\\route.ts", "line": 445, "column": 12, "context": "details: Record<string, any>;", "suggestedFix": "Record<string, unknown>"}], "category": "Other"}, {"filePath": "app\\api\\references\\[id]\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\references\\[id]\\route.ts", "line": 89, "column": 20, "context": "const updates: Record<string, any> = {", "suggestedFix": "Record<string, unknown>"}], "category": "Other"}, {"filePath": "app\\api\\email\\send\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\email\\send\\route.ts", "line": 138, "column": 22, "context": "data: data as any"}], "category": "Other"}, {"filePath": "app\\api\\analytics\\selections\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analytics\\selections\\route.ts", "line": 23, "column": 27, "context": "selectionData: z.record(z.any()).optional(),", "suggestedFix": "z.unknown()"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analytics\\selections\\route.ts", "line": 24, "column": 25, "context": "outcomeData: z.record(z.any()).optional()", "suggestedFix": "z.unknown()"}], "category": "Other"}, {"filePath": "app\\api\\analysis\\voice\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analysis\\voice\\route.ts", "line": 16, "column": 20, "context": "existingProfile: z.any().optional()", "suggestedFix": "z.unknown()"}], "category": "Other"}, {"filePath": "app\\api\\analysis\\character-development\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\analysis\\character-development\\route.ts", "line": 216, "column": 46, "context": "const milestonesByAspect: Record<string, any[]> = {}", "suggestedFix": "unknown[]"}], "category": "Other"}, {"filePath": "app\\api\\ai\\structured-content\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\ai\\structured-content\\route.ts", "line": 27, "column": 24, "context": "parameters: z.record(z.any()).optional(),", "suggestedFix": "z.unknown()"}], "category": "Other"}, {"filePath": "app\\api\\billing\\payments\\charge\\route.ts", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\api\\billing\\payments\\charge\\route.ts", "line": 139, "column": 23, "context": "if ((customer as any).deleted) {"}], "category": "Other"}, {"filePath": "app\\(dashboard)\\projects\\[id]\\write\\optimized-write-page.tsx", "anyTypes": [{"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\(dashboard)\\projects\\[id]\\write\\optimized-write-page.tsx", "line": 92, "column": 64, "context": "const [collaborationUsers, setCollaborationUsers] = useState<any[]>([])", "suggestedFix": "unknown[]"}, {"file": "C:\\Users\\<USER>\\BookScribe\\src\\app\\(dashboard)\\projects\\[id]\\write\\optimized-write-page.tsx", "line": 93, "column": 67, "context": "const [collaborationService, setCollaborationService] = useState<any>(null)"}], "category": "Other"}]}