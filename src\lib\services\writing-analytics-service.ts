import { createTypedServerClient } from '@/lib/supabase';
import { logger } from '@/lib/services/logger';
import { ServiceBase, ServiceResponse } from './base-service';
import { Database } from '@/lib/db/database.types';
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays, differenceInDays } from 'date-fns';
import { TIME_MS } from '@/lib/constants';

type WritingSession = Database['public']['Tables']['writing_sessions']['Row'];
type UserAnalytics = Database['public']['Tables']['user_analytics']['Row'];
type AIUsageLog = Database['public']['Tables']['ai_usage_logs']['Row'];
type SelectionAnalytics = Database['public']['Tables']['selection_analytics']['Row'];

interface AgentAggregation {
  agent: string;
  totalCalls: number;
  totalTokens: number;
  totalCost: number;
  totalDuration: number;
  successRate: number;
  successCount: number;
  avgTokensPerCall?: number;
  avgDuration?: number;
}

interface ModelAggregation {
  model: string;
  totalCalls: number;
  totalTokens: number;
  totalCost: number;
  avgResponseTime: number;
  totalDuration: number;
  avgTokensPerCall?: number;
}

interface DayAggregation {
  date: string;
  totalCalls: number;
  totalTokens: number;
  totalCost: number;
  agents: Set<string>;
  uniqueAgents?: number;
}

interface PatternData {
  completed: number;
  abandoned: number;
  successRate?: number;
  total?: number;
}

interface SelectionPatterns {
  genreSuccess: Record<string, PatternData>;
  structureSuccess: Record<string, PatternData>;
  paceSuccess: Record<string, PatternData>;
  targetWordCountSuccess: Record<string, PatternData>;
  povSuccess: Record<string, PatternData>;
  toneSuccess: Record<string, PatternData>;
}

interface ProfileStats {
  profile: {
    name: string;
    description: string;
    category: string;
    usage_count: number;
  };
  completed: number;
  abandoned: number;
  total?: number;
  successRate?: number;
}

interface ProductivityMetrics {
  overview: {
    totalWords: number;
    totalTime: number;
    totalSessions: number;
    activeDays: number;
    totalDays: number;
    productivityScore: number;
  };
  averages: {
    wordsPerDay: number;
    timePerDay: number;
    sessionsPerDay: number;
    wordsPerSession: number;
    sessionDuration: number;
  };
  patterns: {
    hourly: Array<{ hour: number; words: number; sessions: number }>;
    weekday: Array<{ day: string; words: number; sessions: number }>;
  };
  trends?: {
    wordsTrend: number;
    daysTrend: number;
  };
  insights?: Array<{ type: string; title: string; message: string; priority: 'high' | 'medium' | 'low' }>;
}

export class WritingAnalyticsService extends ServiceBase {
  constructor() {
    super({
      name: 'writing-analytics-service',
      version: '1.0.0',
      endpoints: ['/api/analytics'],
      dependencies: [],
      healthCheck: '/api/services/analytics/health'
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async shutdown(): Promise<void> {
    this.setStatus('inactive');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, { status: 'healthy' });
  }

  /**
   * Get productivity metrics for a user
   */
  async getProductivityMetrics(
    userId: string,
    options: {
      projectId?: string;
      timeframe?: 'week' | 'month' | 'quarter' | 'year';
      includeInsights?: boolean;
    } = {}
  ): Promise<ServiceResponse<ProductivityMetrics>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const { projectId, timeframe = 'week', includeInsights = false } = options;

      // Calculate date range
      const now = new Date();
      let startDate: Date;
      let endDate = now;

      switch (timeframe) {
        case 'week':
          startDate = startOfWeek(now);
          endDate = endOfWeek(now);
          break;
        case 'month':
          startDate = startOfMonth(now);
          endDate = endOfMonth(now);
          break;
        case 'quarter':
          startDate = subDays(now, 90);
          break;
        case 'year':
          startDate = subDays(now, 365);
          break;
        default:
          startDate = subDays(now, 7);
      }

      // Fetch writing sessions
      let sessionsQuery = supabase
        .from('writing_sessions')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (projectId) {
        sessionsQuery = sessionsQuery.eq('project_id', projectId);
      }

      const { data: sessions, error: sessionsError } = await sessionsQuery
        .order('created_at', { ascending: false })
        .limit(1000);

      if (sessionsError) {
        logger.error('[WritingAnalyticsService] Error fetching writing sessions:', sessionsError);
        throw sessionsError;
      }

      const sessionsData = sessions || [];

      // Calculate metrics
      const totalDays = differenceInDays(endDate, startDate) + 1;
      const activeDays = new Set(sessionsData.map(s => format(new Date(s.created_at), 'yyyy-MM-dd'))).size;
      const totalWords = sessionsData.reduce((sum, s) => sum + (s.word_count || 0), 0);
      const totalTime = sessionsData.reduce((sum, s) => sum + (s.duration || 0), 0);
      const totalSessions = sessionsData.length;

      // Daily averages
      const avgWordsPerDay = activeDays > 0 ? Math.round(totalWords / activeDays) : 0;
      const avgTimePerDay = activeDays > 0 ? Math.round(totalTime / activeDays / 60) : 0;
      const avgSessionsPerDay = activeDays > 0 ? parseFloat((totalSessions / activeDays).toFixed(1)) : 0;

      // Session averages
      const avgWordsPerSession = totalSessions > 0 ? Math.round(totalWords / totalSessions) : 0;
      const avgSessionDuration = totalSessions > 0 ? Math.round(totalTime / totalSessions / 60) : 0;

      // Productivity score
      const productivityScore = this.calculateProductivityScore({
        activeDays,
        totalDays,
        avgWordsPerDay,
        avgSessionDuration,
        totalSessions
      });

      // Time patterns
      const hourlyPattern = this.analyzeHourlyPattern(sessionsData);
      const weekdayPattern = this.analyzeWeekdayPattern(sessionsData);

      // Build response
      const response: ProductivityMetrics = {
        overview: {
          totalWords,
          totalTime: Math.round(totalTime / 60),
          totalSessions,
          activeDays,
          totalDays,
          productivityScore
        },
        averages: {
          wordsPerDay: avgWordsPerDay,
          timePerDay: avgTimePerDay,
          sessionsPerDay: avgSessionsPerDay,
          wordsPerSession: avgWordsPerSession,
          sessionDuration: avgSessionDuration
        },
        patterns: {
          hourly: hourlyPattern,
          weekday: weekdayPattern
        }
      };

      // Calculate trends for week/month
      if (timeframe === 'week' || timeframe === 'month') {
        const trends = await this.calculateTrends(
          userId,
          projectId,
          startDate,
          endDate,
          totalWords,
          activeDays
        );
        if (trends) {
          response.trends = trends;
        }
      }

      // Generate insights if requested
      if (includeInsights) {
        response.insights = this.generateProductivityInsights({
          activeDays,
          totalDays,
          avgWordsPerDay,
          avgSessionDuration,
          hourlyPattern,
          weekdayPattern,
          sessions: sessionsData
        });
      }

      return response;
    });
  }

  /**
   * Get AI usage analytics
   */
  async getAIUsageAnalytics(
    userId: string,
    options: {
      projectId?: string;
      startDate?: Date;
      endDate?: Date;
      groupBy?: 'agent' | 'model' | 'day';
    } = {}
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const { projectId, startDate = subDays(new Date(), 30), endDate = new Date(), groupBy = 'agent' } = options;

      // Build base query
      let query = supabase
        .from('ai_usage_logs')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      const { data: usageLogs, error } = await query
        .order('created_at', { ascending: false })
        .limit(1000);

      if (error) {
        logger.error('[WritingAnalyticsService] Error fetching AI usage logs:', error);
        throw error;
      }

      // Aggregate data based on groupBy parameter
      let aggregatedData: Record<string, AgentAggregation | ModelAggregation | DayAggregation> = {};

      switch (groupBy) {
        case 'agent':
          aggregatedData = (usageLogs || []).reduce<Record<string, AgentAggregation>>((acc, log) => {
            const agent = log.agent_name || 'Unknown';
            if (!acc[agent]) {
              acc[agent] = {
                agent,
                totalCalls: 0,
                totalTokens: 0,
                totalCost: 0,
                totalDuration: 0,
                successRate: 0,
                successCount: 0
              };
            }
            acc[agent].totalCalls++;
            acc[agent].totalTokens += log.tokens_used || 0;
            acc[agent].totalCost += log.estimated_cost || 0;
            acc[agent].totalDuration += log.duration || 0;
            if (log.success) acc[agent].successCount++;
            return acc;
          }, {});

          // Calculate success rates and averages
          Object.values(aggregatedData as Record<string, AgentAggregation>).forEach((agent) => {
            agent.successRate = agent.totalCalls > 0 
              ? Math.round((agent.successCount / agent.totalCalls) * 100) 
              : 0;
            agent.avgTokensPerCall = agent.totalCalls > 0
              ? Math.round(agent.totalTokens / agent.totalCalls)
              : 0;
            agent.avgDuration = agent.totalCalls > 0
              ? Math.round(agent.totalDuration / agent.totalCalls)
              : 0;
          });
          break;

        case 'model':
          aggregatedData = (usageLogs || []).reduce<Record<string, ModelAggregation>>((acc, log) => {
            const model = log.model || 'Unknown';
            if (!acc[model]) {
              acc[model] = {
                model,
                totalCalls: 0,
                totalTokens: 0,
                totalCost: 0,
                avgResponseTime: 0,
                totalDuration: 0
              };
            }
            acc[model].totalCalls++;
            acc[model].totalTokens += log.tokens_used || 0;
            acc[model].totalCost += log.estimated_cost || 0;
            acc[model].totalDuration += log.duration || 0;
            return acc;
          }, {});

          // Calculate averages
          Object.values(aggregatedData as Record<string, ModelAggregation>).forEach((model) => {
            model.avgResponseTime = model.totalCalls > 0
              ? Math.round(model.totalDuration / model.totalCalls)
              : 0;
            model.avgTokensPerCall = model.totalCalls > 0
              ? Math.round(model.totalTokens / model.totalCalls)
              : 0;
          });
          break;

        case 'day':
          aggregatedData = (usageLogs || []).reduce<Record<string, DayAggregation>>((acc, log) => {
            const date = format(new Date(log.created_at), 'yyyy-MM-dd');
            if (!acc[date]) {
              acc[date] = {
                date,
                totalCalls: 0,
                totalTokens: 0,
                totalCost: 0,
                agents: new Set()
              };
            }
            acc[date].totalCalls++;
            acc[date].totalTokens += log.tokens_used || 0;
            acc[date].totalCost += log.estimated_cost || 0;
            acc[date].agents.add(log.agent_name);
            return acc;
          }, {});

          // Convert sets to counts
          Object.values(aggregatedData as Record<string, DayAggregation>).forEach((day) => {
            day.uniqueAgents = day.agents.size;
            delete day.agents;
          });
          break;
      }

      // Calculate totals
      const totals = (usageLogs || []).reduce((acc, log) => {
        acc.totalCalls++;
        acc.totalTokens += log.tokens_used || 0;
        acc.totalCost += log.estimated_cost || 0;
        acc.totalDuration += log.duration || 0;
        if (log.success) acc.successCount++;
        return acc;
      }, {
        totalCalls: 0,
        totalTokens: 0,
        totalCost: 0,
        totalDuration: 0,
        successCount: 0
      });

      totals.successRate = totals.totalCalls > 0
        ? Math.round((totals.successCount / totals.totalCalls) * 100)
        : 0;
      totals.avgTokensPerCall = totals.totalCalls > 0
        ? Math.round(totals.totalTokens / totals.totalCalls)
        : 0;
      totals.avgDuration = totals.totalCalls > 0
        ? Math.round(totals.totalDuration / totals.totalCalls)
        : 0;

      return {
        aggregated: Object.values(aggregatedData),
        totals,
        raw: usageLogs?.slice(0, 100) || [], // Return latest 100 for detail view
        period: {
          start: format(startDate, 'yyyy-MM-dd'),
          end: format(endDate, 'yyyy-MM-dd')
        }
      };
    });
  }

  /**
   * Log AI usage event
   */
  async logAIUsage(
    userId: string,
    data: {
      projectId: string;
      agentName: string;
      model: string;
      tokensUsed: number;
      estimatedCost: number;
      duration: number;
      success: boolean;
      context?: Record<string, unknown>;
      error?: string;
    }
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      // Log AI usage
      const { data: log, error: insertError } = await supabase
        .from('ai_usage_logs')
        .insert({
          user_id: userId,
          project_id: data.projectId,
          agent_name: data.agentName,
          model: data.model,
          tokens_used: data.tokensUsed,
          estimated_cost: data.estimatedCost,
          duration: data.duration,
          success: data.success,
          context: data.context,
          error_message: data.error,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (insertError) {
        logger.error('[WritingAnalyticsService] Failed to log AI usage:', insertError);
        throw insertError;
      }

      // Update daily aggregates
      const today = format(new Date(), 'yyyy-MM-dd');
      const { data: existing } = await supabase
        .from('ai_usage_daily')
        .select('*')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      if (existing) {
        await supabase
          .from('ai_usage_daily')
          .update({
            total_calls: existing.total_calls + 1,
            total_tokens: existing.total_tokens + data.tokensUsed,
            total_cost: existing.total_cost + data.estimatedCost,
            updated_at: new Date().toISOString()
          })
          .eq('id', existing.id);
      } else {
        await supabase
          .from('ai_usage_daily')
          .insert({
            user_id: userId,
            date: today,
            total_calls: 1,
            total_tokens: data.tokensUsed,
            total_cost: data.estimatedCost
          });
      }

      return { 
        log,
        dailyUsage: {
          date: today,
          totalCalls: existing ? existing.total_calls + 1 : 1,
          totalTokens: existing ? existing.total_tokens + data.tokensUsed : data.tokensUsed,
          totalCost: existing ? existing.total_cost + data.estimatedCost : data.estimatedCost
        }
      };
    });
  }

  /**
   * Track behavioral analytics event
   */
  async trackBehavioralEvent(
    userId: string,
    sessionId: string,
    event: {
      type: string;
      event: string;
      data: Record<string, any>;
      projectId?: string;
    }
  ): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { error } = await supabase
        .from('behavioral_analytics')
        .insert({
          user_id: userId,
          session_id: sessionId,
          event_type: event.type,
          event_name: event.event,
          event_data: event.data,
          project_id: event.projectId,
          created_at: new Date().toISOString()
        });

      if (error) {
        logger.error('[WritingAnalyticsService] Error tracking behavioral event:', error);
        throw error;
      }

      return true;
    });
  }

  /**
   * Get behavioral analytics
   */
  async getBehavioralAnalytics(
    userId: string,
    options: {
      projectId?: string;
      timeframe?: '7days' | '30days' | '90days';
    } = {}
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const { projectId, timeframe = '30days' } = options;

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeframe) {
        case '7days':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30days':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90days':
          startDate.setDate(endDate.getDate() - 90);
          break;
      }

      // Build query for writing sessions
      let query = supabase
        .from('writing_sessions')
        .select('*')
        .eq('user_id', userId)
        .gte('start_time', startDate.toISOString())
        .lte('start_time', endDate.toISOString())
        .order('start_time', { ascending: false });

      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      const { data: sessions, error } = await query;

      if (error) {
        logger.error('[WritingAnalyticsService] Error fetching writing sessions:', error);
        throw error;
      }

      // Analyze behavioral patterns
      return this.analyzeBehavioralPatterns(sessions || []);
    });
  }

  /**
   * Create writing session
   */
  async createWritingSession(
    userId: string,
    data: {
      projectId: string;
      startTime: string;
      endTime?: string;
      wordsWritten?: number;
      mode?: string;
      actions?: Record<string, number>;
    }
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const sessionData = {
        user_id: userId,
        project_id: data.projectId,
        start_time: data.startTime,
        end_time: data.endTime || new Date().toISOString(),
        words_written: data.wordsWritten || 0,
        mode: data.mode || 'Standard Writing',
        actions: data.actions || {}
      };

      const { data: session, error } = await supabase
        .from('writing_sessions')
        .insert(sessionData)
        .select()
        .single();

      if (error) {
        logger.error('[WritingAnalyticsService] Error creating writing session:', error);
        throw error;
      }

      return { session };
    });
  }

  /**
   * Get quality metrics
   */
  async getQualityMetrics(
    userId: string,
    options: {
      projectId?: string;
      startDate?: string;
      endDate?: string;
    } = {}
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const { projectId, startDate, endDate } = options;

      // Build query
      let query = supabase
        .from('quality_metrics')
        .select('*')
        .eq('user_id', userId);

      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      if (startDate) {
        query = query.gte('created_at', startDate);
      }

      if (endDate) {
        query = query.lte('created_at', endDate);
      }

      const { data: qualityMetrics, error } = await query
        .order('created_at', { ascending: false })
        .limit(500);

      if (error) {
        logger.error('[WritingAnalyticsService] Error fetching quality metrics:', error);
        throw error;
      }

      // Calculate aggregate quality scores
      const aggregateQuality = qualityMetrics && qualityMetrics.length > 0 ? {
        overallScore: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.overall_score || 0), 0) / qualityMetrics.length),
        readability: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_readability || 0), 0) / qualityMetrics.length),
        consistency: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_consistency || 0), 0) / qualityMetrics.length),
        pacing: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_pacing || 0), 0) / qualityMetrics.length),
        engagement: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_engagement || 0), 0) / qualityMetrics.length),
        dialogue: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_dialogue_authenticity || 0), 0) / qualityMetrics.length),
        description: Math.round(qualityMetrics.reduce((sum, m) => sum + (m.avg_creativity || 0), 0) / qualityMetrics.length),
      } : {
        overallScore: 0,
        readability: 0,
        consistency: 0,
        pacing: 0,
        engagement: 0,
        dialogue: 0,
        description: 0,
      };

      return {
        metrics: qualityMetrics || [],
        aggregate: aggregateQuality,
        count: qualityMetrics?.length || 0,
        period: {
          start: startDate || qualityMetrics?.[qualityMetrics.length - 1]?.created_at || null,
          end: endDate || qualityMetrics?.[0]?.created_at || null
        }
      };
    });
  }

  /**
   * Check daily quality analysis limit
   */
  async checkDailyAnalysisLimit(userId: string, limit: number = 50): Promise<ServiceResponse<{ allowed: boolean; count: number }>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const today = new Date().toISOString().split('T')[0];
      
      const { data: todayAnalyses } = await supabase
        .from('quality_metrics')
        .select('id')
        .eq('user_id', userId)
        .gte('created_at', today)
        .limit(limit);
      
      const count = todayAnalyses?.length || 0;
      return {
        allowed: count < limit,
        count
      };
    });
  }

  /**
   * Save quality metrics
   */
  async saveQualityMetrics(
    userId: string,
    data: {
      projectId?: string;
      overall_score: number;
      readability: number;
      consistency: number;
      pacing: number;
      engagement: number;
      dialogue: number;
      description: number;
      feedback: string;
      improvement_suggestions: string[];
      content_sample: string;
      word_count: number;
      analysis_type: string;
    }
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data: qualityRecord, error } = await supabase
        .from('quality_metrics')
        .insert({
          user_id: userId,
          project_id: data.projectId,
          overall_score: data.overall_score,
          avg_readability: data.readability,
          avg_consistency: data.consistency,
          avg_pacing: data.pacing,
          avg_engagement: data.engagement,
          avg_dialogue_authenticity: data.dialogue,
          avg_creativity: data.description,
          feedback: data.feedback,
          improvement_suggestions: data.improvement_suggestions,
          content_sample: data.content_sample,
          word_count: data.word_count,
          analysis_type: data.analysis_type,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        logger.error('[WritingAnalyticsService] Error saving quality metrics:', error);
        throw error;
      }

      return qualityRecord;
    });
  }

  /**
   * Get chapter analytics
   */
  async getChapterAnalytics(
    userId: string,
    projectId: string
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data: chapters, error } = await supabase
        .from('chapters')
        .select(`
          id,
          title,
          chapter_number,
          word_count,
          status,
          created_at,
          updated_at,
          chapter_versions(count)
        `)
        .eq('project_id', projectId)
        .order('chapter_number', { ascending: true });

      if (error) {
        logger.error('[WritingAnalyticsService] Error fetching chapter analytics:', error);
        throw error;
      }

      const totalChapters = chapters?.length || 0;
      const completedChapters = chapters?.filter(c => c.status === 'completed').length || 0;
      const totalWords = chapters?.reduce((sum, c) => sum + (c.word_count || 0), 0) || 0;
      const avgWordsPerChapter = totalChapters > 0 ? Math.round(totalWords / totalChapters) : 0;

      return {
        totalChapters,
        completedChapters,
        inProgressChapters: totalChapters - completedChapters,
        totalWords,
        avgWordsPerChapter,
        chapters: chapters || []
      };
    });
  }

  private calculateProductivityScore(metrics: {
    activeDays: number;
    totalDays: number;
    avgWordsPerDay: number;
    avgSessionDuration: number;
    totalSessions: number;
  }): number {
    const { activeDays, totalDays, avgWordsPerDay, avgSessionDuration, totalSessions } = metrics;

    // Consistency score (40% weight)
    const consistencyScore = Math.min((activeDays / totalDays) * 100, 100) * 0.4;

    // Volume score (30% weight)
    const volumeScore = Math.min((avgWordsPerDay / 1000) * 100, 100) * 0.3;

    // Focus score (20% weight)
    const focusScore = Math.min((avgSessionDuration / 60) * 100, 100) * 0.2;

    // Frequency score (10% weight)
    const frequencyScore = Math.min((totalSessions / (totalDays * 2)) * 100, 100) * 0.1;

    return Math.round(consistencyScore + volumeScore + focusScore + frequencyScore);
  }

  private analyzeHourlyPattern(sessions: WritingSession[]): Array<{ hour: number; words: number; sessions: number }> {
    const hourlyData = new Array(24).fill(0).map((_, hour) => ({
      hour,
      words: 0,
      sessions: 0
    }));

    sessions.forEach(session => {
      const hour = new Date(session.created_at).getHours();
      hourlyData[hour].words += session.word_count || 0;
      hourlyData[hour].sessions += 1;
    });

    return hourlyData;
  }

  private analyzeWeekdayPattern(sessions: WritingSession[]): Array<{ day: string; words: number; sessions: number }> {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const weekdayData = days.map(day => ({
      day,
      words: 0,
      sessions: 0
    }));

    sessions.forEach(session => {
      const dayIndex = new Date(session.created_at).getDay();
      weekdayData[dayIndex].words += session.word_count || 0;
      weekdayData[dayIndex].sessions += 1;
    });

    return weekdayData;
  }

  private async calculateTrends(
    userId: string,
    projectId: string | undefined,
    startDate: Date,
    endDate: Date,
    currentWords: number,
    currentActiveDays: number
  ): Promise<{ wordsTrend: number; daysTrend: number } | null> {
    const supabase = await createTypedServerClient();
    const daysDiff = differenceInDays(endDate, startDate) + 1;
    const previousStartDate = subDays(startDate, daysDiff);
    const previousEndDate = subDays(startDate, 1);

    let query = supabase
      .from('writing_sessions')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', previousStartDate.toISOString())
      .lte('created_at', previousEndDate.toISOString());

    if (projectId) {
      query = query.eq('project_id', projectId);
    }

    const { data: prevSessions } = await query;

    if (prevSessions) {
      const prevTotalWords = prevSessions.reduce((sum, s) => sum + (s.word_count || 0), 0);
      const prevActiveDays = new Set(prevSessions.map(s => format(new Date(s.created_at), 'yyyy-MM-dd'))).size;

      return {
        wordsTrend: prevTotalWords > 0
          ? Math.round(((currentWords - prevTotalWords) / prevTotalWords) * 100)
          : 0,
        daysTrend: prevActiveDays > 0
          ? Math.round(((currentActiveDays - prevActiveDays) / prevActiveDays) * 100)
          : 0
      };
    }

    return null;
  }

  private generateProductivityInsights(data: {
    activeDays: number;
    totalDays: number;
    avgWordsPerDay: number;
    avgSessionDuration: number;
    hourlyPattern: Array<{ hour: number; words: number; sessions: number }>;
    weekdayPattern: Array<{ day: string; words: number; sessions: number }>;
    sessions: WritingSession[];
  }): Array<{ type: string; title: string; message: string; priority: 'high' | 'medium' | 'low' }> {
    const insights = [];

    // Consistency insight
    const consistencyRate = (data.activeDays / data.totalDays) * 100;
    if (consistencyRate < 50) {
      insights.push({
        type: 'consistency',
        title: 'Improve Writing Consistency',
        message: `You've written on ${data.activeDays} out of ${data.totalDays} days (${Math.round(consistencyRate)}%). Try to write more regularly to build momentum.`,
        priority: 'high' as const
      });
    } else if (consistencyRate > 80) {
      insights.push({
        type: 'consistency',
        title: 'Excellent Consistency!',
        message: `You've maintained a ${Math.round(consistencyRate)}% writing consistency. Keep up the great work!`,
        priority: 'low' as const
      });
    }

    // Peak productivity hours
    const sortedHours = [...data.hourlyPattern].sort((a, b) => b.words - a.words).slice(0, 3);
    if (sortedHours[0]?.words > 0) {
      const peakHours = sortedHours
        .filter(h => h.words > 0)
        .map(h => {
          const hour = h.hour;
          const period = hour < 12 ? 'AM' : 'PM';
          const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
          return `${displayHour}${period}`;
        })
        .join(', ');

      insights.push({
        type: 'timing',
        title: 'Peak Writing Hours',
        message: `Your most productive hours are ${peakHours}. Consider scheduling important writing sessions during these times.`,
        priority: 'medium' as const
      });
    }

    // Session duration insight
    if (data.avgSessionDuration < 30) {
      insights.push({
        type: 'focus',
        title: 'Extend Your Writing Sessions',
        message: `Your average session is ${data.avgSessionDuration} minutes. Try aiming for 30-60 minute sessions for better flow and productivity.`,
        priority: 'medium' as const
      });
    }

    // Words per day insight
    if (data.avgWordsPerDay < 500 && data.activeDays > 0) {
      insights.push({
        type: 'volume',
        title: 'Increase Daily Word Count',
        message: `You're averaging ${data.avgWordsPerDay} words per writing day. Set a goal to reach 500-1000 words daily.`,
        priority: 'medium' as const
      });
    } else if (data.avgWordsPerDay > 2000) {
      insights.push({
        type: 'volume',
        title: 'Outstanding Output!',
        message: `You're averaging ${data.avgWordsPerDay} words per writing day. That's professional-level productivity!`,
        priority: 'low' as const
      });
    }

    return insights;
  }

  private analyzeBehavioralPatterns(sessions: WritingSession[]): {
    peakHours: Array<{ hour: number; productivity: number }>;
    writingModes: Array<{ mode: string; percentage: number }>;
    frequentActions: Array<{ action: string; count: number }>;
  } {
    // Initialize data structures
    const hourlyProductivity = new Map<number, { totalWords: number; sessionCount: number }>();
    const writingModes = new Map<string, number>();
    const actionCounts = new Map<string, number>();

    // Initialize all hours
    for (let hour = 0; hour < 24; hour++) {
      hourlyProductivity.set(hour, { totalWords: 0, sessionCount: 0 });
    }

    // Process sessions
    sessions.forEach(session => {
      // Analyze peak hours
      const startTime = new Date(session.start_time);
      const hour = startTime.getHours();
      const hourData = hourlyProductivity.get(hour) || { totalWords: 0, sessionCount: 0 };
      hourData.totalWords += session.word_count || 0;
      hourData.sessionCount++;
      hourlyProductivity.set(hour, hourData);

      // Analyze writing modes
      const mode = (session as any).mode || 'Standard Writing';
      writingModes.set(mode, (writingModes.get(mode) || 0) + 1);

      // Analyze actions
      if ((session as any).actions) {
        Object.entries((session as any).actions).forEach(([action, count]) => {
          actionCounts.set(action, (actionCounts.get(action) || 0) + (count as number));
        });
      }
    });

    // Calculate peak hours productivity
    const maxWordsPerHour = Math.max(...Array.from(hourlyProductivity.values()).map(d => d.totalWords));
    const peakHours = Array.from(hourlyProductivity.entries())
      .map(([hour, data]) => ({
        hour,
        productivity: maxWordsPerHour > 0 ? Math.round((data.totalWords / maxWordsPerHour) * 100) : 0
      }))
      .filter(h => h.productivity > 0) // Only include hours with activity
      .sort((a, b) => b.productivity - a.productivity);

    // Calculate writing mode percentages
    const totalSessions = sessions.length;
    const writingModesArray = Array.from(writingModes.entries())
      .map(([mode, count]) => ({
        mode,
        percentage: Math.round((count / totalSessions) * 100)
      }))
      .sort((a, b) => b.percentage - a.percentage);

    // Add default modes if not present
    const defaultModes = ['Sprint Writing', 'Deep Focus', 'Editing', 'Planning'];
    defaultModes.forEach(mode => {
      if (!writingModesArray.find(m => m.mode === mode)) {
        writingModesArray.push({ mode, percentage: 0 });
      }
    });

    // Sort frequent actions
    const frequentActions = Array.from(actionCounts.entries())
      .map(([action, count]) => ({ action, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 actions

    // Add default actions if empty
    if (frequentActions.length === 0) {
      frequentActions.push(
        { action: 'Words Written', count: sessions.reduce((sum, s) => sum + (s.word_count || 0), 0) },
        { action: 'Sessions Started', count: sessions.length },
        { action: 'Characters Created', count: 0 },
        { action: 'Chapters Completed', count: 0 }
      );
    }

    return {
      peakHours: peakHours.slice(0, 24), // All active hours
      writingModes: writingModesArray.slice(0, 4), // Top 4 modes
      frequentActions
    };
  }

  /**
   * Track a search event
   */
  async trackSearchEvent(
    userId: string,
    data: {
      projectId: string;
      query: string;
      resultCount: number;
      clickedResult?: {
        id: string;
        type: string;
        position: number;
      };
      timestamp: string;
    }
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      // Store search event
      const { data: searchEvent, error } = await supabase
        .from('search_analytics')
        .insert({
          project_id: data.projectId,
          user_id: userId,
          query: data.query.toLowerCase().trim(),
          result_count: data.resultCount,
          clicked_result_id: data.clickedResult?.id,
          clicked_result_type: data.clickedResult?.type,
          clicked_result_position: data.clickedResult?.position,
          timestamp: data.timestamp
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to track search event: ${error.message}`);
      }

      return searchEvent;
    });
  }

  /**
   * Get popular searches for a project
   */
  async getPopularSearches(
    projectId: string,
    options: {
      limit?: number;
      since?: Date;
    } = {}
  ): Promise<ServiceResponse<Array<{ query: string; count: number; clickRate: number }>>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const limit = Math.min(options.limit || 5, 10);
      const since = options.since || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days

      // Try to use the RPC function if it exists
      const { data: searches, error: rpcError } = await supabase
        .rpc('get_popular_searches', {
          p_project_id: projectId,
          p_since: since.toISOString(),
          p_limit: limit
        });

      if (!rpcError && searches) {
        return searches;
      }

      // Fallback to direct query if RPC function doesn't exist
      if (rpcError?.code === '42883') {
        // Manual aggregation
        const { data: searchData, error } = await supabase
          .from('search_analytics')
          .select('query, clicked_result_id')
          .eq('project_id', projectId)
          .gte('timestamp', since.toISOString())
          .order('timestamp', { ascending: false });

        if (error) {
          throw new Error(`Failed to get search analytics: ${error.message}`);
        }

        if (!searchData || searchData.length === 0) {
          return [];
        }

        // Aggregate search data
        const searchAggregates = new Map<string, { count: number; clicks: number }>();
        
        searchData.forEach(search => {
          const key = search.query.toLowerCase();
          const existing = searchAggregates.get(key) || { count: 0, clicks: 0 };
          existing.count++;
          if (search.clicked_result_id) {
            existing.clicks++;
          }
          searchAggregates.set(key, existing);
        });

        // Convert to array and calculate click rates
        const popularSearches = Array.from(searchAggregates.entries())
          .map(([query, stats]) => ({
            query,
            count: stats.count,
            clickRate: stats.count > 0 ? (stats.clicks / stats.count) : 0
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, limit);

        return popularSearches;
      }

      throw new Error(`Failed to get popular searches: ${rpcError.message}`);
    });
  }

  /**
   * Get search analytics for a project
   */
  async getSearchAnalytics(
    projectId: string,
    options: {
      startDate?: string;
      endDate?: string;
      groupBy?: 'day' | 'week' | 'month';
    } = {}
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      let query = supabase
        .from('search_analytics')
        .select('*')
        .eq('project_id', projectId);

      if (options.startDate) {
        query = query.gte('timestamp', options.startDate);
      }

      if (options.endDate) {
        query = query.lte('timestamp', options.endDate);
      }

      const { data: searchData, error } = await query
        .order('timestamp', { ascending: false });

      if (error) {
        throw new Error(`Failed to get search analytics: ${error.message}`);
      }

      if (!searchData || searchData.length === 0) {
        return {
          totalSearches: 0,
          uniqueQueries: 0,
          avgResultCount: 0,
          clickThroughRate: 0,
          topQueries: [],
          searchTrends: []
        };
      }

      // Calculate metrics
      const uniqueQueries = new Set(searchData.map(s => s.query.toLowerCase()));
      const searchesWithClicks = searchData.filter(s => s.clicked_result_id).length;
      const totalResultCount = searchData.reduce((sum, s) => sum + (s.result_count || 0), 0);

      // Get top queries
      const queryFrequency = new Map<string, number>();
      searchData.forEach(search => {
        const query = search.query.toLowerCase();
        queryFrequency.set(query, (queryFrequency.get(query) || 0) + 1);
      });

      const topQueries = Array.from(queryFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .map(([query, count]) => ({ query, count }));

      // Group by time period
      const searchTrends = this.groupByTimePeriod(searchData, options.groupBy || 'day', 'timestamp');

      return {
        totalSearches: searchData.length,
        uniqueQueries: uniqueQueries.size,
        avgResultCount: searchData.length > 0 ? Math.round(totalResultCount / searchData.length) : 0,
        clickThroughRate: searchData.length > 0 ? (searchesWithClicks / searchData.length) : 0,
        topQueries,
        searchTrends
      };
    });
  }

  /**
   * Helper method to group data by time period
   */
  private groupByTimePeriod(
    data: any[],
    period: 'day' | 'week' | 'month',
    dateField: string
  ): Array<{ period: string; count: number }> {
    const groups = new Map<string, number>();

    data.forEach(item => {
      const date = new Date(item[dateField]);
      let key: string;

      switch (period) {
        case 'day':
          key = date.toISOString().split('T')[0];
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          key = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          break;
      }

      groups.set(key, (groups.get(key) || 0) + 1);
    });

    return Array.from(groups.entries())
      .map(([period, count]) => ({ period, count }))
      .sort((a, b) => a.period.localeCompare(b.period));
  }

  /**
   * Get writing sessions with analytics
   */
  async getSessionAnalytics(
    userId: string,
    options: {
      projectId?: string;
      startDate?: string;
      endDate?: string;
      type?: 'overview' | 'hourly' | 'weekly' | 'daily';
      limit?: number;
    } = {}
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      // Build base query
      let query = supabase
        .from('writing_sessions')
        .select('id, created_at, word_count, duration, session_type, project_id')
        .eq('user_id', userId);

      if (options.projectId) {
        query = query.eq('project_id', options.projectId);
      }

      if (options.startDate) {
        query = query.gte('created_at', options.startDate);
      }

      if (options.endDate) {
        query = query.lte('created_at', options.endDate);
      }

      const { data: sessions, error } = await query
        .order('created_at', { ascending: false })
        .limit(options.limit || 5000);

      if (error) {
        throw new Error(`Failed to fetch sessions: ${error.message}`);
      }

      const response: any = {
        sessions,
        count: sessions.length
      };

      // Calculate analytics based on type
      switch (options.type || 'overview') {
        case 'overview':
          const totalWords = sessions.reduce((sum, s) => sum + (s.word_count || 0), 0);
          const totalDuration = sessions.reduce((sum, s) => sum + (s.duration || 0), 0);
          const avgSessionDuration = sessions.length > 0 ? Math.round(totalDuration / sessions.length / 60) : 0;
          const avgWordsPerSession = sessions.length > 0 ? Math.round(totalWords / sessions.length) : 0;

          response.overview = {
            totalWords,
            totalSessions: sessions.length,
            totalDuration: Math.round(totalDuration / 60), // in minutes
            avgSessionDuration,
            avgWordsPerSession,
            avgWordsPerMinute: totalDuration > 0 ? Math.round(totalWords / (totalDuration / 60)) : 0
          };
          break;

        case 'hourly':
          const hourlyData = new Array(24).fill(0).map((_, hour) => ({
            hour: `${hour}:00`,
            words: 0,
            sessions: 0
          }));

          sessions.forEach(session => {
            const hour = new Date(session.created_at).getHours();
            hourlyData[hour].words += session.word_count || 0;
            hourlyData[hour].sessions += 1;
          });

          response.hourlyPattern = hourlyData.map(h => ({
            date: h.hour,
            value: h.words
          }));
          break;

        case 'weekly':
          const weeklyData = new Array(7).fill(0).map((_, day) => ({
            day: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][day],
            words: 0,
            sessions: 0
          }));

          sessions.forEach(session => {
            const day = new Date(session.created_at).getDay();
            weeklyData[day].words += session.word_count || 0;
            weeklyData[day].sessions += 1;
          });

          response.weeklyPattern = weeklyData.map(w => ({
            date: w.day,
            value: w.words
          }));
          break;

        case 'daily':
          const dailyMap = new Map<string, { words: number; sessions: number }>();
          
          sessions.forEach(session => {
            const date = format(new Date(session.created_at), 'yyyy-MM-dd');
            const existing = dailyMap.get(date) || { words: 0, sessions: 0 };
            dailyMap.set(date, {
              words: existing.words + (session.word_count || 0),
              sessions: existing.sessions + 1
            });
          });

          response.dailyData = Array.from(dailyMap.entries())
            .map(([date, data]) => ({
              date: format(new Date(date), 'MMM dd'),
              value: data.words,
              sessions: data.sessions
            }))
            .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
          break;
      }

      // Calculate streak
      const streakData = await this.calculateUserStreak(userId);
      response.streak = streakData.currentStreak;
      response.currentStreak = streakData.currentStreak;
      response.longestStreak = streakData.longestStreak;

      return response;
    });
  }

  /**
   * Calculate user writing streak
   */
  async calculateUserStreak(userId: string): Promise<{ currentStreak: number; longestStreak: number }> {
    const supabase = await createTypedServerClient();

    // Get writing sessions to calculate streak
    const { data: sessions } = await supabase
      .from('writing_sessions')
      .select('created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(365); // Last year of sessions

    if (!sessions || sessions.length === 0) {
      return { currentStreak: 0, longestStreak: 0 };
    }

    // Calculate current streak
    const uniqueDays = new Set(
      sessions.map(s => format(new Date(s.created_at), 'yyyy-MM-dd'))
    );
    const sortedDays = Array.from(uniqueDays).sort().reverse();
    let currentStreak = 0;
    
    for (let i = 0; i < sortedDays.length; i++) {
      const expectedDate = format(
        new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        'yyyy-MM-dd'
      );
      if (sortedDays[i] === expectedDate) {
        currentStreak++;
      } else {
        break;
      }
    }

    // Get stored streak data
    const { data: streakData } = await supabase
      .from('user_streaks')
      .select('current_streak, longest_streak')
      .eq('user_id', userId)
      .single();

    const longestStreak = Math.max(
      streakData?.longest_streak || 0,
      currentStreak
    );

    // Update streak if needed
    if (!streakData || streakData.current_streak !== currentStreak) {
      await supabase
        .from('user_streaks')
        .upsert({
          user_id: userId,
          current_streak: currentStreak,
          longest_streak: longestStreak,
          last_writing_date: sortedDays[0] || new Date().toISOString()
        });
    }

    return { currentStreak, longestStreak };
  }

  /**
   * Get profile performance metrics
   */
  async getProfilePerformanceMetrics(
    userId: string,
    options: {
      profileId?: string;
      timeframe?: 'week' | 'month' | 'quarter' | 'year';
    } = {}
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      
      switch (options.timeframe || 'month') {
        case 'week':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(endDate.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      // Query selection analytics
      let query = supabase
        .from('selection_analytics')
        .select(`
          *,
          projects(
            id,
            name,
            genre,
            status,
            word_count,
            target_word_count,
            created_at,
            completed_at
          )
        `)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (options.profileId) {
        query = query.eq('selection_profile_id', options.profileId);
      } else {
        query = query.eq('user_id', userId);
      }

      const { data: analytics, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch analytics: ${error.message}`);
      }

      // Get profile information if profileId is provided
      let profile = null;
      if (options.profileId) {
        const { data: profileData } = await supabase
          .from('selection_profiles')
          .select('*')
          .eq('id', options.profileId)
          .single();

        profile = profileData;
      }

      // Calculate performance metrics
      const performance = this.calculateProfilePerformanceMetrics(analytics || []);

      // Transform data for the ProfilePerformanceMetrics component
      const profileMetrics = [{
        profileName: profile?.name || 'Default Profile',
        projectsCreated: performance.projectsCreated,
        projectsCompleted: performance.projectsCompleted,
        averageWordCount: performance.averageWordCount,
        completionRate: performance.successRate,
        averageTime: performance.averageCompletionTime,
        successScore: Math.min(100, Math.round(
          (performance.successRate * 0.4) + 
          (Math.min(performance.averageWordCount / 1000, 100) * 0.3) +
          (Math.max(0, 100 - performance.averageCompletionTime / 3) * 0.3)
        ))
      }];

      return profileMetrics;
    });
  }

  /**
   * Calculate profile performance metrics
   */
  private calculateProfilePerformanceMetrics(analytics: any[]): any {
    const metrics = {
      totalUsage: 0,
      projectsCreated: 0,
      projectsCompleted: 0,
      projectsAbandoned: 0,
      averageWordCount: 0,
      averageCompletionTime: 0,
      successRate: 0,
      usageByEventType: {} as { [key: string]: number },
      genreDistribution: {} as { [key: string]: number },
      wordCountDistribution: {
        'novella': 0,
        'short-novel': 0,
        'standard-novel': 0,
        'long-novel': 0,
        'epic-novel': 0
      },
      completionTimeDistribution: {
        'under-week': 0,
        'week-month': 0,
        'month-quarter': 0,
        'quarter-year': 0,
        'over-year': 0
      }
    };

    let totalWordCount = 0;
    let totalCompletionTime = 0;
    let completionTimeCount = 0;

    analytics.forEach(entry => {
      metrics.totalUsage++;

      // Count by event type
      const eventType = entry.event_type;
      if (!metrics.usageByEventType[eventType]) {
        metrics.usageByEventType[eventType] = 0;
      }
      metrics.usageByEventType[eventType]++;

      // Project-specific metrics
      if (entry.projects) {
        const project = entry.projects;
        
        if (entry.event_type === 'project_created') {
          metrics.projectsCreated++;
          
          // Genre distribution
          if (project.genre) {
            const genre = project.genre;
            if (!metrics.genreDistribution[genre]) {
              metrics.genreDistribution[genre] = 0;
            }
            metrics.genreDistribution[genre]++;
          }

          // Word count tracking
          if (project.target_word_count) {
            totalWordCount += project.target_word_count;
            
            const wordCountRange = this.getWordCountRange(project.target_word_count);
            metrics.wordCountDistribution[wordCountRange]++;
          }
        }

        if (entry.event_type === 'project_completed') {
          metrics.projectsCompleted++;

          // Calculate completion time
          if (project.created_at && project.completed_at) {
            const createdDate = new Date(project.created_at);
            const completedDate = new Date(project.completed_at);
            const completionTimeMs = completedDate.getTime() - createdDate.getTime();
            const completionTimeDays = completionTimeMs / (1000 * 60 * 60 * 24);
            
            totalCompletionTime += completionTimeDays;
            completionTimeCount++;

            // Completion time distribution
            const timeRange = this.getCompletionTimeRange(completionTimeDays);
            metrics.completionTimeDistribution[timeRange]++;
          }
        }

        if (entry.event_type === 'project_abandoned') {
          metrics.projectsAbandoned++;
        }
      }
    });

    // Calculate averages and rates
    if (metrics.projectsCreated > 0) {
      metrics.averageWordCount = Math.round(totalWordCount / metrics.projectsCreated);
    }

    if (completionTimeCount > 0) {
      metrics.averageCompletionTime = Math.round(totalCompletionTime / completionTimeCount);
    }

    if (metrics.projectsCompleted + metrics.projectsAbandoned > 0) {
      metrics.successRate = Math.round(
        (metrics.projectsCompleted / (metrics.projectsCompleted + metrics.projectsAbandoned)) * 10000
      ) / 100;
    }

    return metrics;
  }

  private getWordCountRange(wordCount: number): string {
    if (wordCount < 50000) return 'novella';
    if (wordCount < 80000) return 'short-novel';
    if (wordCount < 120000) return 'standard-novel';
    if (wordCount < 200000) return 'long-novel';
    return 'epic-novel';
  }

  private getCompletionTimeRange(days: number): string {
    if (days < 7) return 'under-week';
    if (days < 30) return 'week-month';
    if (days < 90) return 'month-quarter';
    if (days < 365) return 'quarter-year';
    return 'over-year';
  }

  /**
   * Get recommendations based on quality metrics
   */
  async getRecommendations(
    userId: string,
    options: {
      projectId?: string;
      seriesId?: string;
    } = {}
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      let projectsData: any[] = [];

      if (options.seriesId) {
        // Handle series recommendations
        const { data: seriesProjects, error } = await supabase
          .from('projects')
          .select(`
            id,
            title,
            project_quality_metrics (
              overall_score,
              avg_readability,
              avg_consistency,
              avg_engagement,
              avg_dialogue_authenticity,
              avg_pacing,
              avg_creativity,
              created_at
            )
          `)
          .eq('series_id', options.seriesId)
          .eq('user_id', userId);

        if (error) {
          throw new Error(`Failed to fetch series projects: ${error.message}`);
        }

        projectsData = seriesProjects || [];

      } else if (options.projectId) {
        // Single project detailed recommendations
        const { data: project, error } = await supabase
          .from('projects')
          .select(`
            id,
            title,
            project_quality_metrics (
              overall_score,
              avg_readability,
              avg_consistency,
              avg_engagement,
              avg_dialogue_authenticity,
              avg_pacing,
              avg_creativity,
              feedback,
              improvement_suggestions
            )
          `)
          .eq('id', options.projectId)
          .eq('user_id', userId)
          .single();

        if (error) {
          throw new Error(`Failed to fetch project: ${error.message}`);
        }

        projectsData = project ? [project] : [];

      } else {
        // All projects
        const { data: allProjects, error } = await supabase
          .from('projects')
          .select(`
            id,
            title,
            project_quality_metrics (
              overall_score,
              avg_readability,
              avg_consistency,
              avg_engagement,
              avg_dialogue_authenticity,
              avg_pacing,
              avg_creativity,
              created_at
            )
          `)
          .eq('user_id', userId);

        if (error) {
          throw new Error(`Failed to fetch projects: ${error.message}`);
        }

        projectsData = allProjects || [];
      }

      // Transform data
      const projectsWithQuality = projectsData
        .map(project => ({
          projectId: project.id,
          projectTitle: project.title,
          overallScore: project.project_quality_metrics?.[0]?.overall_score || 0,
          readability: project.project_quality_metrics?.[0]?.avg_readability || 0,
          consistency: project.project_quality_metrics?.[0]?.avg_consistency || 0,
          engagement: project.project_quality_metrics?.[0]?.avg_engagement || 0,
          dialogue: project.project_quality_metrics?.[0]?.avg_dialogue_authenticity || 0,
          pacing: project.project_quality_metrics?.[0]?.avg_pacing || 0,
          creativity: project.project_quality_metrics?.[0]?.avg_creativity || 0,
        }))
        .filter(p => p.overallScore > 0);

      if (projectsWithQuality.length === 0) {
        return {
          recommendations: [],
          message: 'No quality data available for recommendations',
          projectsAnalyzed: 0,
          selectionType: options.projectId ? 'single' : options.seriesId ? 'series' : 'all'
        };
      }

      // Generate recommendations
      const recommendations = options.projectId 
        ? this.generateDetailedRecommendations(projectsWithQuality[0])
        : this.generatePriorityRecommendations(projectsWithQuality);

      return {
        recommendations,
        projectsAnalyzed: projectsWithQuality.length,
        selectionType: options.projectId ? 'single' : options.seriesId ? 'series' : 'all'
      };
    });
  }

  /**
   * Generate detailed recommendations for a single project
   */
  private generateDetailedRecommendations(project: any): any[] {
    const recommendations: any[] = [];
    const categories = [
      { key: 'readability', score: project.readability, icon: '📖' },
      { key: 'consistency', score: project.consistency, icon: '🔄' },
      { key: 'engagement', score: project.engagement, icon: '✨' },
      { key: 'dialogue', score: project.dialogue, icon: '💬' }
    ];

    for (const category of categories) {
      if (category.score < 80) {
        const categoryRecommendations = this.generateCategoryRecommendations(
          project,
          category.key as any,
          4
        );
        recommendations.push(...categoryRecommendations);
      }
    }

    return recommendations.sort((a, b) => {
      const severityOrder: any = { critical: 4, high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  }

  /**
   * Generate priority recommendations across projects
   */
  private generatePriorityRecommendations(projects: any[]): any[] {
    const allRecommendations: any[] = [];

    for (const project of projects) {
      const categories = [
        { key: 'readability', score: project.readability },
        { key: 'consistency', score: project.consistency },
        { key: 'engagement', score: project.engagement },
        { key: 'dialogue', score: project.dialogue }
      ];

      const worstCategory = categories
        .filter(c => c.score < 80)
        .sort((a, b) => a.score - b.score)[0];

      if (worstCategory) {
        const recommendations = this.generateCategoryRecommendations(
          project,
          worstCategory.key as any,
          1
        );
        allRecommendations.push(...recommendations);
      }
    }

    return allRecommendations
      .sort((a, b) => {
        const severityOrder: any = { critical: 4, high: 3, medium: 2, low: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      })
      .slice(0, 4);
  }

  /**
   * Generate recommendations for a specific category
   */
  private generateCategoryRecommendations(
    project: any,
    category: 'readability' | 'consistency' | 'engagement' | 'dialogue',
    maxRecommendations: number
  ): any[] {
    const score = project[category];
    const severity = this.getSeverity(score);
    const recommendations: any[] = [];

    const categoryRecommendations = this.getCategorySpecificRecommendations(category, score);

    for (let i = 0; i < Math.min(categoryRecommendations.length, maxRecommendations); i++) {
      recommendations.push({
        id: `${project.projectId}-${category}-${i}`,
        bookTitle: project.projectTitle,
        category,
        severity,
        issue: categoryRecommendations[i].issue,
        recommendation: categoryRecommendations[i].recommendation,
        currentScore: Math.round(score),
        targetScore: Math.min(100, Math.round(score + 15))
      });
    }

    return recommendations;
  }

  private getSeverity(score: number): 'critical' | 'high' | 'medium' | 'low' {
    if (score < 50) return 'critical';
    if (score < 65) return 'high';
    if (score < 75) return 'medium';
    return 'low';
  }

  private getCategorySpecificRecommendations(
    category: string,
    score: number
  ): Array<{ issue: string; recommendation: string }> {
    switch (category) {
      case 'readability':
        return [
          {
            issue: 'Complex sentence structures making content hard to follow',
            recommendation: 'Break long sentences into shorter ones. Aim for 15-20 words per sentence on average.'
          },
          {
            issue: 'Overuse of technical or complex vocabulary',
            recommendation: 'Replace complex words with simpler alternatives where possible without losing meaning.'
          },
          {
            issue: 'Dense paragraphs overwhelming readers',
            recommendation: 'Break up long paragraphs. Each paragraph should focus on one main idea.'
          },
          {
            issue: 'Passive voice reducing clarity',
            recommendation: 'Use active voice more frequently to make your writing more direct and engaging.'
          }
        ];

      case 'consistency':
        return [
          {
            issue: 'Character behavior inconsistencies across chapters',
            recommendation: 'Create detailed character sheets and refer to them regularly to maintain consistent personalities.'
          },
          {
            issue: 'Timeline or plot continuity issues',
            recommendation: 'Develop a comprehensive timeline and plot outline to track events and maintain logical flow.'
          },
          {
            issue: 'Inconsistent tone or writing style',
            recommendation: 'Establish a style guide for your narrative voice and review chapters for tone consistency.'
          },
          {
            issue: 'World-building rule violations or contradictions',
            recommendation: 'Create a world-building bible documenting all rules, settings, and logic systems.'
          }
        ];

      case 'engagement':
        return [
          {
            issue: 'Weak chapter openings failing to hook readers',
            recommendation: 'Start each chapter with action, dialogue, or compelling questions to immediately engage readers.'
          },
          {
            issue: 'Insufficient conflict or tension throughout the story',
            recommendation: 'Increase stakes and add obstacles for characters. Every scene should have some form of conflict.'
          },
          {
            issue: 'Lack of sensory details making scenes feel flat',
            recommendation: 'Incorporate more sensory descriptions (sight, sound, smell, touch, taste) to immerse readers.'
          },
          {
            issue: 'Characters\' emotions told rather than shown',
            recommendation: 'Show character emotions through actions, dialogue, and physical reactions rather than stating them.'
          }
        ];

      case 'dialogue':
        return [
          {
            issue: 'All characters sound the same in dialogue',
            recommendation: 'Develop unique speech patterns, vocabulary, and mannerisms for each character.'
          },
          {
            issue: 'Dialogue feels unnatural or stilted',
            recommendation: 'Read dialogue aloud to ensure it sounds natural. Use contractions and interruptions.'
          },
          {
            issue: 'Too much exposition delivered through dialogue',
            recommendation: 'Balance dialogue with action and narrative. Avoid info-dumping through character speech.'
          },
          {
            issue: 'Missing dialogue tags causing confusion',
            recommendation: 'Use clear dialogue tags and action beats to help readers follow conversations.'
          }
        ];

      default:
        return [
          {
            issue: 'General quality improvement needed',
            recommendation: 'Review and revise content to improve overall writing quality.'
          }
        ];
    }
  }

  /**
   * Update daily analytics after session creation
   */
  async updateDailyAnalytics(
    userId: string,
    data: {
      wordCount: number;
      duration?: number;
      date?: string;
    }
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const today = data.date || format(new Date(), 'yyyy-MM-dd');

      // Check for existing analytics
      const { data: existingAnalytics } = await supabase
        .from('user_analytics')
        .select('*')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      if (existingAnalytics) {
        // Update existing record
        const { data: updated, error } = await supabase
          .from('user_analytics')
          .update({
            total_words: existingAnalytics.total_words + data.wordCount,
            total_sessions: existingAnalytics.total_sessions + 1,
            total_time: existingAnalytics.total_time + (data.duration || 0),
            updated_at: new Date().toISOString()
          })
          .eq('id', existingAnalytics.id)
          .select()
          .single();

        if (error) {
          throw new Error(`Failed to update analytics: ${error.message}`);
        }

        return updated;
      } else {
        // Create new record
        const { data: created, error } = await supabase
          .from('user_analytics')
          .insert({
            user_id: userId,
            date: today,
            total_words: data.wordCount,
            total_sessions: 1,
            total_time: data.duration || 0
          })
          .select()
          .single();

        if (error) {
          throw new Error(`Failed to create analytics: ${error.message}`);
        }

        return created;
      }
    });
  }

  /**
   * Track selection analytics event
   */
  async trackSelectionEvent(
    userId: string,
    data: {
      projectId?: string;
      selectionProfileId?: string;
      eventType: string;
      selectionData?: any;
      outcomeData?: any;
    }
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const { projectId, selectionProfileId, eventType, selectionData, outcomeData } = data;

      // Validate event type
      const validEventTypes = [
        'profile_used',
        'project_created',
        'project_completed',
        'project_abandoned',
        'selection_modified',
        'writing_started',
        'chapter_completed',
        'export_generated'
      ];

      if (!validEventTypes.includes(eventType)) {
        return {
          success: false,
          error: 'Invalid event type'
        };
      }

      const analyticsData = {
        user_id: userId,
        project_id: projectId || null,
        selection_profile_id: selectionProfileId || null,
        event_type: eventType,
        selection_data: selectionData || {},
        outcome_data: outcomeData || {},
      };

      const { data: analyticsEntry, error } = await supabase
        .from('selection_analytics')
        .insert(analyticsData)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to track selection event: ${error.message}`);
      }

      return {
        id: analyticsEntry.id,
        userId: analyticsEntry.user_id,
        projectId: analyticsEntry.project_id,
        selectionProfileId: analyticsEntry.selection_profile_id,
        eventType: analyticsEntry.event_type,
        selectionData: analyticsEntry.selection_data,
        outcomeData: analyticsEntry.outcome_data,
        createdAt: new Date(analyticsEntry.created_at),
      };
    });
  }

  /**
   * Get selection analytics
   */
  async getSelectionAnalytics(
    userId: string,
    options: {
      projectId?: string;
      eventType?: string;
      profileId?: string;
      days?: number;
    } = {}
  ): Promise<ServiceResponse<any[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const { projectId, eventType, profileId, days = 30 } = options;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      let query = supabase
        .from('selection_analytics')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });

      if (projectId) {
        query = query.eq('project_id', projectId);
      }
      if (eventType) {
        query = query.eq('event_type', eventType);
      }
      if (profileId) {
        query = query.eq('selection_profile_id', profileId);
      }

      const { data: analytics, error } = await query;

      if (error) {
        throw new Error(`Failed to get selection analytics: ${error.message}`);
      }

      // Transform data
      const formattedAnalytics = analytics?.map(entry => ({
        id: entry.id,
        userId: entry.user_id,
        projectId: entry.project_id,
        selectionProfileId: entry.selection_profile_id,
        eventType: entry.event_type,
        selectionData: entry.selection_data,
        outcomeData: entry.outcome_data,
        createdAt: new Date(entry.created_at),
      })) || [];

      return formattedAnalytics;
    });
  }

  /**
   * Get selection success patterns
   */
  async getSelectionSuccessPatterns(
    userId: string,
    options: {
      timeframe?: 'week' | 'month' | 'quarter' | 'year';
      genre?: string;
    } = {}
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();
      const { timeframe = 'month', genre } = options;

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeframe) {
        case 'week':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(endDate.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      // Get completed projects with their selection data
      let completedProjectsQuery = supabase
        .from('selection_analytics')
        .select(`
          *,
          projects!inner(
            id,
            title,
            genre,
            status,
            word_count,
            target_word_count,
            created_at,
            completed_at
          )
        `)
        .eq('user_id', userId)
        .eq('event_type', 'project_completed')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (genre) {
        completedProjectsQuery = completedProjectsQuery.eq('projects.genre', genre);
      }

      const { data: completedProjects, error: completedError } = await completedProjectsQuery;

      if (completedError) {
        throw new Error(`Failed to get completed projects: ${completedError.message}`);
      }

      // Get abandoned projects for comparison
      let abandonedProjectsQuery = supabase
        .from('selection_analytics')
        .select(`
          *,
          projects!inner(
            id,
            title,
            genre,
            status,
            word_count,
            target_word_count,
            created_at
          )
        `)
        .eq('user_id', userId)
        .eq('event_type', 'project_abandoned')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (genre) {
        abandonedProjectsQuery = abandonedProjectsQuery.eq('projects.genre', genre);
      }

      const { data: abandonedProjects, error: abandonedError } = await abandonedProjectsQuery;

      if (abandonedError) {
        throw new Error(`Failed to get abandoned projects: ${abandonedError.message}`);
      }

      // Calculate success patterns
      const patterns = this.calculateSuccessPatterns(completedProjects || [], abandonedProjects || []);

      // Get top performing selection profiles
      const topProfiles = await this.getTopPerformingProfiles(userId, timeframe, genre);

      // Calculate metrics
      const totalCompleted = completedProjects?.length || 0;
      const totalAbandoned = abandonedProjects?.length || 0;
      const successRate = totalCompleted + totalAbandoned > 0 
        ? (totalCompleted / (totalCompleted + totalAbandoned)) * 100 
        : 0;

      return {
        successRate: Math.round(successRate * 100) / 100,
        totalProjects: totalCompleted + totalAbandoned,
        completedProjects: totalCompleted,
        abandonedProjects: totalAbandoned,
        timeframe,
        patterns,
        topProfiles,
        recommendations: this.generateSuccessRecommendations(patterns)
      };
    });
  }

  /**
   * Calculate success patterns from project analytics
   */
  private calculateSuccessPatterns(completed: any[], abandoned: any[]): any {
    const patterns = {
      genreSuccess: {} as Record<string, any>,
      structureSuccess: {} as Record<string, any>,
      paceSuccess: {} as Record<string, any>,
      targetWordCountSuccess: {} as Record<string, any>,
      povSuccess: {} as Record<string, any>,
      toneSuccess: {} as Record<string, any>
    };

    // Process completed projects
    completed.forEach(project => {
      const selections = project.selection_data;
      if (!selections) return;

      this.incrementPattern(patterns.genreSuccess, selections.genre || 'unknown', 'completed');
      this.incrementPattern(patterns.structureSuccess, selections.structureType || 'unknown', 'completed');
      this.incrementPattern(patterns.paceSuccess, selections.pacingPreference || 'unknown', 'completed');
      this.incrementPattern(patterns.povSuccess, selections.narrativeVoice || 'unknown', 'completed');
      
      if (selections.toneOptions && Array.isArray(selections.toneOptions)) {
        selections.toneOptions.forEach((tone: string) => {
          this.incrementPattern(patterns.toneSuccess, tone, 'completed');
        });
      }

      // Word count ranges
      const wordCountRange = this.getWordCountRange(project.projects?.target_word_count);
      this.incrementPattern(patterns.targetWordCountSuccess, wordCountRange, 'completed');
    });

    // Process abandoned projects
    abandoned.forEach(project => {
      const selections = project.selection_data;
      if (!selections) return;

      this.incrementPattern(patterns.genreSuccess, selections.genre || 'unknown', 'abandoned');
      this.incrementPattern(patterns.structureSuccess, selections.structureType || 'unknown', 'abandoned');
      this.incrementPattern(patterns.paceSuccess, selections.pacingPreference || 'unknown', 'abandoned');
      this.incrementPattern(patterns.povSuccess, selections.narrativeVoice || 'unknown', 'abandoned');
      
      if (selections.toneOptions && Array.isArray(selections.toneOptions)) {
        selections.toneOptions.forEach((tone: string) => {
          this.incrementPattern(patterns.toneSuccess, tone, 'abandoned');
        });
      }

      const wordCountRange = this.getWordCountRange(project.projects?.target_word_count);
      this.incrementPattern(patterns.targetWordCountSuccess, wordCountRange, 'abandoned');
    });

    // Calculate success rates for each pattern
    Object.keys(patterns).forEach(patternType => {
      const pattern = patterns[patternType as keyof typeof patterns];
      Object.keys(pattern).forEach(key => {
        const data = pattern[key];
        if (data) {
          const total = data.completed + data.abandoned;
          data.successRate = total > 0 ? Math.round((data.completed / total) * 10000) / 100 : 0;
          data.total = total;
        }
      });
    });

    return patterns;
  }

  /**
   * Increment pattern counter
   */
  private incrementPattern(pattern: Record<string, any>, key: string, type: 'completed' | 'abandoned'): void {
    if (!key) return;
    
    if (!pattern[key]) {
      pattern[key] = { completed: 0, abandoned: 0 };
    }
    pattern[key][type]++;
  }

  /**
   * Get top performing selection profiles
   */
  private async getTopPerformingProfiles(
    userId: string,
    _timeframe: string,
    genre?: string
  ): Promise<any[]> {
    try {
      const supabase = await createTypedServerClient();
      
      let query = supabase
        .from('selection_analytics')
        .select(`
          selection_profile_id,
          selection_profiles!inner(id, name, description, category, usage_count),
          event_type
        `)
        .eq('user_id', userId)
        .in('event_type', ['project_completed', 'project_abandoned'])
        .not('selection_profile_id', 'is', null);

      if (genre) {
        query = query.eq('selection_profiles.category', genre);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to get top profiles: ${error.message}`);
      }

      // Group by profile and calculate success rates
      const profileStats: Record<string, any> = {};
      
      data?.forEach(entry => {
        const profileId = entry.selection_profile_id;
        if (!profileStats[profileId]) {
          const profileData = Array.isArray(entry.selection_profiles) && entry.selection_profiles.length > 0
            ? entry.selection_profiles[0] 
            : Array.isArray(entry.selection_profiles) 
              ? null
              : entry.selection_profiles;
          
          profileStats[profileId] = {
            profile: {
              name: profileData?.name || 'Unknown Profile',
              description: profileData?.description || '',
              category: profileData?.category || '',
              usage_count: profileData?.usage_count || 0
            },
            completed: 0,
            abandoned: 0
          };
        }
        profileStats[profileId][entry.event_type === 'project_completed' ? 'completed' : 'abandoned']++;
      });

      // Calculate success rates and sort
      const profiles = Object.values(profileStats)
        .map(stats => ({
          profile: stats.profile,
          completed: stats.completed,
          abandoned: stats.abandoned,
          total: stats.completed + stats.abandoned,
          successRate: stats.completed + stats.abandoned > 0 
            ? Math.round((stats.completed / (stats.completed + stats.abandoned)) * 10000) / 100 
            : 0
        }))
        .filter(p => p.total >= 3) // Minimum projects for statistical relevance
        .sort((a, b) => b.successRate - a.successRate)
        .slice(0, 10);

      return profiles;
    } catch (error) {
      this.logger.error('Error getting top profiles:', error);
      return [];
    }
  }

  /**
   * Generate recommendations based on success patterns
   */
  private generateSuccessRecommendations(patterns: any): any[] {
    const recommendations: any[] = [];

    // Find top performing patterns
    Object.keys(patterns).forEach(patternType => {
      const pattern = patterns[patternType];
      const sorted = Object.entries(pattern)
        .filter(([, data]: [string, any]) => data.total && data.total >= 5)
        .sort(([, a]: [string, any], [, b]: [string, any]) => (b.successRate || 0) - (a.successRate || 0));

      if (sorted.length > 0) {
        const [bestKey, bestData] = sorted[0] as [string, any];
        if (bestData.successRate && bestData.successRate > 70) {
          recommendations.push({
            type: patternType,
            recommendation: bestKey,
            successRate: bestData.successRate,
            sampleSize: bestData.total,
            message: `Projects with ${this.formatPatternType(patternType)} "${bestKey}" have a ${bestData.successRate}% completion rate`
          });
        }
      }
    });

    return recommendations.slice(0, 5); // Top 5 recommendations
  }

  /**
   * Format pattern type for display
   */
  private formatPatternType(type: string): string {
    const formatMap: Record<string, string> = {
      genreSuccess: 'genre',
      structureSuccess: 'story structure',
      paceSuccess: 'pacing preference',
      targetWordCountSuccess: 'target word count',
      povSuccess: 'point of view',
      toneSuccess: 'tone'
    };
    return formatMap[type] || type;
  }

  /**
   * Log an editing session
   */
  async logEditingSession(data: {
    userId: string;
    selectedText: string;
    aiPrompt: string;
    aiResponse: string;
    actionType: string;
  }): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data: editingSession, error } = await supabase
        .from('editing_sessions')
        .insert({
          user_id: data.userId,
          selected_text: data.selectedText,
          ai_prompt: data.aiPrompt,
          ai_response: data.aiResponse,
          action_type: data.actionType,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        logger.error('[WritingAnalyticsService] Error logging editing session:', error);
        throw error;
      }

      return editingSession;
    });
  }

  /**
   * Get user goals with optional progress data
   */
  async getUserGoals(
    userId: string,
    options: {
      projectId?: string;
      status?: string;
      includeProgress?: boolean;
      limit?: number;
    } = {}
  ): Promise<ServiceResponse<any[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { projectId, status = 'active', includeProgress = false, limit } = options;

      let query = supabase
        .from('writing_goals')
        .select('*')
        .eq('user_id', userId)
        .eq('status', status)
        .order('created_at', { ascending: false });

      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      if (limit) {
        query = query.limit(limit);
      }

      const { data: goals, error } = await query;

      if (error) {
        logger.error('[WritingAnalyticsService] Error fetching goals:', error);
        throw error;
      }

      if (!includeProgress) {
        return goals || [];
      }

      // Fetch progress for each goal
      const goalsWithProgress = await Promise.all(
        (goals || []).map(async (goal) => {
          const { data: progress } = await supabase
            .from('writing_goal_progress')
            .select('*')
            .eq('goal_id', goal.id)
            .order('progress_date', { ascending: false })
            .limit(7);

          return {
            ...goal,
            progress: progress || []
          };
        })
      );

      return goalsWithProgress;
    });
  }

  /**
   * Create a writing goal
   */
  async createWritingGoal(
    userId: string,
    goalData: {
      goal_type: string;
      title: string;
      description?: string;
      target_value: number;
      unit: string;
      deadline?: string;
      difficulty?: string;
      is_recommended?: boolean;
      project_id?: string;
      metadata?: Record<string, unknown>;
    }
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data: goal, error } = await supabase
        .from('writing_goals')
        .insert({
          ...goalData,
          user_id: userId,
          status: 'active',
          current_value: 0,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        logger.error('[WritingAnalyticsService] Error creating goal:', error);
        throw error;
      }

      return goal;
    });
  }

  /**
   * Create multiple writing goals
   */
  async createMultipleWritingGoals(
    userId: string,
    goalsData: Array<{
      goal_type: string;
      title: string;
      description?: string;
      target_value: number;
      unit: string;
      deadline?: string;
      difficulty?: string;
      is_recommended?: boolean;
      project_id?: string;
      metadata?: Record<string, unknown>;
    }>
  ): Promise<ServiceResponse<any[]>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const goalsToInsert = goalsData.map(goal => ({
        ...goal,
        user_id: userId,
        status: 'active',
        current_value: 0,
        created_at: new Date().toISOString()
      }));

      const { data: goals, error } = await supabase
        .from('writing_goals')
        .insert(goalsToInsert)
        .select();

      if (error) {
        logger.error('[WritingAnalyticsService] Error creating multiple goals:', error);
        throw error;
      }

      return goals || [];
    });
  }

  /**
   * Update a writing goal
   */
  async updateWritingGoal(
    userId: string,
    goalId: string,
    updates: Record<string, unknown>
  ): Promise<ServiceResponse<any>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { data: goal, error } = await supabase
        .from('writing_goals')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', goalId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        logger.error('[WritingAnalyticsService] Error updating goal:', error);
        throw error;
      }

      return goal;
    });
  }

  /**
   * Delete a writing goal
   */
  async deleteWritingGoal(
    userId: string,
    goalId: string
  ): Promise<ServiceResponse<boolean>> {
    return this.withErrorHandling(async () => {
      const supabase = await createTypedServerClient();

      const { error } = await supabase
        .from('writing_goals')
        .delete()
        .eq('id', goalId)
        .eq('user_id', userId);

      if (error) {
        logger.error('[WritingAnalyticsService] Error deleting goal:', error);
        throw error;
      }

      return true;
    });
  }
}