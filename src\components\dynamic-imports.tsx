// Dynamic import wrappers for heavy components
// These help with code splitting and reduce initial bundle size

import dynamic from 'next/dynamic'
import { Suspense } from 'react'
import { Loader2 } from 'lucide-react'

// Loading component
const LoadingFallback = () => (
  <div className="flex items-center justify-center p-8">
    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
  </div>
)

// Monaco Editor - Load only when needed
export const DynamicMonacoEditor = dynamic(
  () => import('@monaco-editor/react'),
  {
    ssr: false,
    loading: LoadingFallback
  }
)

// Charts - Load only on analytics pages
export const DynamicLineChart = dynamic(
  () => import('recharts').then(mod => mod.LineChart),
  {
    ssr: false,
    loading: LoadingFallback
  }
)

export const DynamicBarChart = dynamic(
  () => import('recharts').then(mod => mod.BarChart),
  {
    ssr: false,
    loading: LoadingFallback
  }
)

export const DynamicAreaChart = dynamic(
  () => import('recharts').then(mod => mod.AreaChart),
  {
    ssr: false,
    loading: LoadingFallback
  }
)

// Export components - Load only when exporting
export const DynamicPDFExport = dynamic(
  () => import('@/components/export/pdf-export'),
  {
    ssr: false,
    loading: LoadingFallback
  }
)

export const DynamicAdvancedExport = dynamic(
  () => import('@/components/export/advanced-export-dialog'),
  {
    ssr: false,
    loading: LoadingFallback
  }
)

// Visualization components - Load only when needed
export const DynamicCharacterArcViz = dynamic(
  () => import('@/components/analysis/character-arc-visualizer'),
  {
    ssr: false,
    loading: LoadingFallback
  }
)

export const DynamicLocationMap = dynamic(
  () => import('@/components/locations/location-map-view'),
  {
    ssr: false,
    loading: LoadingFallback
  }
)

// Collaboration components - Load only in collab mode
export const DynamicCollaborationCanvas = dynamic(
  () => import('@/components/collaboration/collaboration-canvas'),
  {
    ssr: false,
    loading: LoadingFallback
  }
)

// Voice analysis - Load only when using voice features
export const DynamicVoiceAnalyzer = dynamic(
  () => import('@/components/voice/voice-analyzer'),
  {
    ssr: false,
    loading: LoadingFallback
  }
)
