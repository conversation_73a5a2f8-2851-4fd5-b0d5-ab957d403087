import { NextRequest } from 'next/server'
import { UnifiedAuthService } from '@/lib/auth/unified-auth-service'
import { UnifiedResponse } from '@/lib/utils/response'
import { createTypedServerClient } from '@/lib/supabase'
import { z } from 'zod'
import { logger } from '@/lib/services/logger'
import { openai } from '@/lib/ai/openai-client'
import { verifyProjectAccess, PROJECT_ACCESS_ERROR } from '@/lib/db/project-access'

const querySchema = z.object({
  projectId: z.string().uuid(),
  characterId: z.string().uuid()
})

const postSchema = z.object({
  projectId: z.string().uuid(),
  characterId: z.string().uuid()
})

interface CharacterInsight {
  id: string
  type: 'strength' | 'weakness' | 'opportunity' | 'threat' | 'recommendation'
  category: 'arc' | 'consistency' | 'relationships' | 'pacing' | 'voice'
  title: string
  description: string
  severity?: 'low' | 'medium' | 'high'
  examples?: string[]
  actionable?: string
}

// GET - Retrieve cached insights
export const GET = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const searchParams = request.nextUrl.searchParams
    const projectId = searchParams.get('projectId')
    const characterId = searchParams.get('characterId')
    
    const validation = querySchema.safeParse({ projectId, characterId })
    if (!validation.success) {
      return UnifiedResponse.error('Invalid query parameters', 400, validation.error.errors)
    }

    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify access
    const project = await verifyProjectAccess(projectId!, user.id)

    if (!project) {
      return UnifiedResponse.error(PROJECT_ACCESS_ERROR, 404)
    }

    // Get character
    const { data: character } = await supabase
      .from('characters')
      .select('name')
      .eq('id', characterId)
      .eq('project_id', projectId)
      .single()

    if (!character) {
      return UnifiedResponse.error('Character not found', 404)
    }

    // Check for cached insights
    const { data: cached } = await supabase
      .from('character_insights_cache')
      .select('*')
      .eq('character_id', characterId)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // 24h cache
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (cached && cached.insights) {
      return UnifiedResponse.success({
        characterId,
        characterName: character.name,
        insights: cached.insights,
        overallScore: cached.overall_score || 0,
        strengths: cached.strengths_count || 0,
        improvements: cached.improvements_count || 0,
        lastAnalyzed: cached.created_at
      })
    }

    // Return empty state if no cache
    return UnifiedResponse.success({
      characterId,
      characterName: character.name,
      insights: [],
      overallScore: 0,
      strengths: 0,
      improvements: 0,
      lastAnalyzed: new Date().toISOString()
    })
  } catch (error) {
    logger.error('Get character insights error:', error)
    return UnifiedResponse.error('Failed to retrieve insights')
  }
})

// POST - Generate new insights
export const POST = UnifiedAuthService.withAuth(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const validation = postSchema.safeParse(body)
    
    if (!validation.success) {
      return UnifiedResponse.error('Invalid request data', 400, validation.error.errors)
    }

    const { projectId, characterId } = validation.data
    const user = request.user!
    const supabase = await createTypedServerClient()

    // Verify access and get data
    const project = await verifyProjectAccess(projectId, user.id)
    if (!project) {
      return UnifiedResponse.error(PROJECT_ACCESS_ERROR, 404)
    }

    const [characterResult, chaptersResult] = await Promise.all([
      supabase.from('characters').select('*').eq('id', characterId).eq('project_id', projectId).single(),
      supabase.from('chapters').select('id, chapter_number, content').eq('project_id', projectId).order('chapter_number')
    ])

    if (!characterResult.data) {
      return UnifiedResponse.error('Character not found', 404)
    }

    const character = characterResult.data
    const chapters = chaptersResult.data || []

    // Analyze character across all content
    const insights = await analyzeCharacterComprehensively(character, chapters)

    // Calculate summary stats
    const strengths = insights.filter(i => i.type === 'strength').length
    const improvements = insights.filter(i => ['weakness', 'threat'].includes(i.type)).length
    const overallScore = Math.round((strengths / (strengths + improvements)) * 100) || 50

    // Cache the insights
    await supabase.from('character_insights_cache').insert({
      character_id: characterId,
      project_id: projectId,
      insights,
      overall_score: overallScore,
      strengths_count: strengths,
      improvements_count: improvements
    })

    return UnifiedResponse.success({
      characterId,
      characterName: character.name,
      insights,
      overallScore,
      strengths,
      improvements,
      lastAnalyzed: new Date().toISOString()
    })
  } catch (error) {
    logger.error('Generate character insights error:', error)
    return UnifiedResponse.error('Failed to generate insights')
  }
})

async function analyzeCharacterComprehensively(
  character: {
    id: string;
    name: string;
    role?: string;
    personality?: string;
    goals?: string;
    backstory?: string;
    relationships?: string;
    flaws?: string;
    strengths?: string;
  },
  chapters: Array<{ id: string; chapter_number: number; content: string }>
): Promise<CharacterInsight[]> {
  try {
    // Prepare character context
    const characterContext = {
      name: character.name,
      role: character.role,
      personality: character.personality,
      goals: character.goals,
      backstory: character.backstory,
      flaws: character.flaws,
      strengths: character.strengths
    }

    // Sample chapter content (first, middle, last)
    const chapterSamples = []
    if (chapters.length > 0) {
      chapterSamples.push(chapters[0])
      if (chapters.length > 2) {
        chapterSamples.push(chapters[Math.floor(chapters.length / 2)])
        chapterSamples.push(chapters[chapters.length - 1])
      }
    }

    const prompt = `Analyze this character comprehensively and provide actionable insights.

Character Profile:
${JSON.stringify(characterContext, null, 2)}

Chapter Samples:
${chapterSamples.map(ch => `Chapter ${ch.chapter_number}: ${ch.content?.substring(0, 1000) || ''}`).join('\n\n')}

Provide insights in these categories:
1. arc - Character growth and transformation
2. consistency - Voice and behavior consistency
3. relationships - Interpersonal dynamics
4. pacing - Development speed
5. voice - Dialogue and narrative voice

For each insight:
- type: strength, weakness, opportunity, threat, or recommendation
- category: one of the above
- title: Brief title (max 100 chars)
- description: Detailed explanation (max 300 chars)
- severity: low, medium, or high (only for weaknesses/threats)
- examples: 1-3 specific examples from the text
- actionable: Specific suggestion for improvement (if applicable)

Focus on actionable, specific insights that will help improve the character.`

    const response = await openai.chat.completions.create({
      model: 'gpt-4-turbo-preview',
      messages: [
        {
          role: 'system',
          content: 'You are an expert literary analyst and character development specialist. Provide specific, actionable insights.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      response_format: { type: 'json_object' },
      max_tokens: 2000
    })

    const result = JSON.parse(response.choices[0].message.content || '{}')
    
    if (!result.insights || !Array.isArray(result.insights)) {
      return generateFallbackInsights(character)
    }

    // Process and validate insights
    const insights: CharacterInsight[] = result.insights.map((insight, idx) => ({
      id: `insight_${idx}_${Date.now()}`,
      type: insight.type || 'recommendation',
      category: insight.category || 'arc',
      title: (insight.title || '').substring(0, 100),
      description: (insight.description || '').substring(0, 300),
      severity: insight.severity,
      examples: Array.isArray(insight.examples) ? insight.examples.slice(0, 3) : [],
      actionable: insight.actionable
    }))

    return insights.slice(0, 12) // Limit to 12 insights
  } catch (error) {
    logger.error('AI analysis failed:', error)
    return generateFallbackInsights(character)
  }
}

function generateFallbackInsights(character: {
  id: string;
  name: string;
  goals?: string;
  personality?: string;
  backstory?: string;
}): CharacterInsight[] {
  const insights: CharacterInsight[] = []

  if (character.goals) {
    insights.push({
      id: 'fallback_1',
      type: 'strength',
      category: 'arc',
      title: 'Clear Character Goals',
      description: 'The character has well-defined goals that can drive the narrative forward.',
      examples: [character.goals]
    })
  }

  if (character.flaws) {
    insights.push({
      id: 'fallback_2',
      type: 'opportunity',
      category: 'arc',
      title: 'Character Flaws for Growth',
      description: 'The character\'s flaws provide opportunities for meaningful development.',
      examples: [character.flaws],
      actionable: 'Show how these flaws create conflict and drive character change.'
    })
  }

  insights.push({
    id: 'fallback_3',
    type: 'recommendation',
    category: 'consistency',
    title: 'Maintain Voice Consistency',
    description: 'Ensure the character\'s dialogue and actions align with their established personality.',
    actionable: 'Create a character voice guide with key phrases and speech patterns.'
  })

  return insights
}